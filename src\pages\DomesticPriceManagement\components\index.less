@bg-color: #f9fafb;
@card-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
@primary-text: #1f2937;
@secondary-text: #4b5563;
@border-color: #e5e7eb;
@rounded-button: 6px;
@transition-time: 0.3s;
@section-border-radius: 8px;
.domestic-price-modal {
  .ant-modal-body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 24px 24px 12px;
  }

  .form-row {
    margin-bottom: 0;
  }

  .form-item-with-unit {
    .ant-form-item-control-input-content {
      display: flex;
      align-items: center;

      .unit-text {
        margin-left: 8px;
        color: #666;
      }
    }
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .price-section {
    margin-bottom: 8px;

    .section-title {
      font-weight: 500;
      color: #1797e1;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px dashed rgba(23, 151, 225, 0.2);
    }
  }
}

.domestic-price-detail-modal {
  .ant-modal-content {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.06);
    .ant-modal-close {
      top: 30px;
      inset-inline-end: 30px;
    }
  }

  .ant-modal-header {
    padding: 16px 24px;
    background: linear-gradient(to right, #f0f7ff, #f9fcff);
    border-bottom: 1px solid #e6f0fa;
    .ant-tag {
      margin-left: 10px;
    }
  }

  .ant-modal-body {
    padding: 10px;
    max-height: 70vh;
    overflow-y: auto;
    background-color: #f9fafc;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }

  .price-detail-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;

    .title-icon {
      margin-right: 8px;
      color: #1890ff;
      font-size: 18px;
    }

    .price-id-tag {
      margin-left: 12px;
      font-weight: normal;
      font-size: 12px;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }

  .price-detail-content {
    .detail-card {
      margin-bottom: 16px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      background-color: #fff;
      border: 1px solid #e6e8eb;
      overflow: hidden;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }

      .ant-card-body {
        padding: 16px;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      align-items: center;

      .anticon {
        margin-right: 8px;
        color: #1890ff;
      }
    }

    .info-item {
      margin-bottom: 8px;

      .ant-typography {
        display: block;
        margin-bottom: 4px;
        font-size: 13px;
      }

      .info-value {
        font-size: 14px;
        color: #1f2937;
        font-weight: 500;
        min-height: 22px;
        word-break: break-word;
      }
    }

    .json-list {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 4px;
        font-size: 13px;
      }
    }

    .price-tier {
      background-color: #f9fafb;
      border-radius: 6px;
      padding: 12px;
      border: 1px solid #eaedf0;
      height: 100%;

      .tier-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px dashed #e5e7eb;

        .ant-badge {
          font-weight: 500;
        }
      }

      .tier-content {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .tier-item {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .ant-typography {
          margin-bottom: 0;
          font-size: 13px;
        }

        .tier-value {
          font-weight: 500;
          color: #1f2937;
        }
      }
    }
  }
}
