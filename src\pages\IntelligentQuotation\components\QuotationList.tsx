import React from "react";
import { Typography, Empty } from "antd";
import { DollarOutlined } from "@ant-design/icons";
import { Canvas } from "@react-three/fiber";
import { OrbitControls } from "@react-three/drei";
import CommandCenterBackground from "@/components/ThreeScene/CommandCenterBackground";
import QuotationCard from "./QuotationCard";

const { Title, Text } = Typography;

interface QuotationListProps {
  savedQuotations: any[];
  handleQuotationSelect: (quotation: any) => void;
}

const QuotationList: React.FC<QuotationListProps> = ({
  savedQuotations,
  handleQuotationSelect,
}) => {
  return (
    <div className="ai-quotation-list-container">
      {/* 添加3D背景 */}
      <div className="canvas-container">
        <Canvas
          camera={{ position: [0, 0, 10], fov: 40 }}
          style={{ width: "100%", height: "100%" }}
          gl={{ antialias: true, alpha: true }}
        >
          <color attach="background" args={["#f0f7ff"]} />
          <fog attach="fog" args={["#f0f7ff", 15, 40]} />
          <CommandCenterBackground isHolographic={false} />

          <OrbitControls
            enableZoom={false}
            enablePan={false}
            enableRotate={true}
            autoRotate={true}
            autoRotateSpeed={0.5}
          />
        </Canvas>
      </div>

      <div className="quotation-list-header">
        <div className="panel-title">
          <DollarOutlined className="panel-icon" />
          <Title level={4}>我的已保存报价</Title>
        </div>
        <Text className="panel-subtitle">比较和管理您已保存的运输报价</Text>
      </div>

      <div className="quotation-list-content">
        {savedQuotations.length > 0 ? (
          savedQuotations.map((item) => (
            <QuotationCard
              key={item.id}
              item={item}
              handleQuotationSelect={handleQuotationSelect}
              showActions={true}
            />
          ))
        ) : (
          <div className="empty-container">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              className="custom-empty"
              description={
                <span className="empty-text">
                  暂无保存的报价。开始与AI对话以获取报价。
                </span>
              }
            />
          </div>
        )}
      </div>

      {/* 注释掉的操作按钮区域，保持与原代码一致 */}
      {/* <div className="quotation-list-actions">
        <div className="action-buttons">
          <Button type="default" icon={<FilterOutlined />}>
            筛选
          </Button>
          <Button type="default" icon={<SortAscendingOutlined />}>
            排序
          </Button>
        </div>
      </div> */}
    </div>
  );
};

export default QuotationList;
