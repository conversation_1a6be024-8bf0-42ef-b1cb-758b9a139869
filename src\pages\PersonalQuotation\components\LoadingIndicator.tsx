import React from "react";
import { Spin, Progress } from "antd";

interface LoadingIndicatorProps {
  progress: number;
}

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({ progress }) => {
  return (
    <div className="loading-container">
      <div className="hud-loading"></div>
      <Spin
        size="large"
        tip={
          <div style={{ marginTop: 20 }}>
            <Progress
              percent={progress}
              status="active"
              strokeColor={{
                "0%": "#004CFF",
                "100%": "#00CFFF",
              }}
              style={{ maxWidth: 300 }}
            />
          </div>
        }
      />
    </div>
  );
};

export default LoadingIndicator;
