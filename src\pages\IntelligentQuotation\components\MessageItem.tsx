import React from "react";
import {
  Typo<PERSON>,
  Avatar,
  Spin,
  Button,
  List,
  Card,
  Badge,
  Tag,
  Row,
  Col,
  Divider,
  Space,
} from "antd";
import {
  RobotOutlined,
  UserOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  CarOutlined,
  InboxOutlined,
  SafetyOutlined,
  CalendarOutlined,
  DollarOutlined,
} from "@ant-design/icons";
import { Message } from "../types";
import AIPriceCard from "./AIPriceCard";

const { Text, Paragraph } = Typography;

interface MessageItemProps {
  message: Message;
  handleSuggestionClick: (suggestion: string) => void;
  onCreateInquiry?: () => void; // 新增询价回调
  onQueryQuotation?: () => void; // 查询报价回调
}

const MessageItem: React.FC<MessageItemProps> = ({
  message,
  handleSuggestionClick,
  onCreateInquiry,
  onQueryQuotation,
}) => {
  // 格式化时间
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  // 解析消息中的<>内容
  const parseStructuredInfo = (content: string) => {
    // 检查是否包含<>格式的内容
    const match = content.match(/<([^>]+)>/);
    if (!match) return null;

    const infoString = match[1];
    // 检查内容是否为空或只包含空白字符
    if (!infoString || !infoString.trim()) return null;

    const infoItems = infoString.split("，");
    const parsedInfo: { [key: string]: string } = {};

    infoItems.forEach((item) => {
      const [key, value] = item.split("：");
      if (key && value !== undefined) {
        const trimmedKey = key.trim();
        const trimmedValue = value.trim();
        // 只有当key和value都不为空时才添加
        if (trimmedKey && trimmedValue) {
          parsedInfo[trimmedKey] = trimmedValue;
        }
      }
    });

    // 只有当解析出有效的键值对时才返回结果
    return Object.keys(parsedInfo).length > 0 ? parsedInfo : null;
  };

  // 获取字段对应的图标
  const getFieldIcon = (fieldName: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      提货城市: <EnvironmentOutlined style={{ color: "#1890ff" }} />,
      经由城市: <EnvironmentOutlined style={{ color: "#52c41a" }} />,
      目的城市: <EnvironmentOutlined style={{ color: "#52c41a" }} />,
      货物总毛重: <InboxOutlined style={{ color: "#fa8c16" }} />,
      货物总体积: <InboxOutlined style={{ color: "#fa8c16" }} />,
      货物尺寸: <InboxOutlined style={{ color: "#fa8c16" }} />,
      包装形式: <SafetyOutlined style={{ color: "#722ed1" }} />,
      特殊货物: <SafetyOutlined style={{ color: "#eb2f96" }} />,
      要求ETD: <CalendarOutlined style={{ color: "#13c2c2" }} />,
      保舱: <DollarOutlined style={{ color: "#f5222d" }} />,
    };

    return (
      iconMap[fieldName] || <InfoCircleOutlined style={{ color: "#666" }} />
    );
  };

  // 获取字段值的状态颜色
  const getFieldValueColor = (value: string) => {
    if (value === "暂无" || value === "无") {
      return "#ff4d4f"; // 红色表示缺失
    }
    return "#52c41a"; // 绿色表示已有
  };

  // 渲染紧凑的结构化信息
  const renderCompactStructuredInfo = (parsedInfo: {
    [key: string]: string;
  }) => {
    return (
      <div className="compact-structured-info">
        <div className="info-header">
          <CheckCircleOutlined style={{ color: "#52c41a", fontSize: "12px" }} />
          <Text
            type="secondary"
            style={{ fontSize: "12px", marginLeft: "4px" }}
          >
            信息收集
          </Text>
        </div>
        <div className="info-grid">
          {Object.entries(parsedInfo).map(([key, value]) => (
            <div className="info-item-compact" key={key}>
              {getFieldIcon(key)}
              <Text
                style={{ fontSize: "11px", color: "#666", marginLeft: "4px" }}
              >
                {key}:
              </Text>
              <Text
                style={{
                  color: getFieldValueColor(value),
                  fontSize: "11px",
                  fontWeight: 500,
                  marginLeft: "2px",
                }}
              >
                {value}
              </Text>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 处理消息内容，分离普通文本和结构化信息
  const processMessageContent = (content: string) => {
    const parsedInfo = parseStructuredInfo(content);

    if (parsedInfo && Object.keys(parsedInfo).length > 0) {
      // 移除原始的<>内容，保留其他文本
      const cleanContent = content.replace(/<[^>]+>/, "").trim();
      return {
        hasStructuredInfo: true,
        cleanContent,
        structuredInfo: parsedInfo,
      };
    }
    return {
      hasStructuredInfo: false,
      cleanContent: content,
      structuredInfo: null,
    };
  };

  return (
    <>
      {/* 新对话轮次分隔线 */}
      {message.isNewRound && (
        <div className="conversation-divider">
          <Divider>
            <div className="divider-content">
              <ReloadOutlined className="divider-icon" />
              <Text type="secondary">开始新的询价对话</Text>
            </div>
          </Divider>
        </div>
      )}

      <div
        key={message.id}
        className={`message-container ${message.sender === "ai" ? "ai-message" : "user-message"}`}
      >
        <div className="message-avatar">
          {message.sender === "ai" ? (
            <Avatar icon={<RobotOutlined />} className="ai-avatar" />
          ) : (
            <Avatar icon={<UserOutlined />} className="user-avatar" />
          )}
        </div>
        <div className="message-content">
          {message.isLoading ? (
            <div className="typing-indicator">
              <div className="dot"></div>
              <div className="dot"></div>
              <div className="dot"></div>
            </div>
          ) : (
            <>
              {(() => {
                const contentData = processMessageContent(message.content);
                return (
                  <div
                    key={message.id}
                    className={`message-bubble ${message.sender}`}
                  >
                    {/* 如果有结构化信息且不是欢迎消息，在消息内部紧凑展示 */}
                    {contentData.hasStructuredInfo &&
                      contentData.structuredInfo &&
                      !message.id.includes("welcome") &&
                      renderCompactStructuredInfo(contentData.structuredInfo)}

                    <div>{contentData.cleanContent}</div>
                    <div className="message-time">
                      {formatTime(message.timestamp)}
                    </div>
                  </div>
                );
              })()}

              {message.suggestions && message.suggestions.length > 0 && (
                <div className="message-suggestions">
                  {message.suggestions.map((suggestion, index) => (
                    <Button
                      key={index}
                      type="default"
                      className="suggestion-button"
                      onClick={() => handleSuggestionClick(suggestion)}
                    >
                      {suggestion}
                    </Button>
                  ))}
                </div>
              )}

              {/* 信息收集完成后的操作按钮 */}
              {message.isInfoCollectionComplete && (
                <div className="info-collection-actions">
                  <Space size="middle">
                    <Button
                      type="primary"
                      onClick={onCreateInquiry}
                      className="action-button create-inquiry-button"
                    >
                      新增询价
                    </Button>
                    <Button
                      type="default"
                      onClick={onQueryQuotation}
                      className="action-button query-quotation-button"
                    >
                      查询报价
                    </Button>
                  </Space>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default MessageItem;
