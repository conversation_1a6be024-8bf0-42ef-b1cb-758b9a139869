import React, { useEffect } from "react";
import {
  Form,
  Input,
  Button,
  message,
  Card,
  Space,
  DatePicker,
  InputNumber,
  Switch,
  Divider,
  Row,
  Col,
  Select,
} from "antd";
import "./index.less";
import {
  LeftOutlined,
  MinusCircleOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { addInternationalPrice, updateInternationalPrice } from "../services";
import { useAppSelector } from "@/store/hooks";
import { FormInstance } from "antd/es/form";
import { useWatch } from "antd/es/form/Form";
import useBaseData from "@/hooks/useBaseData";
import { useTranslation } from "react-i18next";
import { formatDateToTimestamp } from "@/utils/util";
import CabinReportForm from "./CabinReportForm";
import { use } from "i18next";

interface AddDrawerType {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  method: string;
  form: FormInstance;
  fetchData: (
    userId?: number | null,
    params?: {
      pageIndex?: number;
      pageSize?: number;
      [key: string]: any;
    }
  ) => Promise<void>;
  selectedUserId?: number | null;
  currentPagination?: {
    pageindex: number;
    pagesize: number;
    total: number;
  };
  currentFilters?: any;
  onResetFilters?: () => void;
}

const AddSupplyPrice: React.FC<AddDrawerType> = (props) => {
  const {
    setOpen,
    method,
    form,
    fetchData,
    selectedUserId,
    currentPagination,
    currentFilters,
    onResetFilters,
  } = props;
  const { user } = useAppSelector((state) => state.user);
  const isTransfer = useWatch("istransfer", form);
  const originSchedules = useWatch("originschedules", form);
  const isCabin = useWatch("iscabin", form);
  const isQ45Sub = useWatch("q45_sub", form);
  const isQ100Sub = useWatch("q100_sub", form);
  const { t } = useTranslation();
  const getSupplierIdForUpdate = (
    supplierName: string,
    originalSupplierid?: number | null
  ): number | null => {
    // 当用户角色为4时，优先使用原始记录的supplierid
    if (user?.useridentity === 4 && method !== "add") {
      return originalSupplierid || null;
    }

    // 其他角色用户或新增操作从supplierOptions中查找匹配的供应商ID
    const selectedSupplier = supplierOptions.find(
      (supplier) => supplier.value === supplierName
    );
    return selectedSupplier?.key || originalSupplierid || null;
  };

  const {
    airlineOptions: airlineList,
    portOptions: portList,
    packageTypeOptions: packageTypeList,
    specialItemOptions: specialItemsList,
    supplierOptions,
    loadAll,
  } = useBaseData();

  const weekDayOption = [
    {
      label: t("internationalPriceManagement.actionModal.weekdays.all"),
      value: "all",
    },
    {
      label: t("internationalPriceManagement.actionModal.weekdays.monday"),
      value: "1",
    },
    {
      label: t("internationalPriceManagement.actionModal.weekdays.tuesday"),
      value: "2",
    },
    {
      label: t("internationalPriceManagement.actionModal.weekdays.wednesday"),
      value: "3",
    },
    {
      label: t("internationalPriceManagement.actionModal.weekdays.thursday"),
      value: "4",
    },
    {
      label: t("internationalPriceManagement.actionModal.weekdays.friday"),
      value: "5",
    },
    {
      label: t("internationalPriceManagement.actionModal.weekdays.saturday"),
      value: "6",
    },
    {
      label: t("internationalPriceManagement.actionModal.weekdays.sunday"),
      value: "7",
    },
  ];

  const allWeekDayValues = weekDayOption
    .filter((option) => option.value !== "all")
    .map((option) => option.value);

  useEffect(() => {
    loadAll();
  }, []);

  const goBack = () => {
    setOpen(false);
  };
  const TitleBar = () => {
    return (
      <div className="fixed-title-box">
        <div className="title">
          {t(`internationalPriceManagement.actionModal.title.${method}`)}
        </div>
        <div className="go_back" onClick={goBack}>
          <LeftOutlined />
          <span>
            {t("internationalPriceManagement.actionModal.buttons.back")}
          </span>
        </div>
      </div>
    );
  };
  const onClose = () => {
    setOpen(false);
  };

  useEffect(() => {
    console.log("传输", originSchedules);
  }, [originSchedules]);

  const handleSubmit = () => {
    form
      .validateFields()
      .then(async (values) => {
        try {
          // 获取选中供应商的ID
          const supplierid = getSupplierIdForUpdate(
            values.suppliername,
            values.supplierid
          );

          // 处理特殊字段
          const formattedValues = {
            ...values,
            supplierid,
            effectivetime: formatDateToTimestamp(values.effectivetime),
            unloadingport: values?.unloadingport?.join("/"),
            // 过滤掉全选选项，并按数字排序
            originschedules: `D${values?.originschedules
              ?.filter((v: string) => v !== "all")
              ?.sort()
              ?.join("")}`,
            specialitems: values?.specialitems?.join(","),
            transferschedules:
              values?.transferschedules &&
              `D${values?.transferschedules
                ?.filter((v: string) => v !== "all")
                ?.sort()
                ?.join("")}`,

            specialcharges: values?.specialcharges ?? [],
            packagecharges: values?.packagecharges ?? [],
            cabinreport:
              values?.cabinreport?.map((cabin: any) => ({
                ...cabin,
                date: formatDateToTimestamp(cabin.date),
                transferdate: cabin.transferdate
                  ? formatDateToTimestamp(cabin.transferdate)
                  : undefined,
                pricechanges: cabin.pricechanges ?? [],
                specialprice: cabin.specialprice ?? [],
              })) ?? [],
            intervalprice: values?.intervalprice ?? [],
            userid: user?.userid,
            departmentid: user?.departmentid, // 默认部门ID
          };

          const res =
            method === "add"
              ? await addInternationalPrice(formattedValues)
              : await updateInternationalPrice(formattedValues);
          const { data } = res;
          if (data?.resultCode === 200) {
            message.success(
              t(
                `internationalPriceManagement.actionModal.messages.${method}Success`
              )
            );
            // 新增和复制操作查询当前用户的数据，编辑操作查询选中用户的数据
            const queryUserId =
              method === "add" ? user?.userid : selectedUserId || user?.userid;

            if (method === "add") {
              onResetFilters?.();
              fetchData(queryUserId, {
                pageindex: 1,
                pagesize: currentPagination?.pagesize || 10,
              });
            } else {
              fetchData(queryUserId, {
                pageindex: currentPagination?.pageindex || 1,
                pagesize: currentPagination?.pagesize || 10,
                ...currentFilters,
              });
            }
            onClose();
          } else {
            message.error(
              t(
                `internationalPriceManagement.actionModal.messages.${method}Failed`
              ) +
                "，" +
                (data?.message ||
                  t(
                    "internationalPriceManagement.actionModal.messages.unknownError"
                  ))
            );
          }
        } catch (e) {
          console.error(
            t("internationalPriceManagement.actionModal.messages.submitError"),
            e
          );
          message.error(
            t("internationalPriceManagement.actionModal.messages.submitFailed")
          );
        }
      })
      .catch((e) => {
        console.error("表单验证失败:", e);
        // 获取第一个错误字段并滚动到该位置
        const errorFields = form
          .getFieldsError()
          .filter(({ errors }) => errors.length);
        if (errorFields.length > 0) {
          form.scrollToField(errorFields[0].name, {
            behavior: "smooth",
            block: "center",
          });
        }
      });
  };

  return (
    <div className="add-container">
      <TitleBar />

      {/* 主内容区域 */}
      <div className="content-container">
        <Card variant="borderless" className="form-container">
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            className="form-block"
          >
            <Form.Item label="ID" name="priceid" style={{ display: "none" }}>
              <Input disabled />
            </Form.Item>
            <Form.Item
              label="SupplierID"
              name="supplierid"
              style={{ display: "none" }}
            >
              <Input disabled />
            </Form.Item>
            {/* 基本信息 */}
            <Card className="form-card">
              <h2 className="card-title">
                {t(
                  "internationalPriceManagement.actionModal.sections.basicInfo"
                )}
              </h2>
              <Row gutter={[12, 0]}>
                <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                  <Form.Item
                    label={t(
                      "internationalPriceManagement.actionModal.fields.supplier"
                    )}
                    name="suppliername"
                  >
                    <Select
                      className="input-default"
                      placeholder={t(
                        "internationalPriceManagement.actionModal.placeholders.selectSupplier"
                      )}
                      showSearch
                      optionFilterProp="label"
                      options={supplierOptions}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                  <Form.Item
                    label={t(
                      "internationalPriceManagement.actionModal.fields.effectiveDate"
                    )}
                    name="effectivetime"
                  >
                    <DatePicker
                      className="input-default"
                      style={{ width: "100%" }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                  <Form.Item
                    label={t(
                      "internationalPriceManagement.actionModal.fields.acceptSanction"
                    )}
                    name="acceptsanction"
                    valuePropName="checked"
                    initialValue={true}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                  <Form.Item
                    label={t(
                      "internationalPriceManagement.actionModal.fields.acceptBrand"
                    )}
                    name="acceptbrand"
                    valuePropName="checked"
                    initialValue={false}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* 航线信息 */}
            <Card className="form-card">
              <h2 className="card-title">
                {t(
                  "internationalPriceManagement.actionModal.sections.routeInfo"
                )}
              </h2>
              <Row gutter={[12, 0]}>
                <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                  <Form.Item
                    name="airlinename"
                    label={t(
                      "internationalPriceManagement.actionModal.fields.airlineName"
                    )}
                    rules={[
                      {
                        required: true,
                        message: t(
                          "internationalPriceManagement.actionModal.validation.selectAirline"
                        ),
                      },
                    ]}
                  >
                    <Select
                      options={airlineList}
                      placeholder={t(
                        "internationalPriceManagement.actionModal.placeholders.selectAirline"
                      )}
                      showSearch
                      optionFilterProp="label"
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                  <Form.Item
                    name="originport"
                    label={t(
                      "internationalPriceManagement.actionModal.fields.originPort"
                    )}
                    rules={[
                      {
                        required: true,
                        message: t(
                          "internationalPriceManagement.actionModal.validation.selectOriginPort"
                        ),
                      },
                    ]}
                  >
                    <Select
                      options={portList}
                      placeholder={t(
                        "internationalPriceManagement.actionModal.placeholders.selectOriginPort"
                      )}
                      showSearch
                      optionFilterProp="label"
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                  <Form.Item
                    name="originschedules"
                    label={t(
                      "internationalPriceManagement.actionModal.fields.originSchedules"
                    )}
                    rules={[
                      {
                        required: true,
                        message: t(
                          "internationalPriceManagement.actionModal.validation.selectOriginSchedules"
                        ),
                      },
                    ]}
                  >
                    <Select
                      options={weekDayOption}
                      placeholder={t(
                        "internationalPriceManagement.actionModal.placeholders.selectOriginSchedules"
                      )}
                      showSearch
                      optionFilterProp="label"
                      mode="multiple"
                      onChange={(values) => {
                        if (values.includes("all")) {
                          // 如果选择了"全选"，则选中所有星期
                          form.setFieldsValue({
                            originschedules: allWeekDayValues,
                          });
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                  <Form.Item
                    name="unloadingport"
                    label={t(
                      "internationalPriceManagement.actionModal.fields.destinationPort"
                    )}
                    rules={[
                      {
                        required: true,
                        message: t(
                          "internationalPriceManagement.actionModal.validation.selectDestinationPort"
                        ),
                      },
                    ]}
                  >
                    <Select
                      options={portList}
                      placeholder={t(
                        "internationalPriceManagement.actionModal.placeholders.selectDestinationPort"
                      )}
                      showSearch
                      optionFilterProp="label"
                      mode="multiple"
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                  <Form.Item
                    label={t(
                      "internationalPriceManagement.actionModal.fields.isTransfer"
                    )}
                    name="istransfer"
                    valuePropName="checked"
                    initialValue={false}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              {isTransfer && (
                <Row gutter={[12, 0]}>
                  <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                    <Form.Item
                      name="transfer"
                      label={t(
                        "internationalPriceManagement.actionModal.fields.transferPort"
                      )}
                      rules={[
                        {
                          required: true,
                          message: t(
                            "internationalPriceManagement.actionModal.validation.selectTransferPort"
                          ),
                        },
                      ]}
                    >
                      <Select
                        options={portList}
                        placeholder={t(
                          "internationalPriceManagement.actionModal.placeholders.selectTransferPort"
                        )}
                        showSearch
                        optionFilterProp="label"
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                    <Form.Item
                      name="transferschedules"
                      label={t(
                        "internationalPriceManagement.actionModal.fields.transferSchedules"
                      )}
                      rules={[
                        {
                          required: true,
                          message: t(
                            "internationalPriceManagement.actionModal.validation.selectTransferSchedules"
                          ),
                        },
                      ]}
                    >
                      <Select
                        options={weekDayOption}
                        placeholder={t(
                          "internationalPriceManagement.actionModal.placeholders.selectTransferSchedules"
                        )}
                        showSearch
                        optionFilterProp="label"
                        mode="multiple"
                        onChange={(values) => {
                          if (values.includes("all")) {
                            // 如果选择了"全选"，则选中所有星期
                            form.setFieldsValue({
                              transferschedules: allWeekDayValues,
                            });
                          }
                        }}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              )}
            </Card>

            {/* 尺寸重量限制 */}
            <Card className="form-card">
              <h2 className="card-title">
                {t(
                  "internationalPriceManagement.actionModal.sections.densityAndSize"
                )}
              </h2>
              <Row gutter={[12, 0]}>
                <Col xs={24} sm={12} md={8} lg={4} xl={4}>
                  <Form.Item
                    label={t(
                      "internationalPriceManagement.actionModal.fields.lengthLimit"
                    )}
                    name="lengthlimit"
                  >
                    <InputNumber
                      className="input-default"
                      min={0}
                      placeholder={t(
                        "internationalPriceManagement.actionModal.placeholders.inputLengthLimit"
                      )}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={8} lg={4} xl={4}>
                  <Form.Item
                    label={t(
                      "internationalPriceManagement.actionModal.fields.widthLimit"
                    )}
                    name="widthlimit"
                  >
                    <InputNumber
                      className="input-default"
                      min={0}
                      placeholder={t(
                        "internationalPriceManagement.actionModal.placeholders.inputWidthLimit"
                      )}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={8} lg={4} xl={4}>
                  <Form.Item
                    label={t(
                      "internationalPriceManagement.actionModal.fields.heightLimit"
                    )}
                    name="heightlimit"
                  >
                    <InputNumber
                      className="input-default"
                      min={0}
                      placeholder={t(
                        "internationalPriceManagement.actionModal.placeholders.inputHeightLimit"
                      )}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={8} lg={4} xl={4}>
                  <Form.Item
                    label={t(
                      "internationalPriceManagement.actionModal.fields.singleWeightLimit"
                    )}
                    name="singleweightlimit"
                  >
                    <InputNumber
                      className="input-default"
                      min={0}
                      precision={1}
                      placeholder={t(
                        "internationalPriceManagement.actionModal.placeholders.inputSingleWeightLimit"
                      )}
                    />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12} md={8} lg={4} xl={4}>
                  <Form.Item
                    label={t(
                      "internationalPriceManagement.actionModal.fields.isCabin"
                    )}
                    name="iscabin"
                    valuePropName="checked"
                    initialValue={false}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                {isCabin && (
                  <Col xs={24} sm={12} md={8} lg={4} xl={4}>
                    <Form.Item
                      label={t(
                        "internationalPriceManagement.actionModal.fields.cabinPrice"
                      )}
                      name="cabinprice"
                    >
                      <InputNumber
                        className="input-default"
                        min={0}
                        precision={3}
                      />
                    </Form.Item>
                  </Col>
                )}
              </Row>
            </Card>

            {/* 密度价格区间信息 */}
            <Card className="form-card">
              <h2 className="card-title">
                {t(
                  "internationalPriceManagement.actionModal.sections.intervalPricing"
                )}
              </h2>
              <Form.List name="intervalprice">
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, ...restField }) => (
                      <div key={key} className="interval-price-item">
                        <Row gutter={[12, 0]} align="middle">
                          <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                            <div className="common-label">
                              {t(
                                "internationalPriceManagement.actionModal.fields.densityRange"
                              )}
                            </div>
                            <div
                              style={{
                                display: "flex",
                                gap: "8px",
                                alignItems: "center",
                              }}
                            >
                              <Form.Item
                                {...restField}
                                name={[name, "densitylvalue"]}
                                style={{ margin: 0, flex: 1 }}
                                rules={[
                                  {
                                    required: true,
                                    message: t(
                                      "internationalPriceManagement.actionModal.validation.inputMinDensity"
                                    ),
                                  },
                                ]}
                              >
                                <InputNumber
                                  className="input-default"
                                  min={0}
                                  precision={0}
                                  placeholder={t(
                                    "internationalPriceManagement.actionModal.placeholders.minDensity"
                                  )}
                                />
                              </Form.Item>
                              <span>-</span>
                              <Form.Item
                                {...restField}
                                name={[name, "densityrvalue"]}
                                style={{ margin: 0, flex: 1 }}
                                rules={[
                                  {
                                    required: true,
                                    message: t(
                                      "internationalPriceManagement.actionModal.validation.inputMaxDensity"
                                    ),
                                  },
                                ]}
                              >
                                <InputNumber
                                  className="input-default"
                                  min={0}
                                  precision={0}
                                  placeholder={t(
                                    "internationalPriceManagement.actionModal.placeholders.maxDensity"
                                  )}
                                />
                              </Form.Item>
                            </div>
                          </Col>
                          <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                            <Form.Item
                              {...restField}
                              name={[name, "q100"]}
                              label={t(
                                "internationalPriceManagement.actionModal.fields.q100Price"
                              )}
                            >
                              <InputNumber
                                className="input-default"
                                min={0}
                                precision={1}
                                placeholder={t(
                                  "internationalPriceManagement.actionModal.placeholders.inputQ100Price"
                                )}
                              />
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                            <Form.Item
                              {...restField}
                              name={[name, "q300"]}
                              label={t(
                                "internationalPriceManagement.actionModal.fields.q300Price"
                              )}
                            >
                              <InputNumber
                                className="input-default"
                                min={0}
                                precision={1}
                                placeholder={t(
                                  "internationalPriceManagement.actionModal.placeholders.inputQ300Price"
                                )}
                              />
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                            <Form.Item
                              {...restField}
                              name={[name, "q500"]}
                              label={t(
                                "internationalPriceManagement.actionModal.fields.q500Price"
                              )}
                            >
                              <InputNumber
                                className="input-default"
                                min={0}
                                precision={1}
                                placeholder={t(
                                  "internationalPriceManagement.actionModal.placeholders.inputQ500Price"
                                )}
                              />
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                            <Form.Item
                              {...restField}
                              name={[name, "q1000"]}
                              label={t(
                                "internationalPriceManagement.actionModal.fields.q1000Price"
                              )}
                            >
                              <InputNumber
                                className="input-default"
                                min={0}
                                precision={1}
                                placeholder={t(
                                  "internationalPriceManagement.actionModal.placeholders.inputQ1000Price"
                                )}
                              />
                            </Form.Item>
                          </Col>
                          <MinusCircleOutlined
                            className="icon-remove"
                            onClick={() => remove(name)}
                            style={{
                              fontSize: "16px",
                              color: "#ff4d4f",
                              marginLeft: "10px",
                            }}
                          />
                        </Row>
                      </div>
                    ))}
                    <Button
                      type="dashed"
                      onClick={() => add()}
                      block
                      icon={<PlusOutlined />}
                      className="btn-dashed"
                      style={{ marginTop: "16px" }}
                    >
                      {t(
                        "internationalPriceManagement.actionModal.buttons.addIntervalPrice"
                      )}
                    </Button>
                  </>
                )}
              </Form.List>
            </Card>

            {/* 价格信息 */}
            <Card className="form-card">
              <h2 className="card-title">
                {t(
                  "internationalPriceManagement.actionModal.sections.priceInfo"
                )}
              </h2>

              {/* 包装费用与特殊物品收费 */}
              <Row gutter={[12, 0]} style={{ marginBottom: 16 }}>
                <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                  <div className="sub-section">
                    <h3 className="sub-title">
                      {t(
                        "internationalPriceManagement.actionModal.sections.packageCharges"
                      )}
                    </h3>
                    <Form.List name="packagecharges">
                      {(fields, { add, remove }) => (
                        <>
                          {fields.map(({ key, name, ...restField }) => (
                            <div key={key} className="dynamic-row">
                              <Form.Item
                                {...restField}
                                name={[name, "packageItem"]}
                                className="row-item"
                              >
                                <Select
                                  options={packageTypeList}
                                  placeholder={t(
                                    "internationalPriceManagement.actionModal.placeholders.packageItemName"
                                  )}
                                  showSearch
                                  optionFilterProp="label"
                                />
                              </Form.Item>
                              <Form.Item
                                {...restField}
                                name={[name, "value"]}
                                className="row-item"
                              >
                                <InputNumber
                                  className="input-default"
                                  placeholder={t(
                                    "internationalPriceManagement.actionModal.placeholders.chargeValue"
                                  )}
                                  min={0}
                                  precision={1}
                                />
                              </Form.Item>
                              <MinusCircleOutlined
                                className="icon-remove"
                                onClick={() => remove(name)}
                              />
                            </div>
                          ))}
                          <Button
                            type="dashed"
                            onClick={() => add()}
                            block
                            icon={<PlusOutlined />}
                            className="btn-dashed"
                          >
                            {t(
                              "internationalPriceManagement.actionModal.buttons.addPackageCharge"
                            )}
                          </Button>
                        </>
                      )}
                    </Form.List>
                  </div>
                </Col>
                <Col xs={24} sm={24} md={12} lg={12} xl={12}>
                  <div className="sub-section">
                    <h3 className="sub-title">
                      {t(
                        "internationalPriceManagement.actionModal.sections.specialCharges"
                      )}
                    </h3>
                    <Form.List name="specialcharges">
                      {(fields, { add, remove }) => (
                        <>
                          {fields.map(({ key, name, ...restField }) => (
                            <div key={key} className="dynamic-row">
                              <Form.Item
                                {...restField}
                                name={[name, "specialItem"]}
                                className="row-item"
                              >
                                <Select
                                  options={specialItemsList}
                                  placeholder={t(
                                    "internationalPriceManagement.actionModal.placeholders.specialItemName"
                                  )}
                                  optionFilterProp="label"
                                />
                              </Form.Item>
                              <Form.Item
                                {...restField}
                                name={[name, "value"]}
                                className="row-item"
                              >
                                <InputNumber
                                  className="input-default"
                                  placeholder={t(
                                    "internationalPriceManagement.actionModal.placeholders.chargeValue"
                                  )}
                                  min={0}
                                  precision={1}
                                />
                              </Form.Item>
                              <MinusCircleOutlined
                                className="icon-remove"
                                onClick={() => remove(name)}
                              />
                            </div>
                          ))}
                          <Button
                            type="dashed"
                            onClick={() => add()}
                            block
                            icon={<PlusOutlined />}
                            className="btn-dashed"
                          >
                            {t(
                              "internationalPriceManagement.actionModal.buttons.addSpecialCharge"
                            )}
                          </Button>
                        </>
                      )}
                    </Form.List>
                  </div>
                </Col>
              </Row>

              <Divider />

              <Row gutter={[12, 0]}>
                <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                  <Form.Item
                    label={t(
                      "internationalPriceManagement.actionModal.fields.mPrice"
                    )}
                    name="mprice"
                  >
                    <InputNumber
                      className="input-default"
                      min={0}
                      precision={1}
                      placeholder={t(
                        "internationalPriceManagement.actionModal.placeholders.inputMPrice"
                      )}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                  <Form.Item
                    label={t(
                      "internationalPriceManagement.actionModal.fields.nPrice"
                    )}
                    name="nprice"
                  >
                    <InputNumber
                      className="input-default"
                      min={0}
                      precision={1}
                      placeholder={t(
                        "internationalPriceManagement.actionModal.placeholders.inputNPrice"
                      )}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                  <Form.Item
                    label={t(
                      "internationalPriceManagement.actionModal.fields.q45Price"
                    )}
                    name="q45price"
                  >
                    <InputNumber
                      className="input-default"
                      min={0}
                      precision={1}
                      placeholder={t(
                        "internationalPriceManagement.actionModal.placeholders.inputQ45Price"
                      )}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                  <Form.Item
                    label={t(
                      "internationalPriceManagement.actionModal.fields.smallCargoFee"
                    )}
                    name="smallcargofee"
                  >
                    <InputNumber
                      className="input-default"
                      min={0}
                      precision={1}
                      placeholder={t(
                        "internationalPriceManagement.actionModal.placeholders.inputSmallCargoFee"
                      )}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Divider>
                {t(
                  "internationalPriceManagement.actionModal.sections.branchPricing"
                )}
              </Divider>

              <Row gutter={[12, 0]}>
                <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                  <Form.Item
                    label={t(
                      "internationalPriceManagement.actionModal.fields.q45Sub"
                    )}
                    name="q45_sub"
                    valuePropName="checked"
                    initialValue={false}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                {isQ45Sub && (
                  <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                    <Form.Item
                      label={t(
                        "internationalPriceManagement.actionModal.fields.subQ45Price"
                      )}
                      name="subq45price"
                    >
                      <InputNumber
                        className="input-default"
                        min={0}
                        precision={3}
                        placeholder={t(
                          "internationalPriceManagement.actionModal.placeholders.inputSubQ45Price"
                        )}
                      />
                    </Form.Item>
                  </Col>
                )}
                <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                  <Form.Item
                    label={t(
                      "internationalPriceManagement.actionModal.fields.q100Sub"
                    )}
                    name="q100_sub"
                    valuePropName="checked"
                    initialValue={false}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                {isQ100Sub && (
                  <Col xs={24} sm={12} md={6} lg={6} xl={6}>
                    <Form.Item
                      label={t(
                        "internationalPriceManagement.actionModal.fields.subQ100Price"
                      )}
                      name="subq100price"
                    >
                      <InputNumber
                        className="input-default"
                        min={0}
                        precision={3}
                        placeholder={t(
                          "internationalPriceManagement.actionModal.placeholders.inputSubQ100Price"
                        )}
                      />
                    </Form.Item>
                  </Col>
                )}
              </Row>
            </Card>

            {/* 舱报信息 */}
            <Card className="form-card">
              <CabinReportForm
                form={form}
                isTransfer={isTransfer}
                originSchedules={originSchedules}
              />
            </Card>
          </Form>

          {/* 固定在底部的按钮栏 */}
          <div className="footer">
            <Space className="footer-button">
              <Button onClick={() => form.resetFields()}>
                {t("internationalPriceManagement.actionModal.buttons.reset")}
              </Button>
              <Button onClick={onClose}>
                {t("internationalPriceManagement.actionModal.buttons.cancel")}
              </Button>
              <Button type="primary" onClick={handleSubmit}>
                {t("internationalPriceManagement.actionModal.buttons.save")}
              </Button>
            </Space>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AddSupplyPrice;
