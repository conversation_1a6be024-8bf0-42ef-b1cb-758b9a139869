import React, { useState, useEffect, useRef } from "react";
import { message, Modal, Spin } from "antd";
import type { TextAreaRef } from "antd/es/input/TextArea";
import "./index.less";
import ChatPanel from "./components/ChatPanel";
import {
  initializeAIAssistant,
  sendMessageToAI,
  chatCompletions,
} from "./services";
import { Message } from "./types";
import {
  generateSingleFieldCollectionRequest,
  formatFinalShippingInfo,
  generatePortConvertSystemPrompt,
  parsePortConvertResponse,
} from "./config/promptConfig";
import {
  convertToCentimeters,
  convertShippedPlaceToEnglish,
} from "@/utils/util";
import dayjs from "dayjs";
import { useNavigate } from "react-router-dom";
import { formatDateToTimestamp } from "@/utils/util";

// 常量配置
const SUGGESTIONS_CONFIG = {
  PACKAGING: ["纸箱", "布卷", "木箱", "桶", "麻袋"],
  SPECIAL_CARGO: ["液体", "锂电", "带电", "航材", "温控", "鲜活"],
  INITIAL: [
    "我需要从上海到莫斯科的空运报价",
    "深圳到洛杉矶，3件货物，总重50公斤，总体积0.5立方米，尺寸100*80*60cm",
    "北京到墨西哥，锂电产品，纸箱包装，要求本周发货",
    "广州到圣彼得堡，要求明日发货，需要保舱服务",
  ],
  GROSS_WEIGHT: [
    "总重10kg",
    "每件重量为5kg、8kg、12kg",
    "第一件重量为20kg、第二件为15kg",
    "10kg、8kg、16kg",
  ],
  GROSS_VOLUME: [
    "总体积为1.25cbm",
    "每件体积为2cbm、1.5cbm、1.75cbm",
    "第一件体积为2cbm、第二件为1.5cbm",
    "2cbm、1.5cbm、1.75cbm",
  ],
  GROSS_SIZE: [
    "50cm × 30cm × 20cm",
    "第一件 50cm × 30cm × 20cm，第二件 60cm × 40cm × 30cm",
    "全为20cm × 30cm × 40cm",
  ],
};

// 字段类型映射
const FIELD_TYPE_MAP = {
  包装形式: "PACKAGING",
  特殊货物: "SPECIAL_CARGO",
  货物总毛重: "GROSS_WEIGHT",
  货物总体积: "GROSS_VOLUME",
  货物尺寸: "GROSS_SIZE",
} as const;

interface ExtractedData {
  companyname?: string;
  inquirer?: string;
  originport?: string;
  unloadingport?: string | string[]; // 支持多个目的港
  shippedplace?: string;
  grossweight?: number;
  goodsvolume?: number;
  goodsnumber?: string;
  singleweightlimit?: number | null;
  cargolength?: number;
  cargowidth?: number;
  cargoheight?: number;
  isbrand?: boolean;
  specialcargo?: string | string[];
  isvalidity?: boolean;
  shipmentdate?: string; // 发货日期
  ensurecabin?: boolean;
  packagetype?: string;
}

// 单条询价记录的数据结构
interface InquiryRecord extends ExtractedData {
  unloadingport: string; // 单个目的港
}

const AIQuotation: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [initializing, setInitializing] = useState(true);
  // 保存上一次请求的完整message参数
  const [lastRequestMessages, setLastRequestMessages] = useState<Array<{
    role: string;
    content: any;
  }> | null>(null);
  // 保存上一次AI的响应
  const [lastAIResponse, setLastAIResponse] = useState<string | null>(null);
  // 保存提取的货运信息
  const [extractedShippingInfo, setExtractedShippingInfo] = useState<{
    allInfo: { [key: string]: string };
    missingInfo: string[];
    hasAllInfo: boolean;
  } | null>(null);
  // 当前正在收集的信息字段
  const [currentCollectingField, setCurrentCollectingField] = useState<
    string | null
  >(null);
  // 待收集的信息队列
  const [pendingFields, setPendingFields] = useState<string[]>([]);
  // 对话轮次计数器
  const [conversationRound, setConversationRound] = useState(1);
  // 是否需要重新初始化AI助手
  const [needReinitialize, setNeedReinitialize] = useState(false);
  // 是否是首次初始化
  const [isFirstInitialization, setIsFirstInitialization] = useState(true);
  const [formattedInfo, setFormattedInfo] = useState<string>("");
  // 新增询价页面加载状态
  const [isCreatingInquiry, setIsCreatingInquiry] = useState(false);
  // 当前操作类型，用于显示不同的加载提示
  const [currentOperation, setCurrentOperation] = useState<
    "create" | "query" | null
  >(null);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<TextAreaRef>(null);
  const navigator = useNavigate();

  // 获取字段建议选项
  const extractSuggestions = (fieldType: string): string[] => {
    const mappedType = FIELD_TYPE_MAP[fieldType as keyof typeof FIELD_TYPE_MAP];
    return mappedType ? SUGGESTIONS_CONFIG[mappedType] : [];
  };

  // 创建消息的工具函数
  const createMessage = (
    id: string,
    content: string,
    sender: "user" | "ai",
    options: {
      suggestions?: string[];
      isLoading?: boolean;
      isNewRound?: boolean;
      isInfoCollectionComplete?: boolean;
    } = {}
  ): Message => ({
    id,
    content,
    sender,
    timestamp: new Date(),
    ...options,
  });

  // 创建欢迎消息
  const createWelcomeMessage = (
    content: string,
    conversationRound: number,
    needReinitialize: boolean
  ): Message =>
    createMessage(`welcome-${conversationRound}`, content, "ai", {
      suggestions: SUGGESTIONS_CONFIG.INITIAL,
      isNewRound: needReinitialize,
    });

  // 创建错误消息
  const createErrorMessage = (id: string): Message =>
    createMessage(
      id,
      "抱歉，AI助手初始化失败。请检查网络连接或API Key设置。",
      "ai",
      { suggestions: SUGGESTIONS_CONFIG.INITIAL }
    );

  // 统一的字段信息解析方法
  const parseFieldInfoFromResponse = (content: string) => {
    // 提取<>部分的内容
    const match = content.match(/<([^>]+)>/);
    if (!match) return null;

    const infoString = match[1];
    const infoItems = infoString.split("，");

    const parsedInfo: { [key: string]: string } = {};

    infoItems.forEach((item) => {
      const [key, value] = item.split("：");
      if (key && value !== undefined) {
        const trimmedKey = key.trim();
        const trimmedValue = value.trim();
        parsedInfo[trimmedKey] = trimmedValue;
      }
    });

    return parsedInfo;
  };

  // 基于解析结果分析缺失信息（用于流程控制）
  const analyzeCollectionInfo = (parsedInfo: { [key: string]: string }) => {
    const missingInfo: string[] = [];

    Object.entries(parsedInfo).forEach(([key, value]) => {
      if (value === "暂无") {
        missingInfo.push(key);
      }
    });

    return {
      allInfo: parsedInfo,
      missingInfo,
      hasAllInfo: missingInfo.length === 0,
    };
  };

  // 重置对话状态
  const resetConversationState = (keepHistory: boolean = true) => {
    // 重置所有与当前对话相关的状态
    setLastRequestMessages(null);
    setLastAIResponse(null);
    setExtractedShippingInfo(null);
    setCurrentCollectingField(null);
    setPendingFields([]);

    setInputValue("");

    if (keepHistory) {
      // 保留历史消息，增加对话轮次
      setConversationRound((prev) => prev + 1);
      setNeedReinitialize(true);
    } else {
      // 完全重置，清空所有消息
      setMessages([]);
      setConversationRound(1);
      setIsFirstInitialization(true);
      setNeedReinitialize(false);
    }
  };

  // 手动开启新对话
  const handleStartNewConversation = () => {
    // 检查是否有正在进行的对话或已有消息
    const hasActiveConversation =
      currentCollectingField ||
      extractedShippingInfo ||
      pendingFields.length > 0 ||
      isTyping ||
      messages.length > 0;

    if (hasActiveConversation) {
      // 显示确认对话框
      Modal.confirm({
        title: "开启新对话",
        content: "开启新对话将清空所有聊天记录并重新开始。确定要继续吗？",
        okText: "确定",
        cancelText: "取消",
        onOk: () => {
          resetConversationState(false); // 不保留历史记录
          message.success("已重新开始对话");
        },
      });
    } else {
      // 没有活跃对话，直接开启新对话
      resetConversationState(false); // 不保留历史记录
      message.success("已重新开始对话");
    }
  };

  // 初始化AI助手并获取欢迎消息
  useEffect(() => {
    const initializeAI = async () => {
      setInitializing(true);
      try {
        // 只有首次初始化时才清空消息
        if (isFirstInitialization) {
          setMessages([]);
          setIsFirstInitialization(false);
        }

        // 调用DeepSeek API获取初始欢迎信息
        const response = await initializeAIAssistant();

        const content =
          response?.data?.data?.body?.choices?.[0]?.message?.content;

        const welcomeMessage = content
          ? createWelcomeMessage(content, conversationRound, needReinitialize)
          : createErrorMessage(`welcome-${conversationRound}`);

        // 添加新消息到消息列表
        if (needReinitialize) {
          setMessages((prev) => [...prev, welcomeMessage]);
          setNeedReinitialize(false);
        } else {
          setMessages([welcomeMessage]);
        }
      } catch (error) {
        console.error("初始化AI助手失败:", error);
        const errorMessage = createErrorMessage(`welcome-${conversationRound}`);

        if (needReinitialize) {
          setMessages((prev) => [...prev, errorMessage]);
          setNeedReinitialize(false);
        } else {
          setMessages([errorMessage]);
        }
      } finally {
        setInitializing(false);
      }
    };

    // 只在首次加载或需要重新初始化时执行
    if (isFirstInitialization || needReinitialize) {
      initializeAI();
    }
  }, [needReinitialize, conversationRound, isFirstInitialization]);

  // 自动滚动到最新消息
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // 检查消息列表中是否有加载中的消息
  const hasLoadingMessage = messages.some((msg) => msg.isLoading);

  // 根据消息加载状态更新输入框禁用状态
  useEffect(() => {
    setIsTyping(hasLoadingMessage);
  }, [hasLoadingMessage]);

  // 消息操作工具函数
  const updateMessage = (
    content: string,
    id: string,
    suggestions?: string[]
  ) => {
    const aiResponse = createMessage(id, content, "ai", { suggestions });
    setMessages((prev) =>
      prev.map((msg) => (msg.id === id ? aiResponse : msg))
    );
  };

  const setErrorMessage = (content: string, id: string) => {
    const errorMessage = createMessage(id, content, "ai");
    setMessages((prev) =>
      prev.map((msg) => (msg.id === id ? errorMessage : msg))
    );
  };

  const removeLoadingMessage = (id: string) => {
    setMessages((prev) => prev.filter((msg) => msg.id !== id));
  };

  const addMessage = (message: Message) => {
    setMessages((prev) => [...prev, message]);
  };

  const replaceMessage = (id: string, newMessage: Message) => {
    setMessages((prev) =>
      prev.map((msg) => (msg.id === id ? newMessage : msg))
    );
  };

  // 信息处理工具函数
  const mergeInfo = (
    currentInfo: Record<string, string>,
    newFieldInfo: Record<string, string>
  ): Record<string, string> => ({ ...currentInfo, ...newFieldInfo });

  const updateExtractedInfo = (info: Record<string, string>) => {
    const missingInfo = Object.entries(info)
      .filter(([_, v]) => v === "暂无")
      .map(([k]) => k);

    setExtractedShippingInfo({
      allInfo: info,
      missingInfo,
      hasAllInfo: missingInfo.length === 0,
    });
  };

  // 检查字段是否需要建议选项
  const shouldShowSuggestions = (fieldType: string): boolean => {
    return ["包装形式", "特殊货物"].includes(fieldType);
  };

  // 生成结构化信息内容
  const generateStructuredInfoContent = (
    info: Record<string, string>
  ): string => {
    const structuredInfo = Object.entries(info)
      .map(([key, value]) => `${key}：${value}`)
      .join("，");
    return `<${structuredInfo}>`;
  };

  const handleFinalQuotation = async (
    info: Record<string, string>,
    loadingId: string
  ) => {
    const structuredInfoContent = generateStructuredInfoContent(info);
    setFormattedInfo(formatFinalShippingInfo(info));

    const summaryMessage = createMessage(
      loadingId,
      `🎉 货运信息收集完成！\n\n${structuredInfoContent}`,
      "ai",
      { isInfoCollectionComplete: true }
    );

    replaceMessage(loadingId, summaryMessage);
  };

  // 调用DeepSeek AI获取响应
  const getAIResponse = async (userMessage: string) => {
    const now = Date.now();

    // 添加用户消息和加载状态
    const userMsg = createMessage(now.toString(), userMessage, "user");
    const loadingMessageId = (now + 1).toString();
    const loadingMsg = createMessage(loadingMessageId, "", "ai", {
      isLoading: true,
    });

    addMessage(userMsg);
    addMessage(loadingMsg);

    try {
      // 获取当前对话轮次的消息历史（从最新的欢迎消息开始）
      const filteredMessages = messages.filter((msg) => !msg.isLoading);

      // 找到最后一个欢迎消息的索引
      let lastWelcomeIndex = -1;
      for (let i = filteredMessages.length - 1; i >= 0; i--) {
        if (filteredMessages[i].id.startsWith("welcome-")) {
          lastWelcomeIndex = i;
          break;
        }
      }

      // 从最后一个欢迎消息开始获取当前轮次的消息
      const currentRoundMessages = (
        lastWelcomeIndex >= 0
          ? filteredMessages.slice(lastWelcomeIndex)
          : filteredMessages
      ).map((msg) => ({
        role: msg.sender === "user" ? "user" : "assistant",
        content: msg.content,
      }));

      const response = await sendMessageToAI(
        userMessage,
        currentRoundMessages,
        lastRequestMessages || undefined,
        lastAIResponse || undefined
      );

      const aiContent =
        response?.data?.data?.body?.choices?.[0]?.message?.content;

      if (!aiContent) {
        return setErrorMessage(
          "抱歉，我现在无法处理您的请求，请稍后再试。",
          loadingMessageId
        );
      }

      // 响应内容处理
      // 首先尝试解析响应中的字段信息（无论是否有Collection completed）
      const parsedFieldInfo = parseFieldInfoFromResponse(aiContent);

      // 计算最新的完整信息（用于后续流程控制）
      let latestCompleteInfo = extractedShippingInfo?.allInfo || {};

      // 如果解析到了字段信息，更新存储的数据
      if (parsedFieldInfo && Object.keys(parsedFieldInfo).length > 0) {
        if (currentCollectingField) {
          // 正在收集某字段，合并信息
          const updatedInfo = mergeInfo(
            extractedShippingInfo?.allInfo || {},
            parsedFieldInfo
          );

          // 特殊处理ETD字段
          if (currentCollectingField === "要求ETD") {
            const etdValue = parsedFieldInfo["要求ETD"];
            if (etdValue === "无" || etdValue === "暂无") {
              updatedInfo["保舱"] = "无";
            }
          }

          // 更新最新完整信息
          latestCompleteInfo = updatedInfo;

          updateExtractedInfo(updatedInfo);
          console.log("更新字段信息:", parsedFieldInfo);
          console.log("合并后的完整信息:", updatedInfo);
        } else {
          // 首次解析或非字段收集状态，直接更新
          const currentInfo = extractedShippingInfo?.allInfo || {};
          const updatedInfo = { ...currentInfo, ...parsedFieldInfo };

          // 更新最新完整信息
          latestCompleteInfo = updatedInfo;

          updateExtractedInfo(updatedInfo);
          console.log("首次解析字段信息:", parsedFieldInfo);
          console.log("更新后的完整信息:", updatedInfo);
        }
      }

      // 检查是否包含Collection completed标识
      if (aiContent.includes("Collection completed")) {
        const parsedInfo = parseFieldInfoFromResponse(aiContent);

        if (!parsedInfo) {
          removeLoadingMessage(loadingMessageId);
          return;
        }

        const extractedInfo = analyzeCollectionInfo(parsedInfo);

        if (currentCollectingField) {
          // 正在收集某字段，使用最新计算的完整信息
          console.log("使用最新完整信息:", latestCompleteInfo);

          // 检查哪些字段仍然是"暂无"状态，这些才是需要继续收集的字段
          let fieldsToProcess = Object.entries(latestCompleteInfo)
            .filter(([_, value]) => value === "暂无")
            .map(([key]) => key);

          // 如果ETD为无或暂无，则从待处理字段中移除保舱字段
          if (
            latestCompleteInfo["要求ETD"] === "无" ||
            latestCompleteInfo["要求ETD"] === "暂无"
          ) {
            fieldsToProcess = fieldsToProcess.filter((f) => f !== "保舱");
          }

          console.log("Collection completed - 待处理字段:", fieldsToProcess);

          setPendingFields(fieldsToProcess);
          setCurrentCollectingField(null);

          if (fieldsToProcess.length > 0) {
            return startSingleFieldCollection(
              fieldsToProcess[0],
              latestCompleteInfo,
              loadingMessageId
            );
          } else {
            return handleFinalQuotation(latestCompleteInfo, loadingMessageId);
          }
        }

        if (extractedInfo.missingInfo.length > 0) {
          // 第一次缺失字段
          setExtractedShippingInfo(extractedInfo);
          setPendingFields(extractedInfo.missingInfo);
          return startSingleFieldCollection(
            extractedInfo.missingInfo[0],
            extractedInfo.allInfo,
            loadingMessageId
          );
        }

        // 所有信息已收集完毕
        return handleFinalQuotation(latestCompleteInfo, loadingMessageId);
      }

      // 非结构化响应 - 检查是否需要添加建议选项
      // 注意：对于重量、体积、尺寸字段，只在startSingleFieldCollection的第一次对话中显示示例
      // 后续的用户对话中不再显示示例建议
      let suggestions: string[] = [];
      if (currentCollectingField) {
        if (shouldShowSuggestions(currentCollectingField)) {
          // 对于包装形式和特殊货物，使用预设选项
          suggestions = extractSuggestions(currentCollectingField);
        }
      }

      updateMessage(aiContent, loadingMessageId, suggestions);
      setLastAIResponse(aiContent);
      if (response.sentMessages) {
        setLastRequestMessages(response.sentMessages);
      }
    } catch (error) {
      console.error("调用DeepSeek API失败:", error);
      setErrorMessage(
        "抱歉，我现在无法处理您的请求，请检查网络连接或API Key设置。",
        loadingMessageId
      );
    }
  };

  const handleSendMessage = () => {
    if (inputValue.trim()) {
      getAIResponse(inputValue);
      setInputValue("");

      // 聚焦输入框
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 开始收集单个字段信息
  const startSingleFieldCollection = async (
    targetField: string,
    collectedInfo: { [key: string]: string },
    existingLoadingMessageId?: string
  ) => {
    try {
      // 设置当前正在收集的字段
      setCurrentCollectingField(targetField);

      // 生成单个字段收集的系统提示词
      const fieldRequest = generateSingleFieldCollectionRequest(
        targetField,
        collectedInfo
      );

      // 重置对话状态，使用新的系统提示词
      setLastRequestMessages([fieldRequest]);
      setLastAIResponse(null);

      // 直接调用AI获取该字段的开场白，不显示自定义消息
      // 使用空字符串作为用户消息，让AI根据系统提示词生成开场白
      const response = await sendMessageToAI("", [], [fieldRequest]);

      const aiContent =
        response?.data?.data?.body?.choices?.[0]?.message?.content;
      if (aiContent) {
        // 检查是否需要添加建议选项
        let suggestions: string[] = [];
        if (targetField === "包装形式" || targetField === "特殊货物") {
          // 对于包装形式和特殊货物，使用预设选项
          suggestions = extractSuggestions(targetField);
        } else if (
          targetField === "货物总毛重" ||
          targetField === "货物总体积" ||
          targetField === "货物尺寸"
        ) {
          // 对于重量、体积、尺寸字段，在第一次收集时显示示例
          suggestions = extractSuggestions(targetField);
        }

        const aiResponse = createMessage(
          existingLoadingMessageId || Date.now().toString(),
          aiContent,
          "ai",
          { suggestions: suggestions.length > 0 ? suggestions : undefined }
        );

        if (existingLoadingMessageId) {
          replaceMessage(existingLoadingMessageId, aiResponse);
        } else {
          addMessage(aiResponse);
        }

        // 保存当前请求的messages和AI响应
        if (response.sentMessages) {
          setLastRequestMessages(response.sentMessages);
        }
        setLastAIResponse(aiContent);
      }
    } catch (error) {
      console.error("开始收集字段信息失败:", error);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    // 如果文本框已有内容，用逗号分隔追加；否则直接设置
    if (inputValue.trim()) {
      setInputValue(inputValue.trim() + "，" + suggestion);
    } else {
      setInputValue(suggestion);
    }

    // 聚焦到文本框
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  };

  // 港口转换和询价记录生成工具函数
  const performPortConversion = async (
    formattedInfo: string
  ): Promise<ExtractedData> => {
    const portConvertPrompt = generatePortConvertSystemPrompt(formattedInfo);
    const messages = [{ role: "user", content: portConvertPrompt }];

    const response = await chatCompletions(messages);
    const aiResponse =
      response?.data?.data?.body?.choices?.[0]?.message?.content;

    if (!aiResponse) {
      throw new Error("港口转换AI响应格式错误");
    }

    return parsePortConvertResponse(aiResponse);
  };

  const generateInquiryRecords = (
    extractedInfo: ExtractedData
  ): InquiryRecord[] => {
    const { unloadingport, ...baseData } = extractedInfo;

    if (!unloadingport) return [];

    const processedBaseData = {
      ...baseData,
      shippedplace: baseData.shippedplace
        ? convertShippedPlaceToEnglish(baseData.shippedplace)
        : baseData.shippedplace,
    };

    if (Array.isArray(unloadingport)) {
      return unloadingport.map((port) => ({
        ...processedBaseData,
        unloadingport: port,
      }));
    }

    return [{ ...processedBaseData, unloadingport: unloadingport as string }];
  };

  // 准备预填数据
  const preparePrefilledData = (record: InquiryRecord | ExtractedData) => ({
    ...record,
    inquirytime: dayjs(),
    shipmentdate: record?.shipmentdate
      ? dayjs(record?.shipmentdate)
      : undefined,
    freightmethod: "AF",
    specialcargo: record?.specialcargo
      ? Array.isArray(record.specialcargo)
        ? record.specialcargo
        : [record.specialcargo]
      : undefined,
    originport: record?.originport
      ? Array.isArray(record.originport)
        ? record.originport
        : [record.originport]
      : undefined,
  });

  // 新增询价按钮回调
  const handleCreateInquiry = async () => {
    // 立即显示跳转提示和加载状态
    setCurrentOperation("create");
    setIsCreatingInquiry(true); // 显示加载蒙版

    try {
      const converted = await performPortConversion(formattedInfo);
      console.log("港口转换完成，转换后的对象:", converted);

      const records = generateInquiryRecords(converted);
      console.log("询价记录列表:", records);

      const baseRecord = records.length > 0 ? records[0] : converted;
      const prefilledData = preparePrefilledData(baseRecord);

      navigator("/quotation", {
        state: {
          action: "create",
          prefilledData,
          inquiryRecords: records,
          isMultipleRecords: records.length > 1,
        },
      });

      // 跳转成功后，加载状态会在组件卸载时自动清除
    } catch (error) {
      console.error("港口转换失败:", error);
      message.error("港口转换失败，请稍后重试。");
      setCurrentOperation(null);
      setIsCreatingInquiry(false); // 出错时隐藏加载蒙版
    }
  };

  // 查询报价按钮回调
  const handleQueryQuotation = async () => {
    console.log("点击查询报价按钮");
    setCurrentOperation("query");
    setIsCreatingInquiry(true); // 显示加载蒙版

    try {
      const converted = await performPortConversion(formattedInfo);
      console.log("港口转换完成，转换后的对象:", converted);

      // 准备供应价格查询参数
      const queryParams = {
        originport: converted.originport,
        unloadingport: Array.isArray(converted.unloadingport)
          ? converted.unloadingport[0]
          : converted.unloadingport,
        grossweight: converted.grossweight,
        goodsvolume: converted.goodsvolume,
        cargolength: converted.cargolength,
        cargowidth: converted.cargowidth,
        cargoheight: converted.cargoheight,
        specialcargo: Array.isArray(converted.specialcargo)
          ? converted.specialcargo.join(",")
          : converted.specialcargo,
        shipmentdate: converted.shipmentdate
          ? formatDateToTimestamp(dayjs(converted?.shipmentdate))
          : undefined,
        packagetype: converted.packagetype,
        freightmethod: "AF", // 默认空运
      };

      // 跳转到供应价格管理页面
      navigator("/supply_price", {
        state: queryParams,
      });

      // 跳转成功后，加载状态会在组件卸载时自动清除
    } catch (error) {
      console.error("港口转换失败:", error);
      message.error("港口转换失败，请稍后重试。");
      setCurrentOperation(null);
      setIsCreatingInquiry(false); // 出错时隐藏加载蒙版
    }
  };

  return (
    <div className="intelligent-quotation-container">
      <Spin
        spinning={isCreatingInquiry}
        tip={
          currentOperation === "create"
            ? "正在创建询价，请稍候..."
            : currentOperation === "query"
              ? "正在查询报价，请稍候..."
              : "正在处理询价信息，请稍候..."
        }
        size="large"
      >
        <div className="ai-quotation-content">
          <div className="ai-interface-container">
            {/* 聊天面板 */}
            <ChatPanel
              messages={messages}
              inputValue={inputValue}
              isTyping={isTyping}
              initializing={initializing}
              inputRef={inputRef}
              messagesEndRef={messagesEndRef}
              handleSendMessage={handleSendMessage}
              handleKeyDown={handleKeyDown}
              setInputValue={setInputValue}
              handleSuggestionClick={handleSuggestionClick}
              onStartNewConversation={handleStartNewConversation}
              onCreateInquiry={handleCreateInquiry}
              onQueryQuotation={handleQueryQuotation}
            />
          </div>
        </div>
      </Spin>
    </div>
  );
};

export default AIQuotation;
