import React, { useState, useEffect } from "react";
import {
  Layout,
  Card,
  Form,
  Button,
  Row,
  Col,
  Typography,
  Result,
  message,
  Steps,
} from "antd";
import { RocketOutlined, UpOutlined, HistoryOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "@/store/hooks";
import dayjs from "dayjs";
import { useWatch } from "antd/es/form/Form";
import "./index.less";

// 导入常量和类型
import {
  MOCK_HISTORY_RECORDS,
  MOCK_QUOTATION_RESULTS,
  QuotationFormValues,
} from "./constants";

// 导入自定义Hook
import useBaseData from "@/hooks/useBaseData";

// 导入组件
import {
  TransportInfoStep,
  BasicInfoStep,
  AdditionalInfoStep,
} from "./components/StepForms";
import HistoryRecords from "./components/HistoryRecords";
import QuotationResults from "./components/QuotationResults";
import LoadingIndicator from "./components/LoadingIndicator";

const { Content } = Layout;
const { Title } = Typography;
const { Step } = Steps;

const PersonalQuotation: React.FC = () => {
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [queryProgress, setQueryProgress] = useState(0);
  const [quotationResults, setQuotationResults] = useState<any[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [showHistory, setShowHistory] = useState(true);
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.user);
  const isValidity = useWatch("isvalidity", form);

  // 使用自定义Hook获取基础数据
  const {
    portOptions: portList,
    shipmentPlaceOptions: shipmentPlaceList,
    packageTypeOptions: packagetypeList,
    specialItemOptions: specialItemsList,
    loadAll,
  } = useBaseData();

  // 检查用户权限
  const hasAccess = user?.useridentity === 0;

  useEffect(() => {
    // 确保基础数据已加载
    loadAll();
  }, []);

  // 检查用户是否有权限访问此页面
  useEffect(() => {
    if (user && user.useridentity !== 0) {
      // 只有当用户已登录但权限不符时才显示错误消息
      message.error("您没有权限访问此页面");
      navigate("/");
    } else if (!user) {
      // 用户未登录时，直接重定向到登录页面，不显示错误消息
      navigate("/login");
    }
  }, [user, navigate]);

  // 模拟进度条
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (loading) {
      setQueryProgress(0);
      timer = setInterval(() => {
        setQueryProgress((prev) => {
          if (prev >= 100) {
            clearInterval(timer);
            return 100;
          }
          return prev + 5;
        });
      }, 100);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [loading]);

  // 处理表单提交
  const handleSubmit = (values: QuotationFormValues) => {
    console.log("提交的表单数据:", values);
    setLoading(true);

    // 模拟API请求
    setTimeout(() => {
      setQuotationResults(MOCK_QUOTATION_RESULTS);
      setShowResults(true);
      setLoading(false);
    }, 2500);
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    setShowResults(false);
    setQuotationResults([]);
    setCurrentStep(0);
  };

  // 处理下一步
  const handleNext = () => {
    form
      .validateFields()
      .then(() => {
        setCurrentStep(currentStep + 1);
      })
      .catch((err) => {
        console.log("表单验证失败:", err);
      });
  };

  // 处理上一步
  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
  };

  // 从历史记录中选择
  const handleSelectHistory = (record: any) => {
    form.setFieldsValue({
      ...record,
      shipmentDate: record.shipmentDate
        ? dayjs(record.shipmentDate, "YYYY-MM-DD")
        : undefined,
    });
    setShowHistory(false);
    message.success("已加载历史询价记录");
  };

  // 渲染步骤表单内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <TransportInfoStep
            form={form}
            currentStep={currentStep}
            onNext={handleNext}
            portList={portList}
            shipmentPlaceList={shipmentPlaceList}
            packagetypeList={packagetypeList}
            specialItemsList={specialItemsList}
            isValidity={isValidity}
          />
        );
      case 1:
        return (
          <BasicInfoStep
            form={form}
            currentStep={currentStep}
            onNext={handleNext}
            onPrev={handlePrev}
            portList={portList}
            shipmentPlaceList={shipmentPlaceList}
            packagetypeList={packagetypeList}
            specialItemsList={specialItemsList}
            isValidity={isValidity}
          />
        );
      case 2:
        return (
          <AdditionalInfoStep
            form={form}
            currentStep={currentStep}
            onPrev={handlePrev}
            onSubmit={handleSubmit}
            portList={portList}
            shipmentPlaceList={shipmentPlaceList}
            packagetypeList={packagetypeList}
            specialItemsList={specialItemsList}
            isValidity={isValidity}
          />
        );
      default:
        return null;
    }
  };

  // 如果用户已登录但没有权限，显示无权限页面
  if (user && !hasAccess) {
    return (
      <Layout className="personal-quotation-container">
        <Content className="personal-quotation-content">
          <Result
            status="403"
            title="无权访问"
            subTitle="抱歉，您没有权限访问此页面。"
            extra={
              <Button type="primary" onClick={() => navigate("/")}>
                返回首页
              </Button>
            }
          />
        </Content>
      </Layout>
    );
  }

  // 如果用户未登录，不显示任何内容，等待重定向到登录页面
  if (!user) {
    return null;
  }

  return (
    <div className="personal-quotation-container">
      {/* 页面标题 */}
      <div className="page-header">
        <div className="header-content">
          <RocketOutlined className="header-icon" />
          <Title level={2} className="header-title">
            个人报价查询
          </Title>
        </div>
        <div className="header-background"></div>
      </div>

      {/* 背景粒子效果 */}
      <div className="floating-particles">
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
      </div>

      <div className="personal-quotation-content">
        <Row gutter={24}>
          {/* 主要内容区域 */}
          <Col xs={24} md={24}>
            <Card className="quotation-card">
              <div className="card-header">
                <Title level={4} className="section-title">
                  报价查询
                </Title>
                {/* {!showResults && (
                <Button
                  type="link"
                  onClick={() => setShowHistory(!showHistory)}
                  icon={showHistory ? <UpOutlined /> : <HistoryOutlined />}
                >
                  {showHistory ? "隐藏历史记录" : "查看历史记录"}
                </Button>
              )} */}
              </div>

              {/* 步骤导航 */}
              <Steps current={currentStep} className="quotation-steps">
                <Step title="运输信息" description="港口与货物信息" />
                <Step title="基本信息" description="公司与贸易信息" />
                <Step title="附加信息" description="尺寸与特殊要求" />
              </Steps>

              {/* 表单 */}
              <Form
                form={form}
                layout="vertical"
                className="quotation-form"
                scrollToFirstError
                initialValues={{
                  isBrand: false,
                  ensureCabin: false,
                  isvalidity: false,
                }}
              >
                {renderStepContent()}
              </Form>
            </Card>
          </Col>

          {/* 历史记录区域 */}
          {/* {showHistory && !showResults && (
          <Col xs={24} md={6}>
            <HistoryRecords
              records={MOCK_HISTORY_RECORDS}
              onSelect={handleSelectHistory}
            />
          </Col>
        )} */}
        </Row>

        {/* 加载指示器 */}
        {loading && <LoadingIndicator progress={queryProgress} />}

        {/* 结果展示 */}
        {showResults && !loading && (
          <QuotationResults results={quotationResults} onReset={handleReset} />
        )}
      </div>
    </div>
  );
};

export default PersonalQuotation;
