import React, { useRef } from "react";
import { use<PERSON>rame } from "@react-three/fiber";
import * as THREE from "three";

// 高科技控制台网格背景
export const HolographicGrid: React.FC = () => {
  const gridRef = useRef<THREE.Group>(null);

  useFrame(({ clock }) => {
    if (gridRef.current) {
      // 添加轻微的动画效果
      gridRef.current.position.y =
        Math.sin(clock.getElapsedTime() * 0.2) * 0.05;
    }
  });

  return (
    <group ref={gridRef}>
      {/* 主网格 */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]}>
        <planeGeometry args={[40, 40, 40, 40]} />
        <meshStandardMaterial
          color="#52cafe"
          wireframe={true}
          transparent={true}
          opacity={0.2}
        />
      </mesh>

      {/* 辅助网格 - 垂直 */}
      <mesh rotation={[0, 0, 0]} position={[0, 0, -10]}>
        <planeGeometry args={[40, 10, 40, 10]} />
        <meshStandardMaterial
          color="#76c7f2"
          wireframe={true}
          transparent={true}
          opacity={0.1}
        />
      </mesh>

      {/* 辅助网格 - 水平 */}
      <mesh rotation={[0, Math.PI / 2, 0]} position={[-10, 0, 0]}>
        <planeGeometry args={[40, 10, 40, 10]} />
        <meshStandardMaterial
          color="#76c7f2"
          wireframe={true}
          transparent={true}
          opacity={0.1}
        />
      </mesh>
    </group>
  );
};

// 数据流效果
export const DataFlows: React.FC = () => {
  const flowsRef = useRef<THREE.Group>(null);

  // 创建数据流线
  const createDataFlow = (
    startX: number,
    startY: number,
    startZ: number,
    length: number,
    direction: "x" | "y" | "z",
    color: string,
    speed: number
  ) => {
    const flowRef = useRef<THREE.Mesh>(null);
    const initialPosition = new THREE.Vector3(startX, startY, startZ);

    useFrame(({ clock }) => {
      if (flowRef.current) {
        const time = clock.getElapsedTime() * speed;
        const position = initialPosition.clone();

        // 根据方向移动
        if (direction === "x") {
          position.x += (time % length) - length / 2;
        } else if (direction === "y") {
          position.y += (time % length) - length / 2;
        } else {
          position.z += (time % length) - length / 2;
        }

        flowRef.current.position.copy(position);

        // 透明度脉冲效果
        const opacity = ((Math.sin(time * 2) + 1) / 2) * 0.5 + 0.2;
        if (flowRef.current.material instanceof THREE.MeshBasicMaterial) {
          flowRef.current.material.opacity = opacity;
        }
      }
    });

    return (
      <mesh ref={flowRef} position={[startX, startY, startZ]}>
        <sphereGeometry args={[0.05, 8, 8]} />
        <meshBasicMaterial color={color} transparent={true} opacity={0.5} />
      </mesh>
    );
  };

  return (
    <group ref={flowsRef}>
      {/* 创建多个数据流 */}
      {createDataFlow(-5, 0, 0, 10, "x", "#52cafe", 1)}
      {createDataFlow(5, 0, 0, 10, "x", "#1797e1", 1.5)}
      {createDataFlow(0, -2, 0, 5, "y", "#76c7f2", 0.8)}
      {createDataFlow(0, 2, 0, 5, "y", "#40a9ff", 1.2)}
      {createDataFlow(0, 0, -5, 10, "z", "#52cafe", 1.3)}
      {createDataFlow(0, 0, 5, 10, "z", "#1797e1", 0.9)}

      {/* 对角线数据流 */}
      {createDataFlow(-5, -2, -5, 15, "x", "#52cafe", 1.1)}
      {createDataFlow(5, 2, 5, 15, "x", "#1797e1", 0.7)}
    </group>
  );
};

// 飞船控制台全息投影
export const SpaceshipHologram: React.FC = () => {
  const hologramRef = useRef<THREE.Group>(null);

  useFrame(({ clock }) => {
    if (hologramRef.current) {
      hologramRef.current.rotation.y = clock.getElapsedTime() * 0.2;
    }
  });

  return (
    <group ref={hologramRef} position={[0, 1, 0]}>
      {/* 中心控制台 */}
      <mesh>
        <torusGeometry args={[1.2, 0.2, 16, 50]} />
        <meshStandardMaterial
          color="#52cafe"
          transparent={true}
          opacity={0.3}
          wireframe={true}
        />
      </mesh>

      {/* 内部核心 */}
      <mesh>
        <octahedronGeometry args={[0.6, 0]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#52cafe"
          emissiveIntensity={0.5}
          transparent={true}
          opacity={0.7}
        />
      </mesh>

      {/* 环绕环 */}
      <mesh rotation={[Math.PI / 2, 0, 0]}>
        <ringGeometry args={[1.5, 1.6, 64]} />
        <meshBasicMaterial
          color="#1797e1"
          transparent={true}
          opacity={0.3}
          side={THREE.DoubleSide}
        />
      </mesh>

      {/* 第二个环绕环 */}
      <mesh rotation={[Math.PI / 3, 0, 0]}>
        <ringGeometry args={[1.8, 1.85, 64]} />
        <meshBasicMaterial
          color="#52cafe"
          transparent={true}
          opacity={0.2}
          side={THREE.DoubleSide}
        />
      </mesh>

      {/* 控制台支柱 */}
      <mesh position={[0, -1, 0]}>
        <cylinderGeometry args={[0.1, 0.3, 1, 16]} />
        <meshStandardMaterial color="#d0d9e2" metalness={0.8} roughness={0.2} />
      </mesh>
    </group>
  );
};

// 星光背景
export const StarBackground: React.FC = () => {
  return (
    <>
      <ambientLight intensity={0.3} />
      <directionalLight position={[10, 10, 5]} intensity={1.2} />
      <directionalLight
        position={[-10, -10, -5]}
        intensity={0.4}
        color="#40a9ff"
      />
      <spotLight
        position={[0, 10, 0]}
        angle={0.3}
        penumbra={1}
        intensity={0.8}
        color="#1797e1"
        castShadow
      />

      {/* 添加星星 */}
      {Array.from({ length: 200 }).map((_, i) => {
        const x = (Math.random() - 0.5) * 40;
        const y = (Math.random() - 0.5) * 40;
        const z = (Math.random() - 0.5) * 40;
        const size = Math.random() * 0.15 + 0.05;
        return (
          <mesh key={i} position={[x, y, z]}>
            <sphereGeometry args={[size, 16, 16]} />
            <meshStandardMaterial
              color="#40a9ff"
              emissive="#40a9ff"
              emissiveIntensity={3}
            />
          </mesh>
        );
      })}

      {/* 添加光线效果 */}
      <mesh rotation={[0, 0, Math.PI / 4]} position={[0, 0, -20]}>
        <planeGeometry args={[60, 60]} />
        <meshBasicMaterial
          color="#1797e1"
          transparent
          opacity={0.03}
          side={THREE.DoubleSide}
        />
      </mesh>
    </>
  );
};

// 完整的飞船控制台背景
const SpaceshipBackground: React.FC = () => {
  return (
    <>
      <StarBackground />
      <HolographicGrid />
      <DataFlows />
      <SpaceshipHologram />
    </>
  );
};

export default SpaceshipBackground;
