import React from "react";
import { Typography, Empty, List } from "antd";
import { DollarOutlined, InboxOutlined } from "@ant-design/icons";
import { Canvas } from "@react-three/fiber";
import { OrbitControls } from "@react-three/drei";
import CommandCenterBackground from "@/components/ThreeScene/CommandCenterBackground";
import { QuotationResult } from "@/pages/AIQuotation/types";
import QuotationCard from "@/pages/AIQuotation/components/QuotationCard";

const { Title, Text } = Typography;

interface SavedQuotationsProps {
  savedQuotations: QuotationResult[];
  onSelectQuotation: (quotation: QuotationResult) => void;
}

const SavedQuotations: React.FC<SavedQuotationsProps> = ({
  savedQuotations,
  onSelectQuotation,
}) => {
  return (
    <div className="saved-quotations-container">
      {/* 3D背景 */}
      <div className="canvas-container">
        <Canvas
          camera={{ position: [0, 0, 10], fov: 40 }}
          style={{ width: "100%", height: "100%" }}
          gl={{ antialias: true, alpha: true }}
        >
          <color attach="background" args={["#f0f7ff"]} />
          <fog attach="fog" args={["#f0f7ff", 15, 40]} />
          <CommandCenterBackground isHolographic={false} />
          <OrbitControls
            enableZoom={false}
            enablePan={false}
            enableRotate={true}
            autoRotate={true}
            autoRotateSpeed={0.5}
          />
        </Canvas>
      </div>

      {/* 列表头部 */}
      <div className="list-header">
        <div className="panel-title">
          <DollarOutlined className="panel-icon" />
          <Title level={4}>已保存报价</Title>
        </div>
      </div>

      {/* 列表内容区 */}
      <div className="list-content">
        {savedQuotations.length === 0 ? (
          <div className="empty-list">
            <div className="empty-icon">
              <InboxOutlined />
            </div>
            <Title level={4}>暂无保存的报价</Title>
            <Text>生成报价后可以保存到此处</Text>
          </div>
        ) : (
          <List
            dataSource={savedQuotations}
            renderItem={(item) => (
              <List.Item>
                <QuotationCard
                  item={item}
                  handleQuotationSelect={onSelectQuotation}
                  showActions={true}
                />
              </List.Item>
            )}
          />
        )}
      </div>
    </div>
  );
};

export default SavedQuotations;
