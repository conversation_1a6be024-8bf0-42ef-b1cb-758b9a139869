import { get, post, del } from "@/utils/request";

// 获取需要人工报价的询价列表
export const getManualQuotationList = (params: any) => {
  return get("/getManualQuotationList", { params });
};

// 保存人工报价单
export const saveManualQuotation = (data: any) => {
  return post("/saveManualQuotation", data);
};

// 更新询价状态
export const updateInquiryStatus = (data: any) => {
  return post("/updateInquiryStatus", data);
};

// 删除询价
export const deleteInquiry = (params: { inquiryid: string }) => {
  return del("/deleteInquiry", { params });
};
