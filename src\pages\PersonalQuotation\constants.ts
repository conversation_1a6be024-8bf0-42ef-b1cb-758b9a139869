import type { Dayjs } from "dayjs";

// 货运方式选项
export const MOCK_FREIGHT_METHODS = [
  { id: "AF", name: "空运" },
  { id: "SF", name: "海运" },
  { id: "LF", name: "陆运" },
];

// 模拟历史询价记录
export const MOCK_HISTORY_RECORDS = [
  {
    id: 1,
    title: "上海到东京空运询价",
    freightmethod: "AF",
    shippedplace: "SH",
    originport: "PVG",
    unloadingport: "NRT",
    date: "2023-05-15",
    companyname: "上海贸易有限公司",
    inquirer: "张三",
    grossweight: 500,
    goodsvolume: 2.5,
    packagetype: "CTN",
    goodstype: "LCL",
    tradeterms: "FOB",
  },
  {
    id: 2,
    title: "广州到新加坡海运询价",
    freightmethod: "SF",
    shippedplace: "GZ",
    originport: "CAN",
    unloadingport: "SIN",
    date: "2023-06-20",
    companyname: "广州国际贸易公司",
    inquirer: "李四",
    grossweight: 2000,
    goodsvolume: 10,
    packagetype: "PLT",
    goodstype: "FCL",
    tradeterms: "CIF",
  },
];

// 模拟报价结果数据
export const MOCK_QUOTATION_RESULTS = [
  {
    id: 1,
    airline: "中国国际航空",
    flightNo: "CA1234",
    departureTime: "2023-06-15 08:30",
    arrivalTime: "2023-06-15 11:30",
    price: 2580,
    currency: "CNY",
    transitTime: "3小时",
    available: true,
    route: "直飞",
    validUntil: "2023-07-15",
  },
  {
    id: 2,
    airline: "东方航空",
    flightNo: "MU5678",
    departureTime: "2023-06-15 10:15",
    arrivalTime: "2023-06-15 13:45",
    price: 2350,
    currency: "CNY",
    transitTime: "3小时30分钟",
    available: true,
    route: "直飞",
    validUntil: "2023-07-10",
  },
  {
    id: 3,
    airline: "南方航空",
    flightNo: "CZ9012",
    departureTime: "2023-06-15 14:20",
    arrivalTime: "2023-06-15 17:10",
    price: 2420,
    currency: "CNY",
    transitTime: "2小时50分钟",
    available: false,
    route: "经停广州",
    validUntil: "2023-07-20",
  },
];

// 定义表单字段接口
export interface QuotationFormValues {
  inquiryId?: number;
  freightmethod: string;
  goodstype: string;
  tradeterms: string;
  companyname: string;
  inquirer: string;
  inquiryTime?: string;
  shippedplace: string;
  originport: string;
  unloadingport: string;
  grossweight: number;
  goodsvolume: number;
  isBrand: boolean;
  ensureCabin: boolean;
  isvalidity: boolean;
  shipmentDate?: Dayjs;
  packagetype: string;
  cargoLength?: number;
  cargoWidth?: number;
  cargoHeight?: number;
  specialCargo?: string;
}
