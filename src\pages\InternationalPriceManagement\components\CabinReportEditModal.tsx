import React, { useEffect } from "react";
import { Modal, Form, Button, message } from "antd";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { InternationalPrice } from "../types";
import { updateInternationalPrice } from "../services";
import CabinReportForm from "./CabinReportForm";
import "./index.less";

interface CabinReportEditModalProps {
  visible: boolean;
  onCancel: () => void;
  record: InternationalPrice | null;
  onSuccess: () => void;
}

const CabinReportEditModal: React.FC<CabinReportEditModalProps> = ({
  visible,
  onCancel,
  record,
  onSuccess,
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 格式化日期为时间戳
  const formatDateToTimestamp = (date: any) => {
    if (!date) return null;
    if (dayjs.isDayjs(date)) {
      return date.valueOf();
    }
    if (typeof date === "string" || typeof date === "number") {
      return dayjs(date).valueOf();
    }
    return null;
  };

  useEffect(() => {
    if (visible && record) {
      const cabinReportData = record.cabinreport || [];
      // 转换日期格式为dayjs对象
      const formattedCabinReport = cabinReportData.map((cabin: any) => ({
        ...cabin,
        date: cabin.date ? dayjs(cabin.date) : null,
        transferdate: cabin.transferdate ? dayjs(cabin.transferdate) : null,
        pricechanges: cabin.pricechanges || [],
        specialprice: cabin.specialprice || [],
      }));

      form.setFieldsValue({
        cabinreport: formattedCabinReport,
      });
    }
  }, [visible, record, form]);

  const handleSave = async () => {
    try {
      const values = await form.validateFields();

      // 处理舱报信息，转换日期为时间戳
      const updatedCabinReport =
        values.cabinreport?.map((cabin: any) => ({
          ...cabin,
          date: formatDateToTimestamp(cabin.date),
          transferdate: cabin?.transferdate
            ? formatDateToTimestamp(cabin?.transferdate)
            : undefined,
          pricechanges: cabin.pricechanges || [],
          specialprice: cabin.specialprice || [],
        })) || [];

      if (!record) {
        message.error("记录信息不存在");
        return;
      }

      const updateData = {
        ...record,
        cabinreport: updatedCabinReport,
      };

      await updateInternationalPrice(updateData);
      message.success(
        t("internationalPriceManagement.messages.cabinReportUpdateSuccess")
      );
      onSuccess();
      onCancel();
    } catch (error) {
      console.error("更新舱报信息失败:", error);
      message.error(
        t("internationalPriceManagement.messages.cabinReportUpdateFailed")
      );
    }
  };

  return (
    <Modal
      title={t("internationalPriceManagement.cabinReportEdit.title")}
      open={visible}
      onCancel={onCancel}
      width={1200}
      centered
      className="cabin-report-edit-modal"
      footer={[
        <Button key="cancel" onClick={onCancel}>
          {t("common.cancel")}
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          {t("common.save")}
        </Button>,
      ]}
      destroyOnClose
    >
      <Form form={form} layout="vertical" className="cabin-report-edit-form">
        <CabinReportForm
          form={form}
          isTransfer={record?.istransfer || false}
          originSchedules={record?.originschedules}
        />
      </Form>
    </Modal>
  );
};

export default CabinReportEditModal;
