import React from "react";
import {
  Modal,
  Tag,
  Divider,
  Typography,
  Row,
  Col,
  Card,
  Badge,
  Table,
  Empty,
} from "antd";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  GlobalOutlined,
  DollarCircleOutlined,
  BoxPlotOutlined,
  RocketOutlined,
  EnvironmentOutlined,
  CalendarOutlined,
  DatabaseOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import "./index.less";
import { useTranslation } from "react-i18next";
import type { IntervalPrice } from "../types";

const { Text } = Typography;

interface DetailModalProps {
  open: boolean;
  onClose: () => void;
  record: any;
}

const DetailModal: React.FC<DetailModalProps> = ({ open, onClose, record }) => {
  const { t } = useTranslation();

  if (!record) return null;

  const formatDate = (dateString: string) => {
    if (!dateString)
      return t("internationalPriceManagement.detailModal.noData");
    return dayjs(dateString).format("YYYY-MM-DD");
  };

  const formatBoolean = (value: boolean) => {
    return value ? (
      <Tag color="success" icon={<CheckCircleOutlined />}>
        {t("internationalPriceManagement.detailModal.booleanValues.yes")}
      </Tag>
    ) : (
      <Tag color="default" icon={<CloseCircleOutlined />}>
        {t("internationalPriceManagement.detailModal.booleanValues.no")}
      </Tag>
    );
  };

  // 渲染价格梯度
  const renderPriceTier = (
    title: string,
    subtitle: string,
    color: string,
    prices: any
  ) => {
    return (
      <Col xs={24} sm={12} md={8} lg={8} xl={6} xxl={6}>
        <div className="price-tier">
          <div className="tier-header">
            <Badge color={color} text={title} />
            <Text type="secondary">{subtitle}</Text>
          </div>
          <div className="tier-content">
            {Object.entries(prices).map(([key, value]: [string, any]) => (
              <div key={key} className="tier-item">
                <Text>{key}:</Text>
                <Text className="tier-value">
                  {value || "-"}{" "}
                  {value
                    ? t(
                        "internationalPriceManagement.detailModal.units.cnyPerKg"
                      )
                    : ""}
                </Text>
              </div>
            ))}
          </div>
        </div>
      </Col>
    );
  };

  // 渲染密度价格区间表格
  const renderIntervalPriceTable = () => {
    if (!record.intervalprice || record.intervalprice.length === 0) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={t("internationalPriceManagement.detailModal.noData")}
        />
      );
    }

    const columns = [
      {
        title: t("internationalPriceManagement.columns.densityRange"),
        dataIndex: "densityRange",
        key: "densityRange",
        width: 150,
        render: (_: any, intervalRecord: IntervalPrice) => (
          <Tag color="blue">
            {intervalRecord.densitylvalue} - {intervalRecord.densityrvalue}{" "}
            kg/m³
          </Tag>
        ),
      },
      {
        title: t("internationalPriceManagement.columns.q100Price"),
        dataIndex: "q100",
        key: "q100",
        width: 100,
        render: (value: number) => (
          <span>
            {value || "-"} {value ? "CNY/KG" : ""}
          </span>
        ),
      },
      {
        title: t("internationalPriceManagement.columns.q300Price"),
        dataIndex: "q300",
        key: "q300",
        width: 100,
        render: (value: number) => (
          <span>
            {value || "-"} {value ? "CNY/KG" : ""}
          </span>
        ),
      },
      {
        title: t("internationalPriceManagement.columns.q500Price"),
        dataIndex: "q500",
        key: "q500",
        width: 100,
        render: (value: number) => (
          <span>
            {value || "-"} {value ? "CNY/KG" : ""}
          </span>
        ),
      },
      {
        title: t("internationalPriceManagement.columns.q1000Price"),
        dataIndex: "q1000",
        key: "q1000",
        width: 120,
        render: (value: number) => (
          <span>
            {value || "-"} {value ? "CNY/KG" : ""}
          </span>
        ),
      },
    ];

    return (
      <Table
        columns={columns}
        dataSource={record.intervalprice.map(
          (item: IntervalPrice, index: number) => ({
            ...item,
            key: index,
          })
        )}
        pagination={false}
        size="small"
        bordered
        className="interval-price-table"
      />
    );
  };

  return (
    <Modal
      title={
        <div className="inter-price-detail-modal">
          <span className="title-icon">
            <InfoCircleOutlined />
          </span>
          {t("internationalPriceManagement.detailModal.title")}
          {record && (
            <Tag color="blue" className="price-id-tag">
              ID: {record.priceid}
            </Tag>
          )}
        </div>
      }
      open={open}
      onCancel={onClose}
      footer={null}
      width={1000}
      className="inter-price-detail-modal"
      destroyOnClose
      centered
    >
      <div className="price-detail-content">
        <Card className="detail-card">
          <div className="section-title">
            <GlobalOutlined />{" "}
            {t("internationalPriceManagement.detailModal.sections.basicInfo")}
          </div>
          <Row gutter={[12, 8]}>
            <Col xs={24} sm={12} md={6}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "internationalPriceManagement.detailModal.fields.supplierName"
                  )}
                </Text>
                <div className="info-value">
                  {record.suppliername ||
                    t("internationalPriceManagement.detailModal.noData")}
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "internationalPriceManagement.detailModal.fields.createTime"
                  )}
                </Text>
                <div className="info-value">
                  {formatDate(record.pricecreatetime)}
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "internationalPriceManagement.detailModal.fields.updateTime"
                  )}
                </Text>
                <div className="info-value">
                  {formatDate(record.priceupdatetime)}
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "internationalPriceManagement.detailModal.fields.effectiveTime"
                  )}
                </Text>
                <div className="info-value">
                  {formatDate(record.effectivetime)}
                </div>
              </div>
            </Col>
          </Row>
        </Card>

        <Card className="detail-card">
          <div className="section-title">
            <RocketOutlined />{" "}
            {t("internationalPriceManagement.detailModal.sections.routeInfo")}
          </div>
          <Row gutter={[12, 8]}>
            <Col xs={24} sm={12} md={6}>
              <div className="info-item">
                <Text type="secondary">
                  {t("internationalPriceManagement.detailModal.fields.airline")}
                </Text>
                <div className="info-value">
                  <Tag color="blue">
                    {record.airlinename ||
                      t("internationalPriceManagement.detailModal.noData")}
                  </Tag>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "internationalPriceManagement.detailModal.fields.originPort"
                  )}
                </Text>
                <div className="info-value">
                  <Tag color="green" icon={<EnvironmentOutlined />}>
                    {record.originport ||
                      t("internationalPriceManagement.detailModal.noData")}
                  </Tag>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "internationalPriceManagement.detailModal.fields.originSchedules"
                  )}
                </Text>
                <div className="info-value">
                  {record.originschedules ||
                    t("internationalPriceManagement.detailModal.noData")}
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "internationalPriceManagement.detailModal.fields.isTransfer"
                  )}
                </Text>
                <div className="info-value">
                  {formatBoolean(record.istransfer)}
                </div>
              </div>
            </Col>
            <Col xs={24} sm={24} md={12}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "internationalPriceManagement.detailModal.fields.destinationPort"
                  )}
                </Text>
                <div className="info-value">
                  {record?.unloadingport?.split("/").map((item: string) => (
                    <Tag
                      key={item}
                      color="orange"
                      icon={<EnvironmentOutlined />}
                      style={{ marginBottom: 2, marginRight: 4 }}
                    >
                      {item}
                    </Tag>
                  ))}
                </div>
              </div>
            </Col>
            {record.istransfer && (
              <>
                <Col xs={24} sm={12} md={8}>
                  <div className="info-item">
                    <Text type="secondary">
                      {t(
                        "internationalPriceManagement.detailModal.fields.transferPort"
                      )}
                    </Text>
                    <div className="info-value">
                      <Tag color="purple" icon={<EnvironmentOutlined />}>
                        {record.transfer ||
                          t("internationalPriceManagement.detailModal.noData")}
                      </Tag>
                    </div>
                  </div>
                </Col>
                <Col xs={24} sm={12} md={8}>
                  <div className="info-item">
                    <Text type="secondary">
                      {t(
                        "internationalPriceManagement.detailModal.fields.transferSchedules"
                      )}
                    </Text>
                    <div className="info-value">
                      {record.transferschedules ||
                        t("internationalPriceManagement.detailModal.noData")}
                    </div>
                  </div>
                </Col>
              </>
            )}
          </Row>
        </Card>

        <Card className="detail-card">
          <div className="section-title">
            <BoxPlotOutlined />{" "}
            {t("internationalPriceManagement.detailModal.sections.cargoLimits")}
          </div>
          <Row gutter={[12, 8]}>
            <Col xs={24} sm={12} md={6}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "internationalPriceManagement.detailModal.fields.minDensity"
                  )}
                </Text>
                <div className="info-value">
                  {record?.mindensity ||
                    t("internationalPriceManagement.detailModal.noData")}{" "}
                  {record?.mindensity
                    ? t(
                        "internationalPriceManagement.detailModal.units.kgPerCubicMeter"
                      )
                    : ""}
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "internationalPriceManagement.detailModal.fields.maxDensity"
                  )}
                </Text>
                <div className="info-value">
                  {record?.maxdensity ||
                    t("internationalPriceManagement.detailModal.noData")}{" "}
                  {record?.maxdensity
                    ? t(
                        "internationalPriceManagement.detailModal.units.kgPerCubicMeter"
                      )
                    : ""}
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "internationalPriceManagement.detailModal.fields.lengthLimit"
                  )}
                </Text>
                <div className="info-value">
                  {record?.lengthlimit ||
                    t("internationalPriceManagement.detailModal.noData")}
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "internationalPriceManagement.detailModal.fields.widthLimit"
                  )}
                </Text>
                <div className="info-value">
                  {record.widthlimit ||
                    t("internationalPriceManagement.detailModal.noData")}
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "internationalPriceManagement.detailModal.fields.heightLimit"
                  )}
                </Text>
                <div className="info-value">
                  {record.heightlimit ||
                    t("internationalPriceManagement.detailModal.noData")}
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "internationalPriceManagement.detailModal.fields.singleWeightLimit"
                  )}
                </Text>
                <div className="info-value">
                  {record.singleweightlimit || record.singleweightlimit === 0
                    ? `${record.singleweightlimit}kg`
                    : t("internationalPriceManagement.detailModal.noData")}
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "internationalPriceManagement.detailModal.fields.acceptBrand"
                  )}
                </Text>
                <div className="info-value">
                  {formatBoolean(record.acceptbrand)}
                </div>
              </div>
            </Col>
          </Row>
        </Card>

        <Card className="detail-card">
          <div className="section-title">
            <DollarCircleOutlined />{" "}
            {t("internationalPriceManagement.detailModal.sections.priceInfo")}
          </div>
          <Row gutter={[12, 8]}>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">
                  {t("internationalPriceManagement.detailModal.fields.isCabin")}
                </Text>
                <div className="info-value">
                  {formatBoolean(record.iscabin)}
                </div>
              </div>
            </Col>
            {record.iscabin && (
              <Col xs={24} sm={12} md={8}>
                <div className="info-item">
                  <Text type="secondary">
                    {t(
                      "internationalPriceManagement.detailModal.fields.cabinPrice"
                    )}
                  </Text>
                  <div className="info-value">
                    {record?.cabinprice || "-"}{" "}
                    {record?.cabinprice
                      ? t("internationalPriceManagement.detailModal.units.cny")
                      : ""}
                  </div>
                </div>
              </Col>
            )}
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "internationalPriceManagement.detailModal.fields.smallCargoFee"
                  )}
                </Text>
                <div className="info-value">
                  {record?.smallcargofee || "-"}{" "}
                  {record?.smallcargofee
                    ? t("internationalPriceManagement.detailModal.units.cny")
                    : ""}
                </div>
              </div>
            </Col>
          </Row>

          {/* 密度价格区间表格 */}
          {record.intervalprice && record.intervalprice.length > 0 && (
            <>
              <Divider orientation="left">
                <DatabaseOutlined style={{ marginRight: 8 }} />
                密度价格区间
              </Divider>
              {renderIntervalPriceTable()}
            </>
          )}

          {/* 传统价格梯度展示 */}
          <Divider orientation="left">
            {t("internationalPriceManagement.detailModal.sections.priceTiers")}
          </Divider>
          <Row gutter={[8, 8]}>
            {renderPriceTier(
              t("internationalPriceManagement.detailModal.priceTypes.mPrice"),
              t(
                "internationalPriceManagement.detailModal.priceTypes.standardPrice"
              ),
              "#1890ff",
              {
                [t(
                  "internationalPriceManagement.detailModal.priceLabels.standard"
                )]: record.mprice,
              }
            )}
            {renderPriceTier(
              t("internationalPriceManagement.detailModal.priceTypes.nPrice"),
              t(
                "internationalPriceManagement.detailModal.priceTypes.basePrice"
              ),
              "#52c41a",
              {
                [t(
                  "internationalPriceManagement.detailModal.priceLabels.base"
                )]: record.nprice,
              }
            )}
            {renderPriceTier(
              t("internationalPriceManagement.detailModal.priceTypes.q45Price"),
              t(
                "internationalPriceManagement.detailModal.priceTypes.above45kg"
              ),
              "#722ed1",
              {
                [t(
                  "internationalPriceManagement.detailModal.priceLabels.standardTier"
                )]: record.q45price,
                ...(record.q45_sub
                  ? {
                      [t(
                        "internationalPriceManagement.detailModal.priceLabels.segment"
                      )]: record.subq45price,
                    }
                  : {}),
              }
            )}
          </Row>

          {Array.isArray(record.specialcharges) &&
            record.specialcharges.length > 0 && (
              <>
                <Divider orientation="left">
                  {t(
                    "internationalPriceManagement.detailModal.sections.specialCharges"
                  )}
                </Divider>
                <Row gutter={[8, 6]}>
                  {record.specialcharges.map((item: any, index: number) => (
                    <Col key={index} xs={24} sm={12} md={8} lg={6}>
                      <div className="info-item">
                        <Text type="secondary">{item.specialItem}</Text>
                        <div className="info-value">
                          <Tag color="red">+{item.value}</Tag>
                        </div>
                      </div>
                    </Col>
                  ))}
                </Row>
              </>
            )}

          {Array.isArray(record.packagecharges) &&
            record.packagecharges.length > 0 && (
              <>
                <Divider orientation="left">
                  {t(
                    "internationalPriceManagement.detailModal.sections.packageCharges"
                  )}
                </Divider>
                <Row gutter={[8, 6]}>
                  {record.packagecharges.map((item: any, index: number) => (
                    <Col key={index} xs={24} sm={12} md={8} lg={6}>
                      <div className="info-item">
                        <Text type="secondary">{item.packageItem}</Text>
                        <div className="info-value">
                          <Tag color="red">+{item.value}</Tag>
                        </div>
                      </div>
                    </Col>
                  ))}
                </Row>
              </>
            )}
        </Card>

        {/* 舱报信息展示 */}
        {Array.isArray(record.cabinreport) && record.cabinreport.length > 0 && (
          <Card className="detail-card">
            <div className="section-title">
              <CalendarOutlined /> 舱报信息
            </div>
            {record.cabinreport.map((cabin: any, index: number) => (
              <div key={index} className="cabin-report-detail">
                <div className="cabin-header">
                  <Tag color="blue">
                    {dayjs(cabin.date).format("YYYY-MM-DD")}
                  </Tag>
                  {cabin.transferdate && (
                    <Tag color="purple">
                      中转: {dayjs(cabin.transferdate).format("YYYY-MM-DD")}
                    </Tag>
                  )}
                  <div className="cabin-basic-info">
                    <span>重量限制: {cabin.weightlimit}kg</span>
                    <span>体积限制: {cabin.volumelimit}m³</span>
                    <span>
                      密度范围: {cabin.lowerdensity}-{cabin.upperdensity} kg/m³
                    </span>
                  </div>
                </div>

                {/* 价格变化信息 */}
                {Array.isArray(cabin.pricechanges) &&
                  cabin.pricechanges.length > 0 && (
                    <div className="price-changes">
                      <Text strong>价格变化:</Text>
                      <div className="price-change-list">
                        {cabin.pricechanges.map(
                          (change: any, changeIndex: number) => (
                            <Tag key={changeIndex} color="orange">
                              {change.leftdensity}-{change.rightdensity} kg/m³:{" "}
                              {change.pchanges > 0 ? "+" : ""}
                              {change.pchanges}
                            </Tag>
                          )
                        )}
                      </div>
                    </div>
                  )}

                {/* 特价找货信息 */}
                {Array.isArray(cabin.specialprice) &&
                  cabin.specialprice.length > 0 && (
                    <div className="special-prices">
                      <Text strong>特价找货:</Text>
                      <div className="special-price-list">
                        {cabin.specialprice.map(
                          (special: any, specialIndex: number) => (
                            <Tag key={specialIndex} color="green">
                              {special.densitylowerlimit}-
                              {special.densityupperlimit} kg/m³:{" "}
                              {special.sprice} CNY/KG
                            </Tag>
                          )
                        )}
                      </div>
                    </div>
                  )}
              </div>
            ))}
          </Card>
        )}
      </div>
    </Modal>
  );
};

export default DetailModal;
