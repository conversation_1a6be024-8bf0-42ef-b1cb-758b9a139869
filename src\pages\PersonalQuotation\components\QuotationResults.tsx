import React from "react";
import { Card, Typography, Table, Button } from "antd";
import { DollarOutlined } from "@ant-design/icons";
import { getQuotationColumns } from "./tableColumns";

const { Title, Text } = Typography;

interface QuotationResultsProps {
  results: any[];
  onReset: () => void;
}

const QuotationResults: React.FC<QuotationResultsProps> = ({
  results,
  onReset,
}) => {
  const columns = getQuotationColumns();

  return (
    <Card className="results-card">
      <div className="results-header">
        <Title level={4}>
          <DollarOutlined /> 报价结果
        </Title>
        <Text>共找到 {results.length} 条报价信息</Text>
      </div>
      <Table
        columns={columns}
        dataSource={results}
        rowKey="id"
        pagination={{
          pageSize: 5,
          showSizeChanger: true,
          pageSizeOptions: ["5", "10", "20"],
          showTotal: (total) => `共 ${total} 条记录`,
        }}
        className="quotation-table"
        size="middle"
        scroll={{ x: "max-content" }}
      />
      <div className="results-actions">
        <Button type="primary" onClick={onReset}>
          重新查询
        </Button>
      </div>
    </Card>
  );
};

export default QuotationResults;
