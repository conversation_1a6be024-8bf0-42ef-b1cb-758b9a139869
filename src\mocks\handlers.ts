import { http, HttpResponse } from "msw";
import { v4 as uuidv4 } from "uuid";
import dayjs from "dayjs";

// 用户类型定义
interface UserType {
  id: string;
  fullname: string;
  email: string;
  telephone: string;
  department: string;
  role: string;
  isvalid: boolean;
  createtime: string;
  recentlogintime: string | null;
}

// 模拟用户数据
const mockUsers: UserType[] = [
  {
    id: uuidv4(),
    fullname: "系统管理员",
    email: "<EMAIL>",
    telephone: "13800138000",
    department: "技术部",
    role: "管理员",
    isvalid: true,
    createtime: dayjs().subtract(180, "day").format("YYYY-MM-DD"),
    recentlogintime: dayjs().format("YYYY-MM-DD"),
  },
  {
    id: uuidv4(),
    fullname: "张三",
    email: "zhang<PERSON>@example.com",
    telephone: "13800138001",
    department: "销售部",
    role: "销售经理",
    isvalid: true,
    createtime: dayjs().subtract(150, "day").format("YYYY-MM-DD"),
    recentlogintime: dayjs().subtract(2, "day").format("YYYY-MM-DD"),
  },
  {
    id: uuidv4(),
    fullname: "李四",
    email: "<EMAIL>",
    telephone: "13800138002",
    department: "财务部",
    role: "财务专员",
    isvalid: false,
    createtime: dayjs().subtract(120, "day").format("YYYY-MM-DD"),
    recentlogintime: dayjs().subtract(30, "day").format("YYYY-MM-DD"),
  },
  {
    id: uuidv4(),
    fullname: "王五",
    email: "<EMAIL>",
    telephone: "13800138003",
    department: "运营部",
    role: "运营专员",
    isvalid: true,
    createtime: dayjs().subtract(90, "day").format("YYYY-MM-DD"),
    recentlogintime: dayjs().subtract(5, "day").format("YYYY-MM-DD"),
  },
  {
    id: uuidv4(),
    fullname: "赵六",
    email: "<EMAIL>",
    telephone: "13800138004",
    department: "客服部",
    role: "客服主管",
    isvalid: true,
    createtime: dayjs().subtract(60, "day").format("YYYY-MM-DD"),
    recentlogintime: dayjs().subtract(1, "day").format("YYYY-MM-DD"),
  },
];

export const handlers = [
  // 获取用户列表
  http.get("http://***************:10086/v1/users", () => {
    return HttpResponse.json(
      {
        data: mockUsers,
        resultCode: 200,
        message: "获取用户列表成功",
      },
      { status: 200 }
    );
  }),

  // 添加用户
  http.post("http://localhost:3000/users", async ({ request }) => {
    const userData = (await request.json()) as Partial<UserType>;

    // 确保必填字段存在
    if (
      !userData.fullname ||
      !userData.email ||
      !userData.telephone ||
      !userData.department ||
      !userData.role
    ) {
      return HttpResponse.json(
        {
          resultCode: 400,
          message: "缺少必要的用户信息",
        },
        { status: 400 }
      );
    }

    const newUser: UserType = {
      id: uuidv4(),
      fullname: userData.fullname,
      email: userData.email,
      telephone: userData.telephone,
      department: userData.department,
      role: userData.role,
      isvalid: userData.isvalid ?? true,
      createtime: dayjs().format("YYYY-MM-DD"),
      recentlogintime: null,
    };

    mockUsers.push(newUser);

    return HttpResponse.json(
      {
        data: newUser,
        resultCode: 200,
        message: "添加用户成功",
      },
      { status: 200 }
    );
  }),

  // 更新用户
  http.post("http://localhost:3000/users/:id", async ({ request, params }) => {
    const { id } = params;
    const userData = (await request.json()) as Partial<UserType>;

    const userIndex = mockUsers.findIndex((user) => user.id === id);
    if (userIndex === -1) {
      return HttpResponse.json(
        {
          resultCode: 404,
          message: "用户不存在",
        },
        { status: 404 }
      );
    }

    // 更新用户信息，保留原有字段
    mockUsers[userIndex] = {
      ...mockUsers[userIndex],
      ...(userData as Partial<UserType>),
    };

    return HttpResponse.json(
      {
        data: mockUsers[userIndex],
        resultCode: 200,
        message: "更新用户成功",
      },
      { status: 200 }
    );
  }),

  // 删除用户
  http.delete("http://localhost:3000/users/:id", ({ params }) => {
    const { id } = params;

    const userIndex = mockUsers.findIndex((user) => user.id === id);
    if (userIndex === -1) {
      return HttpResponse.json(
        {
          resultCode: 404,
          message: "用户不存在",
        },
        { status: 404 }
      );
    }

    mockUsers.splice(userIndex, 1);

    return HttpResponse.json(
      {
        resultCode: 200,
        message: "删除用户成功",
      },
      { status: 200 }
    );
  }),

  // 重置密码
  http.post(
    "http://localhost:3000/users/:id/reset-password",
    async ({ params }) => {
      const { id } = params;

      const userIndex = mockUsers.findIndex((user) => user.id === id);
      if (userIndex === -1) {
        return HttpResponse.json(
          {
            resultCode: 404,
            message: "用户不存在",
          },
          { status: 404 }
        );
      }

      return HttpResponse.json(
        {
          resultCode: 200,
          message: "密码重置成功",
        },
        { status: 200 }
      );
    }
  ),

  // 更新用户状态
  http.post(
    "http://localhost:3000/users/:id/status",
    async ({ request, params }) => {
      const { id } = params;
      const data = (await request.json()) as { isvalid: boolean };

      const userIndex = mockUsers.findIndex((user) => user.id === id);
      if (userIndex === -1) {
        return HttpResponse.json(
          {
            resultCode: 404,
            message: "用户不存在",
          },
          { status: 404 }
        );
      }

      mockUsers[userIndex].isvalid = data.isvalid;

      return HttpResponse.json(
        {
          data: mockUsers[userIndex],
          resultCode: 200,
          message: `用户已${data.isvalid ? "启用" : "禁用"}`,
        },
        { status: 200 }
      );
    }
  ),

  http.post(
    "http://***************:10086/v1/api/login",
    async ({ request }) => {
      const { email, password } = (await request.json()) as {
        email: string;
        password: string;
      };

      //判断账号和密码是否对应
      if (email === "admin" && password === "admin") {
        return HttpResponse.json({
          code: 200,
          message: `Welcome, ${email}!`,
          data: {
            token: uuidv4(),
          },
        });
      } else if (email === "account" && password === "account") {
        return HttpResponse.json({
          code: 200,
          message: `Welcome, ${email}!`,
          data: {
            token: uuidv4(),
          },
        });
      } else {
        return HttpResponse.json({
          code: 400,
          message: "登录失败",
        });
      }
    }
  ),

  // 发送邮箱验证码
  http.post("/sendEmailVerificationCode", async ({ request }) => {
    const { email } = (await request.json()) as { email: string };

    // 检查邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return HttpResponse.json({
        resultCode: 400,
        message: "邮箱格式不正确",
      });
    }

    // 模拟发送验证码
    return HttpResponse.json({
      resultCode: 200,
      message: "验证码发送成功",
      data: {
        email,
        // 实际应用中，验证码应该发送到用户邮箱，而不是返回给前端
        // 这里仅作为模拟
        verificationCode: "123456",
      },
    });
  }),

  // 用户注册
  http.post("/registerUser", async ({ request }) => {
    const userData = (await request.json()) as {
      email: string;
      password: string;
      verificationCode: string;
    };

    // 检查用户名是否已存在
    const existingUser = mockUsers.find(
      (user) => user.email === userData.email
    );
    if (existingUser) {
      return HttpResponse.json({
        resultCode: 400,
        message: "用户名已存在",
      });
    }

    // 检查邮箱是否已存在
    const existingEmail = mockUsers.find(
      (user) => user.email === userData.email
    );
    if (existingEmail) {
      return HttpResponse.json({
        resultCode: 400,
        message: "邮箱已被注册",
      });
    }

    // 验证验证码 (实际应用中应该与存储在服务器的验证码比对)
    // 这里简化处理，假设验证码是123456
    if (userData.verificationCode !== "123456") {
      return HttpResponse.json({
        resultCode: 400,
        message: "验证码不正确",
      });
    }

    // 创建新用户
    const newUser = {
      id: uuidv4(),
      email: userData.email,
      fullname: userData.email, // 默认使用用户名作为全名
      telephone: "",
      department: "未分配",
      role: "普通用户",
      isvalid: true,
      createtime: dayjs().format("YYYY-MM-DD"),
      recentlogintime: null,
    };

    mockUsers.push(newUser);

    return HttpResponse.json({
      resultCode: 200,
      message: "注册成功",
      data: {
        email: newUser.email,
      },
    });
  }),

  http.get("http://***************:10086/v1/api/quotation", () => {
    return HttpResponse.json(
      {
        data: [
          {
            inquiryid: uuidv4(),
            originport: "北京",
            unloadingport: "成都",
            grossweight: "7pkgs",
            goodsvolume: "27.5kgs",
            cargo_volume: "0.51cbm",
            isvalidity: "2025-04-15",
            ensurecabin: "否",
            isbrand: "否",
            packagetype: "木箱",
            cargosize: "0.5*0.5*0.5",
            specialcargo: "锂电",
            shipmentdate: "2025-04-15",
          },
          {
            inquiryid: uuidv4(),
            originport: "北京",
            unloadingport: "成都",
            grossweight: "7pkgs",
            goodsvolume: "27.5kgs",
            cargo_volume: "0.51cbm",
            isvalidity: "2025-04-15",
            ensurecabin: "否",
            isbrand: "否",
            packagetype: "木箱",
            cargosize: "0.5*0.5*0.5",
            specialcargo: "锂电",
            shipmentdate: "2025-04-15",
          },
          {
            inquiryid: uuidv4(),
            originport: "北京",
            unloadingport: "成都",
            grossweight: "7pkgs",
            goodsvolume: "27.5kgs",
            cargo_volume: "0.51cbm",
            isvalidity: "2025-04-15",
            ensurecabin: "否",
            isbrand: "否",
            packagetype: "木箱",
            cargosize: "0.5*0.5*0.5",
            specialcargo: "锂电",
            shipmentdate: "2025-04-15",
          },
          {
            inquiryid: uuidv4(),
            originport: "北京",
            unloadingport: "成都",
            grossweight: "7pkgs",
            goodsvolume: "27.5kgs",
            cargo_volume: "0.51cbm",
            isvalidity: "2025-04-15",
            ensurecabin: "否",
            isbrand: "否",
            packagetype: "木箱",
            cargosize: "0.5*0.5*0.5",
            specialcargo: "锂电",
            shipmentdate: "2025-04-15",
          },
          {
            inquiryid: uuidv4(),
            originport: "北京",
            unloadingport: "成都",
            grossweight: "7pkgs",
            goodsvolume: "27.5kgs",
            cargo_volume: "0.51cbm",
            isvalidity: "2025-04-15",
            ensurecabin: "否",
            isbrand: "否",
            packagetype: "木箱",
            cargosize: "0.5*0.5*0.5",
            specialcargo: "锂电",
            shipmentdate: "2025-04-15",
          },
          {
            inquiryid: uuidv4(),
            originport: "北京",
            unloadingport: "成都",
            grossweight: "7pkgs",
            goodsvolume: "27.5kgs",
            cargo_volume: "0.51cbm",
            isvalidity: "2025-04-15",
            ensurecabin: "否",
            isbrand: "否",
            packagetype: "木箱",
            cargosize: "0.5*0.5*0.5",
            specialcargo: "锂电",
            shipmentdate: "2025-04-15",
          },
          {
            inquiryid: uuidv4(),
            originport: "北京",
            unloadingport: "成都",
            grossweight: "7pkgs",
            goodsvolume: "27.5kgs",
            cargo_volume: "0.51cbm",
            isvalidity: "2025-04-15",
            ensurecabin: "否",
            isbrand: "否",
            packagetype: "木箱",
            cargosize: "0.5*0.5*0.5",
            specialcargo: "锂电",
            shipmentdate: "2025-04-15",
          },
          {
            inquiryid: uuidv4(),
            originport: "北京",
            unloadingport: "成都",
            grossweight: "7pkgs",
            goodsvolume: "27.5kgs",
            cargo_volume: "0.51cbm",
            isvalidity: "2025-04-15",
            ensurecabin: "否",
            isbrand: "否",
            packagetype: "木箱",
            cargosize: "0.5*0.5*0.5",
            specialcargo: "锂电",
            shipmentdate: "2025-04-15",
          },
          {
            inquiryid: uuidv4(),
            originport: "北京",
            unloadingport: "成都",
            grossweight: "7pkgs",
            goodsvolume: "27.5kgs",
            cargo_volume: "0.51cbm",
            isvalidity: "2025-04-15",
            ensurecabin: "否",
            isbrand: "否",
            packagetype: "木箱",
            cargosize: "0.5*0.5*0.5",
            specialcargo: "锂电",
            shipmentdate: "2025-04-15",
          },
          {
            inquiryid: uuidv4(),
            originport: "北京",
            unloadingport: "成都",
            grossweight: "7pkgs",
            goodsvolume: "27.5kgs",
            cargo_volume: "0.51cbm",
            isvalidity: "2025-04-15",
            ensurecabin: "否",
            isbrand: "否",
            packagetype: "木箱",
            cargosize: "0.5*0.5*0.5",
            specialcargo: "锂电",
            shipmentdate: "2025-04-15",
          },
          {
            inquiryid: uuidv4(),
            originport: "北京",
            unloadingport: "成都",
            grossweight: "7pkgs",
            goodsvolume: "27.5kgs",
            cargo_volume: "0.51cbm",
            isvalidity: "2025-04-15",
            ensurecabin: "否",
            isbrand: "否",
            packagetype: "木箱",
            cargosize: "0.5*0.5*0.5",
            specialcargo: "锂电",
            shipmentdate: "2025-04-15",
          },
          {
            inquiryid: uuidv4(),
            originport: "北京",
            unloadingport: "成都",
            grossweight: "7pkgs",
            goodsvolume: "27.5kgs",
            cargo_volume: "0.51cbm",
            isvalidity: "2025-04-15",
            ensurecabin: "否",
            isbrand: "否",
            packagetype: "木箱",
            cargosize: "0.5*0.5*0.5",
            specialcargo: "锂电",
            shipmentdate: "2025-04-15",
          },
          {
            inquiryid: uuidv4(),
            originport: "北京",
            unloadingport: "成都",
            grossweight: "7pkgs",
            goodsvolume: "27.5kgs",
            cargo_volume: "0.51cbm",
            isvalidity: "2025-04-15",
            ensurecabin: "否",
            isbrand: "否",
            packagetype: "木箱",
            cargosize: "0.5*0.5*0.5",
            specialcargo: "锂电",
            shipmentdate: "2025-04-15",
          },
          {
            inquiryid: uuidv4(),
            originport: "北京",
            unloadingport: "成都",
            grossweight: "7pkgs",
            goodsvolume: "27.5kgs",
            cargo_volume: "0.51cbm",
            isvalidity: "2025-04-15",
            ensurecabin: "否",
            isbrand: "否",
            packagetype: "木箱",
            cargosize: "0.5*0.5*0.5",
            specialcargo: "锂电",
            shipmentdate: "2025-04-15",
          },
          {
            inquiryid: uuidv4(),
            originport: "北京",
            unloadingport: "成都",
            grossweight: "7pkgs",
            goodsvolume: "27.5kgs",
            cargo_volume: "0.51cbm",
            isvalidity: "2025-04-15",
            ensurecabin: "否",
            isbrand: "否",
            packagetype: "木箱",
            cargosize: "0.5*0.5*0.5",
            specialcargo: "锂电",
            shipmentdate: "2025-04-15",
          },
          {
            inquiryid: uuidv4(),
            originport: "北京",
            unloadingport: "成都",
            grossweight: "7pkgs",
            goodsvolume: "27.5kgs",
            cargo_volume: "0.51cbm",
            isvalidity: "2025-04-15",
            ensurecabin: "否",
            isbrand: "否",
            packagetype: "木箱",
            cargosize: "0.5*0.5*0.5",
            specialcargo: "锂电",
            shipmentdate: "2025-04-15",
          },
        ],
        code: 200,
      },
      { status: 200 }
    );
  }),

  http.get("http://***************:10086/v1/api/supplyPrice", () => {
    return HttpResponse.json(
      {
        data: [
          {
            supplypriceid: uuidv4(),
            airlines: "东方航空",
            quotation_user: "John Doe",
            originport: "北京",
            destination: "成都",
            company: "xxxxxx有限公司",
            goods_quantity: "7pkgs",
            gross_cargo_weight: "27.5kgs",
            cargo_volume: "0.51cbm",
          },
          {
            supplypriceid: uuidv4(),
            airlines: "东方航空",
            quotation_user: "Jane Doe",
            originport: "北京",
            destination: "成都",
            company: "xxxxxx有限公司",
            goods_quantity: "7pkgs",
            gross_cargo_weight: "27.5kgs",
            cargo_volume: "0.51cbm",
          },
          {
            supplypriceid: uuidv4(),
            airlines: "东方航空",
            quotation_user: "John Smith",
            originport: "北京",
            destination: "成都",
            company: "xxxxxx有限公司",
            goods_quantity: "7pkgs",
            gross_cargo_weight: "27.5kgs",
            cargo_volume: "0.51cbm",
          },
          {
            supplypriceid: uuidv4(),
            airlines: "东方航空",
            quotation_user: "Jane Smith",
            originport: "北京",
            destination: "成都",
            company: "xxxxxx有限公司",
            goods_quantity: "7pkgs",
            gross_cargo_weight: "27.5kgs",
            cargo_volume: "0.51cbm",
          },
          {
            supplypriceid: uuidv4(),
            airlines: "东方航空",
            quotation_user: "John Brown",
            originport: "北京",
            destination: "成都",
            company: "xxxxxx有限公司",
            goods_quantity: "7pkgs",
            gross_cargo_weight: "27.5kgs",
            cargo_volume: "0.51cbm",
          },
          {
            supplypriceid: uuidv4(),
            airlines: "东方航空",
            quotation_user: "Jane Brown",
            originport: "北京",
            destination: "成都",
            company: "xxxxxx有限公司",
            goods_quantity: "7pkgs",
            gross_cargo_weight: "27.5kgs",
            cargo_volume: "0.51cbm",
          },
          {
            supplypriceid: uuidv4(),
            airlines: "东方航空",
            quotation_user: "Jane Brown",
            originport: "北京",
            destination: "成都",
            company: "xxxxxx有限公司",
            goods_quantity: "7pkgs",
            gross_cargo_weight: "27.5kgs",
            cargo_volume: "0.51cbm",
          },
          {
            supplypriceid: uuidv4(),
            airlines: "东方航空",
            quotation_user: "Jane Brown",
            originport: "北京",
            destination: "成都",
            company: "xxxxxx有限公司",
            goods_quantity: "7pkgs",
            gross_cargo_weight: "27.5kgs",
            cargo_volume: "0.51cbm",
          },
          {
            supplypriceid: uuidv4(),
            airlines: "东方航空",
            quotation_user: "Jane Brown",
            originport: "北京",
            destination: "成都",
            company: "xxxxxx有限公司",
            goods_quantity: "7pkgs",
            gross_cargo_weight: "27.5kgs",
            cargo_volume: "0.51cbm",
          },
          {
            supplypriceid: uuidv4(),
            airlines: "东方航空",
            quotation_user: "Jane Brown",
            originport: "北京",
            destination: "成都",
            company: "xxxxxx有限公司",
            goods_quantity: "7pkgs",
            gross_cargo_weight: "27.5kgs",
            cargo_volume: "0.51cbm",
          },
          {
            supplypriceid: uuidv4(),
            airlines: "东方航空",
            quotation_user: "Jane Brown",
            originport: "北京",
            destination: "成都",
            company: "xxxxxx有限公司",
            goods_quantity: "7pkgs",
            gross_cargo_weight: "27.5kgs",
            cargo_volume: "0.51cbm",
          },
          {
            supplypriceid: uuidv4(),
            airlines: "东方航空",
            quotation_user: "Jane Brown",
            originport: "北京",
            destination: "成都",
            company: "xxxxxx有限公司",
            goods_quantity: "7pkgs",
            gross_cargo_weight: "27.5kgs",
            cargo_volume: "0.51cbm",
          },
          {
            supplypriceid: uuidv4(),
            airlines: "东方航空",
            quotation_user: "Jane Brown",
            originport: "北京",
            destination: "成都",
            company: "xxxxxx有限公司",
            goods_quantity: "7pkgs",
            gross_cargo_weight: "27.5kgs",
            cargo_volume: "0.51cbm",
          },
          {
            supplypriceid: uuidv4(),
            airlines: "东方航空",
            quotation_user: "Jane Brown",
            originport: "北京",
            destination: "成都",
            company: "xxxxxx有限公司",
            goods_quantity: "7pkgs",
            gross_cargo_weight: "27.5kgs",
            cargo_volume: "0.51cbm",
          },
          {
            supplypriceid: uuidv4(),
            airlines: "东方航空",
            quotation_user: "Jane Brown",
            originport: "北京",
            destination: "成都",
            company: "xxxxxx有限公司",
            goods_quantity: "7pkgs",
            gross_cargo_weight: "27.5kgs",
            cargo_volume: "0.51cbm",
          },
          {
            supplypriceid: uuidv4(),
            airlines: "东方航空",
            quotation_user: "Jane Brown",
            originport: "北京",
            destination: "成都",
            company: "xxxxxx有限公司",
            goods_quantity: "7pkgs",
            gross_cargo_weight: "27.5kgs",
            cargo_volume: "0.51cbm",
          },
        ],
        code: 200,
      },
      { status: 200 }
    );
  }),

  http.get("http://***************:10086/v1/api/airlines", () => {
    return HttpResponse.json(
      {
        data: [
          {
            id: uuidv4(),
            code: "CA",
            name: "中国国际航空",
            country: "中国",
          },
          {
            id: uuidv4(),
            code: "CZ",
            name: "中国南方航空",
            country: "中国",
          },
          {
            id: uuidv4(),
            code: "MU",
            name: "中国东方航空",
            country: "中国",
          },
          {
            id: uuidv4(),
            code: "SQ",
            name: "新加坡航空",
            country: "新加坡",
          },
          {
            id: uuidv4(),
            code: "EK",
            name: "阿联酋航空",
            country: "阿联酋",
          },
        ],
        code: 200,
      },
      { status: 200 }
    );
  }),

  http.get("http://***************:10086/v1/api/harborList", () => {
    return HttpResponse.json(
      {
        data: [
          {
            id: uuidv4(),
            code: "CNSHA",
            name: "上海港",
            country: "中国",
          },
          {
            id: uuidv4(),
            code: "SGSIN",
            name: "新加坡港",
            country: "新加坡",
          },
          {
            id: uuidv4(),
            code: "NLRTM",
            name: "鹿特丹港",
            country: "荷兰",
          },
          {
            id: uuidv4(),
            code: "USNYC",
            name: "纽约新泽西港",
            country: "美国",
          },
          {
            id: uuidv4(),
            code: "AEDXB",
            name: "迪拜港",
            country: "阿联酋",
          },
        ],
        code: 200,
      },
      { status: 200 }
    );
  }),
];
