# 邮件提取询价功能完整文档

## 目录

1. [功能概述](#功能概述)
2. [技术实现](#技术实现)
3. [两轮对话流程](#两轮对话流程)
4. [使用流程](#使用流程)
5. [进度条优化](#进度条优化)
6. [实现细节](#实现细节)
7. [港口映射表](#港口映射表)
8. [错误处理](#错误处理)
9. [配置要求](#配置要求)
10. [测试覆盖](#测试覆盖)
11. [性能考虑](#性能考虑)
12. [部署注意事项](#部署注意事项)
13. [后续优化建议](#后续优化建议)

---

## 功能概述

邮件提取询价功能允许用户通过粘贴包含询价信息的邮件内容，利用AI智能提取关键信息并自动创建询价单。该功能采用两轮AI对话机制，将原本的单轮对话升级为更精确的两轮对话流程：

1. **第一轮**：从邮件内容中提取基础信息（城市名称等）
2. **第二轮**：将城市名称转换为具体的港口代码

---

## 技术实现

### AI接口集成

- **接口**: `chatCompletions` (DeepSeek API)
- **配置文件**: `src/services/secondaryService.ts`
- **认证方式**: Bearer Token (DeepSeek API Key)
- **对话轮次**: 两轮对话（基础信息提取 + 港口转换）

### 核心改进

#### 1. 系统提示词设计

**第一轮提示词 (`EMAIL_EXTRACT_SYSTEM_PROMPT`)**

- 提取基础信息：提货城市、目的城市、货物信息等
- 输出格式：`<提货城市：，目的城市：，货物件数：，...>`

**第二轮提示词 (`generatePortConvertSystemPrompt`)**

- 将城市名称转换为港口代码
- 包含完整的港口映射表（16个国内机场 + 2个国外机场）
- 动态生成，包含第一轮结果
- 输出格式：`<起始港：，目的港：，货物件数：，...>`

#### 2. 解析函数重构

**`parseFirstRoundResponse()`**

- 解析第一轮AI返回结果
- 转换为第二轮对话的"补充信息1"格式
- 提取关键字段：提货城市、目的城市、毛重、体积、ETD要求

**`parsePortConvertResponse()`**

- 解析第二轮AI返回结果
- 直接映射到表单字段
- 处理港口代码、发货地等最终信息

#### 3. 流程控制优化

**进度管理**

- 第一轮对话过程中：0% → 60%（每370ms增加2%，约11秒，占总时间59.5%）
- 第一轮对话完成：60%（立即设置）
- 第二轮对话过程中：60% → 95%（每430ms增加2%，约7.5秒，占总时间40.5%）
- 第二轮对话完成：95% → 100%（立即设置）

**状态管理**

- 新增 `firstRoundResult` 状态存储第一轮结果
- 更新 `aiRawResponse` 显示两轮对话的完整记录
- 完善 `resetState` 函数清理所有状态

---

## 两轮对话流程

### 第一轮：基础信息提取

**系统提示词：**

```
请你从我输入的信息中提取以下有效信息：
提货城市（与提货/发货/货物地址相关的才是提货城市），目的城市（城市名和机场名均可，可多个，不能是中国城市及机场，如香港、澳门、台湾和中国的其他城市，无需考虑托运信息），货物件数，包装形式（可根据件数后的包装来判断：纸箱、木箱、布卷、麻袋、桶、托盘，"boxes"和"packages"均判定为有效，记录为纸箱），货物总毛重，货物总体积（禁止根据尺寸计算体积），货物尺寸，货物品牌（"no brand"和"brands"均判定为有效），特殊货物（只有明确标注的"general cargo"才判定有效，记录为无，除"general cargo"外的其他所有情况均记录为"-1"），要求ETD（无需为具体日期，给出当日及过去日期则判定无效，无要求或需提供预计时间也判定无效），是否保舱，信息无效的类目内容输出为-1

多运单时，有多件货物则计算总件数，有多个毛重则求和计算总毛重，有多个体积则求和计算总体积，其他信息若有多个，则均记录

经由城市默认为"无"，判定有效，若出现经由/出口到某一中国城市/机场（包括香港，澳门，台湾和其他中国城市及机场），则记录对应城市/机场

输出格式（毛重以数字+kg输出，体积以数字+cbm输出，尺寸以长x宽x高+单位输出）：
<提货城市：，目的城市：，货物件数：，包装形式：，货物总毛重：，货物总体积：，货物尺寸：，货物品牌：，特殊货物：，要求ETD：，保舱：>，信息无效的类目内容输出为-1
```

### 第二轮：港口转换

**系统提示词（动态生成，包含第一轮结果）：**

```
阅读补充信息1中的内容，将其严格按照输出格式进行输出，禁止输出解释
要求ETD：有具体日期则按格式输出
品牌货：对应补充信息1中的货物品牌
发货地：对应补充信息1中的提货城市

从补充信息2中获取：
起始港缩写：距离提货城市最近国际机场名的缩写
目的港缩写：由目的城市确定

补充信息1：
[第一轮对话的结果会动态插入到这里]

补充信息2：
目的港：
如果目的城市为莫斯科，则目的港为SVO
如果目的城市为圣彼得堡，则目的港为LED
如果目的城市为其他城市，则目的港为SVO、LED
起始港：
CGO：郑州新郑国际机场
HAK：海口美兰国际机场
HGH：杭州萧山国际机场
HRB：哈尔滨太平国际机场
PEK：北京首都国际机场
PKX：北京大兴国际机场
PVG：上海浦东国际机场
TAO：青岛胶东国际机场
URC：乌鲁木齐地窝堡国际机场
XIY：西安咸阳国际机场
CAN：广州白云国际机场
HKG：香港国际机场
SZX：深圳宝安国际机场
TFU：成都天府国际机场
CKG：重庆江北国际机场
SHE：沈阳桃仙国际机场
输出格式（尺寸以长x宽x高+单位输出）:
<起始港：SZX
目的港：SVO
货物件数：
货物毛重：220kg
货物体积：1.03cbm
货物尺寸：
包装形式：
品牌货：
特殊货物：
发货地：深圳
要求ETD: 5.10
保舱：>
信息无效的类目内容输出为-1
```

**补充信息1格式**（由第一轮结果转换）：

```
提货地址:成都
目的城市:莫斯科
总毛重:24kg
总体积:0.54cbm
ETD要求:5月20日
```

---

## 使用流程

### 第一步：输入邮件内容

1. 点击"快捷提取询价"按钮
2. 在文本框中粘贴包含询价信息的邮件内容
3. 点击"开始提取"按钮

### 第二步：AI智能分析（两轮对话）

1. **第一轮**：系统调用DeepSeek API提取基础信息（城市名称等）
   - 进度条从0%缓慢增长至60%（约11秒，占总时间59.5%）
   - 第一轮完成后进度保持在60%
2. **第二轮**：将城市名称转换为港口代码，获取最终结果
   - 进度条从60%缓慢增长至95%（约7.5秒，占总时间40.5%）
   - 第二轮完成后进度到达100%
3. 展示两轮AI对话的原始返回结果

### 第三步：确认和编辑信息

1. 查看AI提取的信息
2. 可以手动编辑和修正任何字段
3. 点击"确认并创建询价"

### 第四步：创建询价单

1. 系统创建询价单
2. 显示创建进度
3. 完成后自动关闭模态框

---

## 进度条优化

### 问题描述

原始实现中，进度条在第一轮对话时就到达90%，然后在第二轮对话时又调回50%，这给用户造成了困惑和不好的体验。

### 优化方案

#### 期望的进度条行为（基于真实响应时间占比）

1. **第一轮对话过程中**：进度条缓慢增长至60%（约11秒，占总时间59.5%）
2. **第一轮对话结束**：进度条保持在60%
3. **第二轮对话过程中**：进度条从60%缓慢增长至95%（约7.5秒，占总时间40.5%）
4. **第二轮对话结束**：进度条到达100%

#### 详细进度控制（基于实际响应时间）

- **第一轮对话过程**：0% → 60%（每370ms增加2%，约11秒）
- **第一轮对话完成**：保持在60%（立即设置）
- **第二轮对话过程**：60% → 95%（每430ms增加2%，约7.5秒）
- **第二轮对话完成**：95% → 100%（立即跳转）

#### 实现逻辑

```javascript
// 第一轮对话的进度更新（0% → 60%），约11秒，占总时间59.5%
const firstProgressInterval = setInterval(() => {
  setExtractProgress((prev) => {
    if (prev >= 60) {
      clearInterval(firstProgressInterval);
      return 60;
    }
    return prev + 2;
  });
}, 370);

// 第一轮完成后保持在60%
clearInterval(firstProgressInterval);
setExtractProgress(60);

// 第二轮对话的进度更新（60% → 95%），约7.5秒，占总时间40.5%
const secondProgressInterval = setInterval(() => {
  setExtractProgress((prev) => {
    if (prev >= 95) {
      clearInterval(secondProgressInterval);
      return 95;
    }
    return prev + 2;
  });
}, 430);

// 第二轮完成后设置到100%
clearInterval(secondProgressInterval);
setExtractProgress(100);
```

### 关键改进点

#### 1. 分阶段进度控制（基于真实响应时间）

- **第一阶段**：独立的进度更新器，控制0% → 60%（约11秒）
- **第二阶段**：独立的进度更新器，控制60% → 95%（约7.5秒）
- **里程碑**：在关键节点（60%、100%）立即设置进度

#### 2. 进度更新参数优化（基于实际测试）

- **第一轮更新频率**：370ms，提供与实际响应时间匹配的视觉效果
- **第二轮更新频率**：430ms，根据第二轮较短的响应时间调整
- **增长幅度**：每次+2%，提供更细腻的进度感知
- **上限控制**：每个阶段都有明确的上限，避免进度超调

#### 3. 时间线设计（基于真实响应时间占比）

```
时间轴：0s -------- 11s -------- 18.5s
进度：  0%   →   60%   →   100%
阶段：  [第一轮对话:59.5%] [第二轮对话:40.5%]
响应时间：约11秒          约7.5秒
```

#### 4. 用户体验改进

- **连续性**：进度条始终向前推进，不会出现倒退
- **可预测性**：用户可以清楚地看到两个主要阶段
- **反馈性**：每个阶段的进度都有明确的视觉反馈

---

## 实现细节

### 接口调用流程

```javascript
// 第一轮对话
const firstMessages = [
  { role: "system", content: EMAIL_EXTRACT_SYSTEM_PROMPT },
  { role: "user", content: emailContent },
];
const firstResponse = await chatCompletions(firstMessages);

// 解析第一轮结果
const supplementInfo1 = parseFirstRoundResponse(firstResponse);

// 第二轮对话（将第一轮结果嵌入系统提示词）
const secondMessages = [
  {
    role: "system",
    content: generatePortConvertSystemPrompt(supplementInfo1),
  },
  {
    role: "user",
    content: "请按照系统提示词中的要求进行输出",
  },
];
const secondResponse = await chatCompletions(secondMessages);

// 解析最终结果
const extractedInfo = parsePortConvertResponse(secondResponse);
```

### 数据转换示例

#### 第一轮输入/输出

```
输入：发货地：成都，目的地：莫斯科，货物：5箱电子产品，重量：24kg，体积：0.54cbm
输出：<提货城市：成都，目的城市：莫斯科，货物件数：5，包装形式：纸箱，货物总毛重：24kg，货物总体积：0.54cbm，...>
```

#### 第二轮输入/输出

```
输入：补充信息1：提货地址:成都\n目的城市:莫斯科\n总毛重:24kg\n总体积:0.54cbm\nETD要求:5月20日
输出：<起始港：TFU\n目的港：SVO\n货物件数：5\n包装形式：纸箱\n货物毛重：24kg\n...>
```

---

## 港口映射表

### 支持的国内机场

- CGO：郑州新郑国际机场
- HAK：海口美兰国际机场
- HGH：杭州萧山国际机场
- HRB：哈尔滨太平国际机场
- PEK：北京首都国际机场
- PKX：北京大兴国际机场
- PVG：上海浦东国际机场
- TAO：青岛胶东国际机场
- URC：乌鲁木齐地窝堡国际机场
- XIY：西安咸阳国际机场
- CAN：广州白云国际机场
- HKG：香港国际机场
- SZX：深圳宝安国际机场
- TFU：成都天府国际机场
- CKG：重庆江北国际机场
- SHE：沈阳桃仙国际机场

### 支持的国外机场

- SVO：莫斯科
- LED：圣彼得堡

---

## 错误处理

### 1. API Key错误

- 错误信息：`DeepSeek API Key无效或未设置，请检查配置`
- 解决方案：检查环境变量或本地存储中的API Key

### 2. 解析错误

- 错误信息：`AI返回格式解析失败，请重试`
- 解决方案：重新尝试提取或手动输入信息

### 3. 网络错误

- 错误信息：`提取失败: [具体错误信息]`
- 解决方案：检查网络连接，稍后重试

### 错误处理实现

```javascript
try {
  // AI对话逻辑
} catch (error) {
  // 清理所有进度更新器
  clearInterval(firstProgressInterval);
  clearInterval(secondProgressInterval);
  setExtractProgress(0); // 重置进度

  // 根据错误类型显示不同的错误信息
  if (error instanceof Error) {
    if (error.message.includes("API Key")) {
      message.error("DeepSeek API Key无效或未设置，请检查配置");
    } else if (error.message.includes("解析")) {
      message.error("AI返回格式解析失败，请重试");
    } else {
      message.error(`提取失败: ${error.message}`);
    }
  }
}
```

---

## 配置要求

### 环境变量

```
REACT_APP_SECONDARY_BASE_URL=https://api.deepseek.com
```

### DeepSeek API Key

- 存储位置：`localStorage.getItem("deepseekApiKey")`
- 默认值：`***********************************`

### 状态管理

```javascript
const [extractProgress, setExtractProgress] = useState(0);
const [firstRoundResult, setFirstRoundResult] = useState("");
const [aiRawResponse, setAiRawResponse] = useState("");
```

---

## 测试覆盖

### 1. 单元测试

- 两轮对话流程测试
- 结果解析测试
- 错误处理测试
- 进度条控制测试

### 2. 集成测试

- 完整的用户交互流程
- AI接口调用验证
- 表单数据填充验证

### 测试示例

```javascript
test("应该进行两轮AI对话进行信息提取", async () => {
  // Mock两轮AI响应
  mockChatCompletions
    .mockResolvedValueOnce(mockFirstResponse)
    .mockResolvedValueOnce(mockSecondResponse);

  // 验证第一轮调用
  expect(mockChatCompletions).toHaveBeenNthCalledWith(1, [
    {
      role: "system",
      content: expect.stringContaining("请你从我输入的信息中提取以下有效信息"),
    },
    { role: "user", content: emailContent },
  ]);

  // 验证第二轮调用（第一轮结果嵌入到系统提示词中）
  expect(mockChatCompletions).toHaveBeenNthCalledWith(2, [
    {
      role: "system",
      content: expect.stringContaining("阅读补充信息1中的内容"),
    },
    {
      role: "user",
      content: "请按照系统提示词中的要求进行输出",
    },
  ]);
});
```

### 手动测试检查点

1. 启动提取过程，观察进度条从0%开始
2. 第一轮对话期间，进度条应缓慢增长至60%左右（约11秒）
3. 第一轮完成时，进度条应保持在60%
4. 第二轮对话期间，进度条应从60%缓慢增长至95%左右（约7.5秒）
5. 第二轮完成时，进度条应跳转到100%
6. 整个过程中进度条不应出现倒退
7. 进度条的时间分配应与实际AI响应时间占比一致

---

## 性能考虑

### 1. API调用优化

- 两次API调用的延迟处理
- 进度条平滑过渡
- 错误重试机制

### 2. 用户体验

- 加载状态的视觉反馈
- 禁用不必要的用户操作
- 清晰的状态指示

### 3. 内存管理

- 确保所有setInterval都被正确清理
- 在组件卸载时清理进度更新器
- 错误情况下的资源清理

### 4. 用户体验优化

- 进度更新频率基于实际响应时间调整，不会造成性能问题
- 视觉效果流畅，提供真实的反馈
- 进度时间与实际AI处理时间完全匹配（第一轮59.5%，第二轮40.5%）
- 进度条分配基于真实测试数据，提供准确的时间预期

---

## 部署注意事项

1. **API Key配置**：确保DeepSeek API Key正确配置
2. **网络连接**：两轮对话需要稳定的网络连接
3. **错误监控**：建议添加详细的错误日志记录
4. **性能监控**：监控两轮对话的总耗时
5. **数据隐私**：邮件内容会发送到第三方AI服务，请注意数据安全
6. **API配额限制**：DeepSeek API有使用配额限制，请合理使用

---

## 后续优化建议

### 1. 功能扩展

- **缓存机制**：对相似的第一轮结果进行缓存
- **批量处理**：支持批量邮件的两轮处理
- **智能重试**：针对特定错误的智能重试机制
- **用户反馈**：收集用户对提取准确性的反馈，持续优化提示词

### 2. 港口映射扩展

- 易于添加新的港口映射
- 支持动态港口配置
- 可配置的映射规则

### 3. 提示词优化

- 模块化的提示词设计
- 易于调整和优化
- 支持多语言扩展

### 4. 进度条进一步优化

- **已实现**：基于真实响应时间占比的进度分配（第一轮59.5%，第二轮40.5%）
- **已优化**：进度条时间与实际AI处理时间完全匹配
- **动画效果**：添加CSS动画使进度条更加平滑
- **错误恢复**：在网络错误时提供进度条的恢复机制
- **更细粒度**：可以考虑更多的进度节点，如25%、75%等

### 5. 用户体验改进

- **结果展示**：显示两轮对话的完整记录
- **最终结果**：直接填入表单字段
- **编辑功能**：用户可以查看和编辑所有提取的信息
- **错误处理**：分别处理两轮对话的错误，提供详细的错误信息提示

---

## 文件结构

```
src/
├── pages/Quotation/components/
│   ├── EmailExtractModal.tsx          # 主组件
│   ├── EmailExtractModal.test.tsx     # 测试文件
│   └── index.less                     # 样式文件
├── services/
│   └── secondaryService.ts            # AI接口服务
└── utils/
    ├── request.ts                     # 请求配置
    └── deepseekAuth.ts               # API Key管理

docs/
├── ComprehensiveDocumentation.md      # 综合文档（本文件）
├── EmailExtractFeature.md            # 功能说明文档
├── TwoRoundAIImplementation.md        # 实现总结文档
└── ProgressBarOptimization.md        # 进度条优化文档
```

---

## 总结

邮件提取询价功能通过两轮AI对话机制，成功实现了从邮件内容到结构化询价数据的智能转换。该功能具有以下特点：

1. **智能化**：利用AI技术自动提取和转换信息
2. **准确性**：两轮对话确保城市到港口的准确映射
3. **用户友好**：优化的进度条和清晰的步骤指示
4. **可扩展**：模块化设计便于后续功能扩展
5. **稳定性**：完善的错误处理和资源管理

通过持续的优化和改进，该功能将为用户提供更加便捷、准确、高效的询价信息提取体验。
