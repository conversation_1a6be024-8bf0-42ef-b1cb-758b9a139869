import React from "react";
import { Card } from "antd";
import { HistoryOutlined } from "@ant-design/icons";
import { MOCK_FREIGHT_METHODS } from "../constants";

interface HistoryRecordsProps {
  records: any[];
  onSelect: (record: any) => void;
}

const HistoryRecords: React.FC<HistoryRecordsProps> = ({
  records,
  onSelect,
}) => {
  return (
    <Card className="history-card">
      <div className="history-records">
        <h3 className="history-title">
          <HistoryOutlined /> 历史询价记录
        </h3>
        <div className="history-list">
          {records.map((record) => (
            <div
              key={record.id}
              className="history-item"
              onClick={() => onSelect(record)}
            >
              <div className="history-item-title">{record.title}</div>
              <div className="history-item-info">
                <span>{record.date}</span>
                <span>
                  {
                    MOCK_FREIGHT_METHODS.find(
                      (m) => m.id === record.freightmethod
                    )?.name
                  }
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
};

export default HistoryRecords;
