import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import "./index.less";
import TableCom from "@/components/TableCom";
import {
  Space,
  Button,
  Form,
  Popconfirm,
  Tooltip,
  message,
  Table,
  Select,
  Input,
} from "antd";
import type { TableProps } from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  FilterOutlined,
} from "@ant-design/icons";
import { delDomesticPrice, getDomesticPriceByCondition } from "./services";
import ActionModal from "./components/ActionModal";
import DetailModal from "./components/DetailModal";
import { useAppSelector } from "@/store/hooks";
import useBaseData from "@/hooks/useBaseData";

interface DomesticPrice {
  domesticpriceid: number;
  shippingplace: string;
  shippingprovince: string;
  destination: string;
  volumeprice: number;
  weightprice: number;
  minicharge: number;
  fastestaging: number;
  slowestaging: number;
  deliveryfee: number;
  logistics: string;
}

const DomesticPriceManagement: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [data, setData] = useState<DomesticPrice[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [method, setMethod] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [detailModalVisible, setDetailModalVisible] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<DomesticPrice | null>(
    null
  );
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const { user } = useAppSelector((state) => state.user);
  const { currentLanguage } = useAppSelector((state) => state.language);

  // 根据当前语言判断是否为英文
  const isEnglish = currentLanguage === "en-US";

  // 使用自定义Hook获取基础数据
  const {
    shipmentPlaceOptions: rawShipmentPlaceOptions,
    ShippingProvinceOptions: rawShippingProvinceOptions,
    portOptions: rawPortOptions,
    loadShipmentPlaces,
    loadPorts,
  } = useBaseData();

  // 转换为表格筛选所需的格式
  const shipmentPlaceOptions = rawShipmentPlaceOptions.map((item) => ({
    text: item.label,
    value: item.value,
  }));

  const ShippingProvinceOptions = rawShippingProvinceOptions.map((item) => ({
    text: item.label,
    value: item.value,
  }));

  const portOptions = rawPortOptions.map((item) => ({
    text: item.label,
    value: item.value,
  }));

  // 检查用户是否有修改权限
  const hasEditPermission =
    user?.useridentity !== 1 &&
    user?.useridentity !== 2 &&
    user?.useridentity !== 3;

  const queryDomesticPriceList = async (params: any = {}) => {
    setLoading(true);
    try {
      const { current = 1, pageSize = 10, ...restParams } = params;
      const res = await getDomesticPriceByCondition({
        pageindex: current,
        pagesize: pageSize,
        ...restParams,
      });
      const { data } = res;
      if (data?.resultCode === 200) {
        setData(data.data || []);
        setPagination({
          current,
          pageSize,
          total: data?.totalNum || 0,
        });
      } else {
        message.error(
          data.message || t("domesticPriceManagement.messages.fetchListFailed")
        );
      }
    } catch (e) {
      console.log(e);
      message.error(t("domesticPriceManagement.messages.fetchListFailed"));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    queryDomesticPriceList();

    // 加载基础数据
    loadShipmentPlaces();
    loadPorts();
  }, []);

  // 根据用户权限和数据动态生成表格列
  const getColumns = () => {
    const baseColumns: TableProps<DomesticPrice>["columns"] = [
      // {
      //   title: "ID",
      //   dataIndex: "domesticpriceid",
      //   key: "domesticpriceid",
      // },
      {
        title: t("domesticPriceManagement.columns.shippingPlace"),
        dataIndex: "shippingplace",
        key: "shippingplace",
        width: isEnglish ? 125 : 100,
        filters: shipmentPlaceOptions,
        filterMultiple: true,
        filterSearch: true,
      },
      {
        title: t("domesticPriceManagement.columns.shippingProvince"),
        dataIndex: "shippingprovince",
        key: "shippingprovince",
        width: isEnglish ? 150 : 100,
        filters: ShippingProvinceOptions,
        filterMultiple: true,
        filterSearch: true,
      },
      {
        title: t("domesticPriceManagement.columns.destination"),
        dataIndex: "destination",
        key: "destination",
        width: isEnglish ? 110 : 80,
        filters: portOptions,
        filterMultiple: true,
        filterSearch: true,
      },
      {
        title: t("domesticPriceManagement.columns.volumePrice"),
        dataIndex: "volumeprice",
        key: "volumeprice",
        width: isEnglish ? 160 : 130,
      },
      {
        title: t("domesticPriceManagement.columns.weightPrice"),
        dataIndex: "weightprice",
        key: "weightprice",
        width: isEnglish ? 160 : 130,
      },
      {
        title: t("domesticPriceManagement.columns.minCharge"),
        dataIndex: "minicharge",
        key: "minicharge",
        width: isEnglish ? 140 : 110,
      },
      {
        title: t("domesticPriceManagement.columns.fastestAging"),
        dataIndex: "fastestaging",
        key: "fastestaging",
        width: isEnglish ? 140 : 110,
      },
      {
        title: t("domesticPriceManagement.columns.slowestAging"),
        dataIndex: "slowestaging",
        key: "slowestaging",
        width: isEnglish ? 150 : 110,
      },
      {
        title: t("domesticPriceManagement.columns.deliveryFee"),
        dataIndex: "deliveryfee",
        key: "deliveryfee",
        width: isEnglish ? 140 : 110,
      },
      {
        title: t("domesticPriceManagement.columns.logistics"),
        dataIndex: "logistics",
        key: "logistics",
        width: isEnglish ? 100 : 80,
        filterDropdown: ({
          setSelectedKeys,
          selectedKeys,
          confirm,
          clearFilters,
        }) => (
          <div style={{ padding: 8 }}>
            <Input
              placeholder={t(
                "domesticPriceManagement.filters.inputLogisticsName"
              )}
              value={selectedKeys[0]}
              onChange={(e) =>
                setSelectedKeys(e.target.value ? [e.target.value] : [])
              }
              onPressEnter={() => confirm()}
              style={{ width: 188, marginBottom: 8, display: "block" }}
            />
            <Space>
              <Button
                type="primary"
                onClick={() => confirm()}
                size="small"
                style={{ width: 90 }}
              >
                {t("domesticPriceManagement.filters.confirm")}
              </Button>
              <Button
                onClick={() => {
                  clearFilters && clearFilters();
                  // 点击重置后直接确认，不需要再点击确认按钮
                  confirm();
                }}
                size="small"
                style={{ width: 90 }}
              >
                {t("domesticPriceManagement.filters.reset")}
              </Button>
            </Space>
          </div>
        ),
        filterIcon: (filtered) => (
          <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />
        ),
      },
    ];

    // 添加操作列
    baseColumns.push({
      title: t("domesticPriceManagement.columns.actions"),
      key: "action",
      fixed: "right" as const,
      width: 150,
      render: (_, record) => (
        <Space size="middle" className="operation-buttons">
          <Tooltip title={t("domesticPriceManagement.tooltips.viewDetail")}>
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
              className="view-button"
              size="small"
            />
          </Tooltip>
          {hasEditPermission && (
            <>
              <Tooltip title={t("domesticPriceManagement.tooltips.edit")}>
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(record)}
                  className="edit-button"
                  size="small"
                />
              </Tooltip>
              <Tooltip title={t("domesticPriceManagement.tooltips.delete")}>
                <Popconfirm
                  title={t("domesticPriceManagement.deleteConfirm.title")}
                  description={t(
                    "domesticPriceManagement.deleteConfirm.description"
                  )}
                  onConfirm={() => handleDel(record)}
                  onCancel={handleCancel}
                  okText={t("domesticPriceManagement.deleteConfirm.okText")}
                  cancelText={t(
                    "domesticPriceManagement.deleteConfirm.cancelText"
                  )}
                >
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    className="delete-button"
                    size="small"
                  />
                </Popconfirm>
              </Tooltip>
            </>
          )}
        </Space>
      ),
    });

    return baseColumns;
  };

  // 当数据变化时重新生成列定义，以更新筛选选项
  const columns = getColumns();

  // 处理表格变化（分页、筛选）
  const handleTableChange: TableProps<DomesticPrice>["onChange"] = (
    paginate,
    filters
  ) => {
    const params: any = {
      current: paginate.current,
      pageSize: paginate.pageSize,
    };

    // 处理筛选条件
    if (filters.shippingplace && filters.shippingplace.length > 0) {
      params.shippingplace = filters.shippingplace.join(",");
    }

    if (filters.shippingprovince && filters.shippingprovince.length > 0) {
      params.shippingprovince = filters.shippingprovince.join(",");
    }

    if (filters.destination && filters.destination.length > 0) {
      params.destination = filters.destination.join(",");
    }

    if (filters.logistics && filters.logistics.length > 0) {
      // 物流列使用输入框筛选，直接传递用户输入的值
      params.logistics = filters.logistics[0];
    }

    queryDomesticPriceList(params);
  };

  const handleAdd = () => {
    showModal();
    setMethod("add");
    form.resetFields();
  };

  const handleEdit = (record: DomesticPrice) => {
    setMethod("edit");
    showModal();
    const newItem = {
      ...record,
      destination: record.destination.split(","),
    };
    form.setFieldsValue(newItem);
  };

  const handleDel = async (record: DomesticPrice) => {
    const res = await delDomesticPrice({
      domesticpriceid: record?.domesticpriceid,
    });
    const { data } = res;
    if (data.resultCode === 200) {
      // 删除后保持当前分页状态
      queryDomesticPriceList({
        current: pagination.current,
        pageSize: pagination.pageSize,
      });
      message.success(t("domesticPriceManagement.messages.deleteSuccess"));
    } else {
      message.error(data.message);
    }
  };

  const handleCancel = () => {
    // 取消删除操作
  };

  // 处理查看详情
  const handleViewDetail = (record: DomesticPrice) => {
    setCurrentRecord(record);
    setDetailModalVisible(true);
  };

  // 关闭详情模态框
  const handleCloseDetail = () => {
    setDetailModalVisible(false);
    setCurrentRecord(null);
  };

  const showModal = () => {
    setIsModalOpen(true);
  };

  const filterRender = () => {
    return (
      <div className="filter_box">
        <div className="title">{t("domesticPriceManagement.title")}</div>
        {hasEditPermission && (
          <Space>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              {t("domesticPriceManagement.newPrice")}
            </Button>
          </Space>
        )}
      </div>
    );
  };

  return (
    <div className="domestic-price-management">
      {filterRender()}
      <Table<DomesticPrice>
        className={"scroll-table"}
        columns={columns}
        dataSource={data}
        loading={loading}
        rowKey="domesticpriceid"
        size="middle"
        tableLayout="fixed"
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) =>
            t("domesticPriceManagement.pagination.total", { total }),
        }}
        onChange={handleTableChange}
        scroll={{
          x: isEnglish ? 1500 : 1300,
          y: "80vh",
        }}
      />
      <ActionModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        method={method}
        form={form}
        queryDomesticPriceList={() =>
          queryDomesticPriceList({
            current: pagination.current,
            pageSize: pagination.pageSize,
          })
        }
      />

      {/* 详情模态框 */}
      <DetailModal
        open={detailModalVisible}
        onClose={handleCloseDetail}
        record={currentRecord}
      />
    </div>
  );
};

export default DomesticPriceManagement;
