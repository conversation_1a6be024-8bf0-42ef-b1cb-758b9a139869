import React, { useRef, useEffect } from "react";
import { useFrame } from "@react-three/fiber";
import { useTexture } from "@react-three/drei";
import {
  Mesh,
  Group,
  BackSide,
  DoubleSide,
  Vector3,
  QuadraticBezierCurve3,
  BufferGeometry,
  LineBasicMaterial,
  Line,
} from "three";

// 地球组件 - 高级科幻风格
export const FuturisticEarth: React.FC = () => {
  const earthRef = useRef<Mesh>(null);
  const cloudsRef = useRef<Mesh>(null);
  const glowRef = useRef<Mesh>(null);

  // 使用地球纹理
  const earthTexture = useTexture("/earth_texture.jpg");

  // 地球自转动画
  useFrame(() => {
    if (earthRef.current) {
      earthRef.current.rotation.y += 0.0005;
    }
    if (cloudsRef.current) {
      cloudsRef.current.rotation.y += 0.0007;
    }
    if (glowRef.current) {
      glowRef.current.rotation.y += 0.0003;
    }
  });

  return (
    <group>
      {/* 地球 */}
      <mesh ref={earthRef} position={[0, 0, 0]} castShadow receiveShadow>
        <sphereGeometry args={[2, 64, 64]} />
        <meshStandardMaterial
          map={earthTexture}
          metalness={0.2}
          roughness={0.6}
          emissive="#1e3a60"
          emissiveIntensity={0.1}
        />
      </mesh>

      {/* 云层 */}
      <mesh ref={cloudsRef} position={[0, 0, 0]}>
        <sphereGeometry args={[2.03, 48, 48]} />
        <meshStandardMaterial
          color="#ffffff"
          transparent={true}
          opacity={0.2}
          depthWrite={false}
        />
      </mesh>

      {/* 大气层效果 */}
      <mesh position={[0, 0, 0]}>
        <sphereGeometry args={[2.1, 32, 32]} />
        <meshStandardMaterial
          color="#88c6ff"
          transparent={true}
          opacity={0.1}
          side={BackSide}
        />
      </mesh>

      {/* 外部光晕效果 */}
      <mesh ref={glowRef} position={[0, 0, 0]}>
        <sphereGeometry args={[2.5, 32, 32]} />
        <meshStandardMaterial
          color="#1797e1"
          transparent={true}
          opacity={0.05}
          side={BackSide}
        />
      </mesh>

      {/* 添加轨道环 */}
      <mesh rotation={[Math.PI / 2, 0, 0]} position={[0, 0, 0]}>
        <ringGeometry args={[3, 3.1, 64]} />
        <meshBasicMaterial
          color="#40a9ff"
          transparent={true}
          opacity={0.3}
          side={DoubleSide}
        />
      </mesh>

      {/* 添加第二个轨道环 */}
      <mesh rotation={[Math.PI / 3, 0, 0]} position={[0, 0, 0]}>
        <ringGeometry args={[3.5, 3.55, 64]} />
        <meshBasicMaterial
          color="#52cafe"
          transparent={true}
          opacity={0.2}
          side={DoubleSide}
        />
      </mesh>
    </group>
  );
};

// 航线组件
export const FlightRoutes: React.FC = () => {
  const routesRef = useRef<Group>(null);

  // 创建航线路径
  const createRoute = (
    startLat: number,
    startLng: number,
    endLat: number,
    endLng: number,
    height: number,
    color: string
  ) => {
    // 将经纬度转换为3D坐标
    const radius = 2;
    const startPos = latLngToVector3(startLat, startLng, radius);
    const endPos = latLngToVector3(endLat, endLng, radius);

    // 创建曲线控制点
    const midPoint = new Vector3()
      .addVectors(startPos, endPos)
      .multiplyScalar(0.5);
    const distance = startPos.distanceTo(endPos);
    midPoint.normalize().multiplyScalar(radius + distance * height);

    // 创建曲线
    const curve = new QuadraticBezierCurve3(startPos, midPoint, endPos);
    const points = curve.getPoints(50);
    const geometry = new BufferGeometry().setFromPoints(points);

    // 创建材质
    const material = new LineBasicMaterial({
      color: color,
      transparent: true,
      opacity: 0.7,
      linewidth: 1,
    });

    return new Line(geometry, material);
  };

  // 经纬度转换为3D坐标
  const latLngToVector3 = (lat: number, lng: number, radius: number) => {
    const phi = (90 - lat) * (Math.PI / 180);
    const theta = (lng + 180) * (Math.PI / 180);
    const x = -radius * Math.sin(phi) * Math.cos(theta);
    const y = radius * Math.cos(phi);
    const z = radius * Math.sin(phi) * Math.sin(theta);
    return new Vector3(x, y, z);
  };

  useEffect(() => {
    if (routesRef.current) {
      // 清除现有路线
      while (routesRef.current.children.length > 0) {
        routesRef.current.remove(routesRef.current.children[0]);
      }

      // 添加新路线
      const routes = [
        // 上海到纽约
        createRoute(31.2304, 121.4737, 40.7128, -74.006, 0.3, "#52cafe"),
        // 北京到伦敦
        createRoute(39.9042, 116.4074, 51.5074, -0.1278, 0.25, "#1797e1"),
        // 东京到洛杉矶
        createRoute(35.6762, 139.6503, 34.0522, -118.2437, 0.28, "#76c7f2"),
        // 悉尼到新加坡
        createRoute(-33.8688, 151.2093, 1.3521, 103.8198, 0.22, "#40a9ff"),
        // 迪拜到法兰克福
        createRoute(25.2048, 55.2708, 50.1109, 8.6821, 0.2, "#52cafe"),
      ];

      // 将路线添加到组中
      routes.forEach((route) => {
        routesRef.current?.add(route);
      });
    }
  }, []);

  // 动画效果
  useFrame(() => {
    if (routesRef.current) {
      routesRef.current.rotation.y += 0.0005;
    }
  });

  return <group ref={routesRef} />;
};

// 星光背景组件
export const StarBackground: React.FC = () => {
  return (
    <>
      <ambientLight intensity={0.3} />
      <directionalLight position={[10, 10, 5]} intensity={1.2} />
      <directionalLight
        position={[-10, -10, -5]}
        intensity={0.4}
        color="#40a9ff"
      />
      <spotLight
        position={[0, 10, 0]}
        angle={0.3}
        penumbra={1}
        intensity={0.8}
        color="#1797e1"
        castShadow
      />

      {/* 添加星星 */}
      {Array.from({ length: 200 }).map((_, i) => {
        const x = (Math.random() - 0.5) * 40;
        const y = (Math.random() - 0.5) * 40;
        const z = (Math.random() - 0.5) * 40;
        const size = Math.random() * 0.15 + 0.05;
        return (
          <mesh key={i} position={[x, y, z]}>
            <sphereGeometry args={[size, 16, 16]} />
            <meshStandardMaterial
              color="#40a9ff"
              emissive="#40a9ff"
              emissiveIntensity={3}
            />
          </mesh>
        );
      })}

      {/* 添加光线效果 */}
      <mesh rotation={[0, 0, Math.PI / 4]} position={[0, 0, -20]}>
        <planeGeometry args={[60, 60]} />
        <meshBasicMaterial
          color="#1797e1"
          transparent
          opacity={0.03}
          side={DoubleSide}
        />
      </mesh>
    </>
  );
};

// 完整的3D背景场景
const FuturisticBackground: React.FC = () => {
  return (
    <>
      <StarBackground />
      <FuturisticEarth />
      {/* <FlightRoutes /> */}
    </>
  );
};

export default FuturisticBackground;
