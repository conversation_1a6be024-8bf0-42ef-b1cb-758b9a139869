.personal-quotation-container {
  width: 100%;
  height: 100%;
  background: #f8faff;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      linear-gradient(rgba(0, 76, 255, 0.03) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 76, 255, 0.03) 1px, transparent 1px);
    background-size: 30px 30px;
    z-index: 0;
    pointer-events: none;
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at 50% 50%,
      rgba(0, 207, 255, 0.08),
      transparent 70%
    );
    z-index: 0;
    pointer-events: none;
  }

  .floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
    pointer-events: none;
    overflow: hidden;

    .particle {
      position: absolute;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: rgba(0, 207, 255, 0.2);
      box-shadow: 0 0 10px rgba(0, 207, 255, 0.4);
      animation: float-particle 15s infinite linear;

      &:nth-child(1) {
        top: 10%;
        left: 20%;
        animation-duration: 25s;
        animation-delay: 0s;
      }
      &:nth-child(2) {
        top: 30%;
        left: 80%;
        animation-duration: 30s;
        animation-delay: 2s;
      }
      &:nth-child(3) {
        top: 70%;
        left: 35%;
        animation-duration: 22s;
        animation-delay: 5s;
      }
      &:nth-child(4) {
        top: 50%;
        left: 70%;
        animation-duration: 28s;
        animation-delay: 7s;
      }
      &:nth-child(5) {
        top: 85%;
        left: 15%;
        animation-duration: 20s;
        animation-delay: 10s;
      }
    }
  }

  .personal-quotation-content {
    height: calc(100% - 70px);
    overflow: hidden auto;
  }

  .page-header {
    position: relative;
    height: 60px;
    overflow: hidden;
    margin-bottom: 10px;

    .header-content {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0 20px;

      .header-icon {
        font-size: 22px;
        margin-right: 10px;
        color: #fff;
        background: linear-gradient(135deg, #00cfff, #004cff);
        padding: 8px;
        border-radius: 50%;
        box-shadow: 0 0 8px rgba(0, 76, 255, 0.25);
      }

      .header-title {
        color: #002766;
        margin: 0;
        text-shadow: 0 0 3px rgba(0, 207, 255, 0.15);
        font-weight: 600;
        letter-spacing: 0.3px;
        font-size: 18px;
      }
    }

    .header-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.95),
        rgba(240, 245, 255, 0.95)
      );
      z-index: 1;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: radial-gradient(
          circle at 50% 50%,
          rgba(0, 207, 255, 0.08) 0%,
          transparent 70%
        );
        z-index: -1;
      }
    }
  }

  .quotation-card {
    margin: 0 auto 16px;
    max-width: 1200px;
    width: calc(100% - 32px);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    overflow: hidden;
    border: 1px solid rgba(0, 76, 255, 0.1);
    box-shadow:
      0 8px 20px rgba(0, 76, 255, 0.08),
      inset 0 0 8px rgba(0, 207, 255, 0.03);

    .ant-card-body {
      padding: 16px;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .section-title {
      color: #002766;
      margin-bottom: 0;
      position: relative;
      padding-left: 10px;
      font-size: 16px;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background: linear-gradient(to bottom, #00cfff, #004cff);
        border-radius: 2px;
      }
    }

    .quotation-steps {
      margin-bottom: 20px;

      .ant-steps-item-icon {
        background: linear-gradient(135deg, #00cfff, #004cff);
        border-color: transparent;
        width: 28px;
        height: 28px;
        line-height: 28px;
        font-size: 14px;
      }

      .ant-steps-item-title {
        color: #002766;
        font-size: 14px;
        line-height: 28px;
      }

      .ant-steps-item-description {
        color: rgba(0, 39, 102, 0.65);
        font-size: 12px;
      }

      .ant-steps-item-wait .ant-steps-item-icon {
        background: #f0f5ff;
        border-color: rgba(0, 76, 255, 0.3);

        .ant-steps-icon {
          color: rgba(0, 76, 255, 0.3);
        }
      }

      .ant-steps-item-finish {
        .ant-steps-icon {
          color: #fff;
        }
      }
    }

    .step-content {
      padding: 12px 0;
    }

    .step-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;

      .ant-btn {
        min-width: 90px;
        height: 36px;
        border-radius: 6px;
        font-size: 14px;

        &.ant-btn-primary {
          background: linear-gradient(135deg, #00cfff, #004cff);
          border: none;
          box-shadow: 0 0 10px rgba(0, 76, 255, 0.15);
        }
      }
    }

    .quotation-form {
      .ant-form-item-label > label {
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        text-shadow: 0 0 3px rgba(0, 207, 255, 0.08);
        font-size: 13px;
        height: 28px;
        line-height: 28px;
      }

      .ant-select-selector,
      .ant-input,
      .ant-picker,
      .ant-input-number,
      .ant-radio-wrapper {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 4px;
        border: 1px solid rgba(0, 76, 255, 0.2);
        color: rgba(0, 0, 0, 0.85);
        transition: all 0.3s;
        height: 32px !important;
        line-height: 32px !important;

        &:hover,
        &:focus {
          border-color: #00cfff;
          box-shadow: 0 0 0 1px rgba(0, 207, 255, 0.15);
        }

        .ant-input-number-input {
          height: 32px;
        }

        .ant-select-selection-item,
        .ant-picker-input > input {
          color: rgba(0, 0, 0, 0.85);
          line-height: 30px !important;
        }

        .ant-select-arrow,
        .ant-picker-suffix {
          color: rgba(0, 76, 255, 0.8);
        }
      }

      .ant-select-selector {
        padding: 0 11px;
        display: flex;
        align-items: center;
      }
      .ant-select-multiple {
        .ant-select-selector {
          height: auto !important;
          min-height: 32px !important;
          padding: 2px 5px !important;
        }

        .ant-select-selection-overflow {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
        }

        .ant-select-selection-item {
          height: 22px;
          line-height: 20px !important;
          margin-top: 2px;
          margin-bottom: 2px;
        }

        .ant-select-selection-search {
          margin-top: 2px;
          margin-bottom: 2px;
        }
      }

      .ant-input-number-handler-wrap {
        background: rgba(255, 255, 255, 0.9);

        .ant-input-number-handler {
          color: rgba(0, 76, 255, 0.8);
        }
      }

      .ant-checkbox-inner,
      .ant-radio-inner {
        background-color: rgba(255, 255, 255, 0.9);
        border-color: rgba(0, 76, 255, 0.5);
        width: 14px;
        height: 14px;
      }

      .ant-checkbox-checked .ant-checkbox-inner,
      .ant-radio-checked .ant-radio-inner {
        background-color: #00cfff;
        border-color: #00cfff;
      }

      .ant-btn {
        border-radius: 6px;
        height: 36px;
        padding: 0 16px;
        font-weight: 500;
        font-size: 14px;
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);

        &.ant-btn-primary {
          background: linear-gradient(135deg, #00cfff, #004cff);
          border: none;
          box-shadow: 0 0 15px rgba(0, 207, 255, 0.5);
          position: relative;
          overflow: hidden;

          &::before {
            content: "";
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(
              to bottom right,
              rgba(255, 255, 255, 0) 0%,
              rgba(255, 255, 255, 0.1) 50%,
              rgba(255, 255, 255, 0) 100%
            );
            transform: rotate(45deg);
            animation: reflectLight 3s ease-in-out infinite;
          }

          &:hover {
            background: linear-gradient(135deg, #00cfff, #004cff);
            box-shadow: 0 0 15px rgba(0, 207, 255, 0.5);
          }
        }

        &.ant-btn-default {
          background: rgba(15, 23, 42, 0.6);
          border: 1px solid rgba(0, 207, 255, 0.3);
          color: rgba(255, 255, 255, 0.85);

          &:hover {
            border-color: #00cfff;
            color: #00cfff;
            box-shadow: 0 0 10px rgba(0, 207, 255, 0.3);
          }
        }
      }

      .ant-form-item {
        margin-bottom: 10px;
      }

      .ant-row {
        row-gap: 8px !important;
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 48px 0;

    .ant-spin {
      .ant-spin-dot-item {
        background-color: #00cfff;
      }

      .ant-spin-text {
        margin-top: 16px;
        color: #00cfff;
        font-size: 16px;
        text-shadow: 0 0 5px rgba(0, 207, 255, 0.5);
      }
    }

    .hud-loading {
      position: relative;
      width: 100px;
      height: 100px;

      &::before,
      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 2px solid transparent;
        border-top-color: #00cfff;
        border-bottom-color: #004cff;
        animation: hudSpin 1.5s linear infinite;
      }

      &::after {
        border-top-color: #004cff;
        border-bottom-color: #00cfff;
        animation-duration: 3s;
      }
    }
  }

  .history-card {
    height: 100%;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    overflow: hidden;
    border: 1px solid rgba(0, 76, 255, 0.1);
    box-shadow:
      0 10px 30px rgba(0, 76, 255, 0.1),
      inset 0 0 10px rgba(0, 207, 255, 0.05);

    .history-records {
      .history-title {
        color: #002766;
        margin-bottom: 16px;
        font-size: 16px;
        display: flex;
        align-items: center;

        .anticon {
          margin-right: 8px;
          color: #00cfff;
        }
      }

      .history-list {
        .history-item {
          padding: 12px;
          border-radius: 8px;
          background: rgba(240, 245, 255, 0.5);
          margin-bottom: 12px;
          cursor: pointer;
          transition: all 0.3s;
          border: 1px solid rgba(0, 76, 255, 0.1);

          .history-item-title {
            font-weight: 500;
            color: #002766;
            margin-bottom: 8px;
          }

          .history-item-info {
            display: flex;
            justify-content: space-between;
            color: rgba(0, 0, 0, 0.45);
            font-size: 12px;
          }
        }
      }
    }
  }

  .results-card {
    margin: 0 auto 24px;
    max-width: 1200px;
    width: calc(100% - 48px);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    overflow: hidden;
    border: 1px solid rgba(0, 76, 255, 0.1);
    box-shadow:
      0 10px 30px rgba(0, 76, 255, 0.1),
      inset 0 0 10px rgba(0, 207, 255, 0.05);
    animation: fadeInUp 0.5s ease-out;

    .results-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 16px 24px 0;

      h4 {
        margin: 0;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.85);
        text-shadow: 0 0 5px rgba(0, 207, 255, 0.2);

        .anticon {
          margin-right: 8px;
          color: #00cfff;
        }
      }
    }

    .quotation-table {
      .ant-table {
        background: transparent;
        color: rgba(0, 0, 0, 0.85);
      }

      .ant-table-thead > tr > th {
        background: rgba(0, 76, 255, 0.1);
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
        border-bottom: 1px solid rgba(0, 76, 255, 0.2);
      }

      .ant-table-tbody > tr > td {
        border-bottom: 1px solid rgba(0, 76, 255, 0.1);
        transition: all 0.3s;
      }

      .ant-table-tbody > tr:hover > td {
        background: rgba(0, 207, 255, 0.05);
      }

      .ant-table-row {
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 0 10px rgba(0, 76, 255, 0.1);
        }
      }

      .ant-empty {
        color: rgba(0, 0, 0, 0.45);

        .ant-empty-img-simple-path {
          stroke: rgba(0, 76, 255, 0.3);
        }

        .ant-empty-img-simple-ellipse {
          fill: rgba(0, 76, 255, 0.1);
        }
      }

      .ant-tag {
        border-radius: 4px;
        padding: 0 8px;
        font-weight: 500;
      }

      .ant-tag-success {
        background: rgba(0, 207, 255, 0.1);
        border-color: rgba(0, 207, 255, 0.3);
        color: #0080ff;
      }

      .ant-tag-error {
        background: rgba(255, 77, 79, 0.1);
        border-color: rgba(255, 77, 79, 0.3);
        color: #f5222d;
      }

      .ant-btn {
        border-radius: 6px;

        &.ant-btn-primary {
          background: linear-gradient(135deg, #00cfff, #004cff);
          border: none;
          box-shadow: 0 0 10px rgba(0, 76, 255, 0.2);

          &:hover {
            background: linear-gradient(135deg, #00cfff, #004cff);
            box-shadow: 0 0 15px rgba(0, 76, 255, 0.3);
          }

          &[disabled] {
            background: rgba(0, 0, 0, 0.04);
            color: rgba(0, 0, 0, 0.25);
            box-shadow: none;
          }
        }
      }

      .ant-pagination {
        .ant-pagination-item {
          background: rgba(255, 255, 255, 0.8);
          border-color: rgba(0, 76, 255, 0.2);

          a {
            color: rgba(0, 0, 0, 0.85);
          }

          &-active {
            background: rgba(0, 207, 255, 0.1);
            border-color: #00cfff;

            a {
              color: #0080ff;
            }
          }
        }

        .ant-pagination-prev,
        .ant-pagination-next {
          .ant-pagination-item-link {
            background: rgba(255, 255, 255, 0.8);
            border-color: rgba(0, 76, 255, 0.2);
            color: rgba(0, 0, 0, 0.85);
          }
        }
      }
    }
  }
}

@keyframes reflectLight {
  0% {
    transform: translate(-100%, -100%) rotate(45deg);
  }
  100% {
    transform: translate(100%, 100%) rotate(45deg);
  }
}

@keyframes hudSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes backgroundMove {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 60px 60px;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float-particle {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(10px, 10px);
  }
  50% {
    transform: translate(0, 20px);
  }
  75% {
    transform: translate(-10px, 10px);
  }
  100% {
    transform: translate(0, 0);
  }
}

:global {
  .ant-select-dropdown {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 76, 255, 0.1);
    box-shadow: 0 0 15px rgba(0, 76, 255, 0.2);

    .ant-select-item {
      color: rgba(0, 0, 0, 0.85);

      &-option-active {
        background: rgba(0, 207, 255, 0.1);
      }

      &-option-selected {
        background: rgba(0, 207, 255, 0.2);
        color: #004cff;
      }
    }
  }

  .ant-picker-dropdown {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 76, 255, 0.1);
    box-shadow: 0 0 15px rgba(0, 76, 255, 0.2);

    .ant-picker-panel-container {
      background: transparent;
    }

    .ant-picker-header {
      color: rgba(0, 0, 0, 0.85);
      border-bottom: 1px solid rgba(0, 76, 255, 0.1);

      button {
        color: rgba(0, 0, 0, 0.6);

        &:hover {
          color: #00cfff;
        }
      }
    }

    .ant-picker-content th {
      color: rgba(0, 0, 0, 0.6);
    }

    .ant-picker-cell {
      color: rgba(0, 0, 0, 0.85);

      &-in-view {
        &.ant-picker-cell-selected .ant-picker-cell-inner {
          background: #00cfff;
        }

        &.ant-picker-cell-today .ant-picker-cell-inner::before {
          border-color: #00cfff;
        }
      }
    }
  }

  .results-actions {
    margin-top: 24px;
    display: flex;
    justify-content: center;

    .ant-btn {
      min-width: 120px;
      height: 40px;
      border-radius: 8px;

      &.ant-btn-primary {
        background: linear-gradient(135deg, #00cfff, #004cff);
        border: none;
        box-shadow: 0 0 15px rgba(0, 76, 255, 0.2);

        &:hover {
          box-shadow: 0 0 15px rgba(0, 76, 255, 0.3);
        }
      }
    }
  }
}
