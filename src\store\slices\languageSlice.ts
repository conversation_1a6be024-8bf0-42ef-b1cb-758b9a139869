import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import i18n from "../../i18n";

export type Language = "zh-CN" | "en-US";

interface LanguageState {
  currentLanguage: Language;
}

const initialState: LanguageState = {
  currentLanguage: (localStorage.getItem("i18nextLng") as Language) || "zh-CN",
};

const languageSlice = createSlice({
  name: "language",
  initialState,
  reducers: {
    setLanguage: (state, action: PayloadAction<Language>) => {
      state.currentLanguage = action.payload;
      // 同步更新i18n语言
      i18n.changeLanguage(action.payload);
      // 保存到localStorage
      localStorage.setItem("i18nextLng", action.payload);
    },
  },
});

export const { setLanguage } = languageSlice.actions;
export default languageSlice.reducer;
