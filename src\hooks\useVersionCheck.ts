import { useState, useEffect, useCallback } from "react";
import { Modal } from "antd";
import { useTranslation } from "react-i18next";

interface VersionInfo {
  version: string;
  gitHash: string;
  shortHash: string;
  branch: string;
  isDirty: boolean;
  lastCommitTime: string;
  buildTime: string;
  timestamp: number;
}

interface UseVersionCheckOptions {
  /** 检查间隔时间（毫秒），默认30秒 */
  checkInterval?: number;
  /** 是否启用版本检查，默认true */
  enabled?: boolean;
  /** 是否在开发环境中启用，默认false */
  enableInDevelopment?: boolean;
}

/**
 * 版本检查Hook
 * 基于Git提交哈希检测代码更新，只有代码变更时才提示用户刷新
 */
export const useVersionCheck = (options: UseVersionCheckOptions = {}) => {
  const {
    checkInterval = 30000, // 30秒检查一次
    enabled = true,
    enableInDevelopment = false,
  } = options;

  const { t } = useTranslation();
  const [currentVersion, setCurrentVersion] = useState<string>("");
  const [isChecking, setIsChecking] = useState(false);

  // 获取版本信息
  const fetchVersion = useCallback(async (): Promise<VersionInfo | null> => {
    try {
      const response = await fetch("/version.json?t=" + Date.now(), {
        cache: "no-cache",
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const versionInfo: VersionInfo = await response.json();
      return versionInfo;
    } catch (error) {
      console.warn("获取版本信息失败:", error);
      return null;
    }
  }, []);

  // 检查版本更新
  const checkForUpdates = useCallback(async () => {
    if (isChecking) return;

    setIsChecking(true);

    try {
      const versionInfo = await fetchVersion();

      if (!versionInfo) {
        return;
      }

      // 首次获取版本信息
      if (!currentVersion) {
        setCurrentVersion(versionInfo.version);
        console.log("当前版本:", versionInfo.version);
        return;
      }

      // 检查版本是否有变化
      if (currentVersion !== versionInfo.version) {
        console.log("检测到新版本:", {
          current: currentVersion,
          new: versionInfo.version,
        });

        // 显示更新提示
        Modal.confirm({
          title: t("common.systemUpdate"),
          content: t("common.updateDetected"),
          okText: t("common.refreshNow"),
          cancelText: t("common.remindLater"),
          onOk: () => {
            window.location.reload();
          },
          onCancel: () => {
            // 用户选择稍后提醒，更新当前版本避免重复提示
            setCurrentVersion(versionInfo.version);
          },
        });
      }
    } catch (error) {
      console.warn("版本检查失败:", error);
    } finally {
      setIsChecking(false);
    }
  }, [currentVersion, fetchVersion, isChecking, t]);

  // 手动检查更新
  const manualCheck = useCallback(async () => {
    await checkForUpdates();
  }, [checkForUpdates]);

  useEffect(() => {
    // 判断是否应该启用版本检查
    const isDevelopment = process.env.NODE_ENV === "development";
    const shouldEnable = enabled && (!isDevelopment || enableInDevelopment);

    if (!shouldEnable) {
      return;
    }

    // 初始化时获取当前版本
    fetchVersion().then((versionInfo) => {
      if (versionInfo) {
        setCurrentVersion(versionInfo.version);
        console.log("初始版本:", versionInfo.version);
      }
    });

    // 设置定时检查
    const timer = setInterval(checkForUpdates, checkInterval);

    // 页面获得焦点时检查更新（用户切换回页面时）
    const handleFocus = () => {
      checkForUpdates();
    };

    window.addEventListener("focus", handleFocus);

    return () => {
      clearInterval(timer);
      window.removeEventListener("focus", handleFocus);
    };
  }, [
    enabled,
    enableInDevelopment,
    checkInterval,
    fetchVersion,
    checkForUpdates,
  ]);

  return {
    currentVersion,
    isChecking,
    manualCheck,
  };
};
