import React from "react";
import {
  Modal,
  Typography,
  Descriptions,
  Button,
  Rate,
  Tag,
  Divider,
} from "antd";
import { RocketOutlined, CheckCircleOutlined } from "@ant-design/icons";
import { QuotationResult } from "@/pages/AIQuotation/types";

const { Title, Text } = Typography;

interface QuotationDetailModalProps {
  visible: boolean;
  quotation: QuotationResult | null;
  onClose: () => void;
  onSave: () => void;
}

const QuotationDetailModal: React.FC<QuotationDetailModalProps> = ({
  visible,
  quotation,
  onClose,
  onSave,
}) => {
  if (!quotation) return null;

  return (
    <Modal
      open={visible}
      title={
        <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
          <RocketOutlined style={{ color: "#2188e8", fontSize: "20px" }} />
          <span>报价详情</span>
        </div>
      }
      onCancel={onClose}
      width={700}
      footer={[
        <Button key="back" onClick={onClose}>
          关闭
        </Button>,
        <Button
          key="save"
          type="primary"
          icon={<CheckCircleOutlined />}
          onClick={onSave}
        >
          保存报价
        </Button>,
      ]}
      className="quotation-detail-modal"
    >
      <div className="quotation-detail-content">
        <div className="quotation-header">
          <Title level={4}>{quotation.airline}</Title>
          {quotation.recommended && (
            <Tag color="#2188e8" className="recommendation-tag">
              推荐选择
            </Tag>
          )}
        </div>

        <Divider />

        <Descriptions bordered column={2}>
          <Descriptions.Item label="起始港" span={1}>
            {quotation.originPort}
          </Descriptions.Item>
          <Descriptions.Item label="目的港" span={1}>
            {quotation.destinationPort}
          </Descriptions.Item>
          <Descriptions.Item label="价格" span={1}>
            <Text strong style={{ fontSize: "18px", color: "#2188e8" }}>
              {quotation.price} {quotation.currency}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="运输时间" span={1}>
            {quotation.transitTime}
          </Descriptions.Item>
          <Descriptions.Item label="航班频率" span={2}>
            {quotation.departureSchedule.map((day, index) => (
              <Tag key={index} color="blue" style={{ margin: "0 4px 4px 0" }}>
                {day}
              </Tag>
            ))}
          </Descriptions.Item>
          <Descriptions.Item label="有效期至" span={1}>
            {quotation.validUntil}
          </Descriptions.Item>
          <Descriptions.Item label="服务等级" span={1}>
            {quotation.serviceLevel}
          </Descriptions.Item>
          <Descriptions.Item label="评分" span={2}>
            <Rate disabled defaultValue={quotation.rating || 0} allowHalf />
            <Text style={{ marginLeft: "10px" }}>
              {quotation.rating?.toFixed(1)}分
            </Text>
          </Descriptions.Item>
        </Descriptions>

        <Divider />

        <div className="additional-info">
          <Title level={5}>附加信息</Title>
          <Text>
            此报价由AI智能系统基于您提供的需求生成，包含了最优的价格和服务组合。
            选择此报价后，您可以进一步与客服人员联系，获取更多定制化服务。
          </Text>
        </div>
      </div>
    </Modal>
  );
};

export default QuotationDetailModal;
