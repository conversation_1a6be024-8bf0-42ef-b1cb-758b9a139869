// 定义变量 - 科幻主题
@primary-color: #2188e8; // 能量蓝
@secondary-color: #76c7f2; // 冰蓝
@accent-color: #d0d9e2; // 科技银灰
@dark-color: #0d1b30;
@light-color: #ffffff;
@text-color: #333333;

// 渐变
@primary-gradient: linear-gradient(135deg, #52cafe, #2188e8); // 能量蓝光渐变
@secondary-gradient: linear-gradient(135deg, #76c7f2, #52cafe);
@dark-gradient: linear-gradient(135deg, #0d1b30, #1a365d);
@futuristic-gradient: linear-gradient(
  90deg,
  rgba(82, 202, 254, 0.1),
  rgba(33, 136, 232, 0.1)
);
@holographic-gradient: linear-gradient(
  135deg,
  rgba(255, 255, 255, 0.1),
  rgba(255, 255, 255, 0.05)
);

// 效果
@glow-effect: 0 0 15px rgba(82, 202, 254, 0.4);
@neon-glow: 0 0 10px rgba(33, 136, 232, 0.5);
@card-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
@futuristic-border: 1px solid rgba(82, 202, 254, 0.3);
@glass-effect: rgba(255, 255, 255, 0.05);
@glass-border: 1px solid rgba(255, 255, 255, 0.1);
@transition-time: 0.3s;

// 主容器
.smart-quotation-container {
  height: 100%;
  background: linear-gradient(135deg, #e9f0f9, #f5faff); // 明亮星空蓝背景
  position: relative;
  overflow: hidden;
  color: #333333;
  display: flex;
  flex-direction: column;

  // 内容区域
  .smart-quotation-content {
    position: relative;
    z-index: 1;
    padding: 20px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0px !important;
    flex: 1;
  }

  // 主界面容器 - 双面板设计
  .smart-interface-container {
    width: 100%;
    max-width: 1400px;
    height: calc(100vh - 120px);
    display: flex;
    gap: 20px;
    position: relative;
  }

  // 3D交互控制台容器
  .command-console-container {
    width: 65%;
    height: 100%;
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;

    // 3D背景容器
    .canvas-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 0;
    }

    // 控制台头部
    .console-header {
      padding: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      z-index: 1;
      border-bottom: @futuristic-border;
      background: rgba(255, 255, 255, 0.5);
      backdrop-filter: blur(5px);
      -webkit-backdrop-filter: blur(5px);

      .panel-title {
        display: flex;
        align-items: center;
        gap: 10px;

        .panel-icon {
          font-size: 24px;
          color: @primary-color;
        }

        h4 {
          margin: 0;
          color: @dark-color;
        }
      }

      .panel-subtitle {
        color: rgba(0, 0, 0, 0.65);
        font-size: 14px;
      }
    }

    // 控制台内容区
    .console-content {
      flex: 1;
      padding: 20px;
      position: relative;
      z-index: 1;
      overflow-y: auto;
      display: flex;
      flex-direction: column;

      // 步骤指示器
      .step-indicator {
        margin-bottom: 20px;
        display: flex;
        justify-content: center;
        position: relative;
        z-index: 2;

        .step-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 0 15px;
          position: relative;
          cursor: pointer;

          .step-number {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: @light-color;
            border: 2px solid @accent-color;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 600;
            color: @accent-color;
            transition: all @transition-time;
            position: relative;
            z-index: 2;
          }

          .step-label {
            margin-top: 8px;
            font-size: 12px;
            color: @accent-color;
            transition: all @transition-time;
          }

          &.active {
            .step-number {
              background: @primary-gradient;
              border-color: @primary-color;
              color: white;
              box-shadow: @glow-effect;
            }

            .step-label {
              color: @primary-color;
              font-weight: 500;
            }
          }

          &.completed {
            .step-number {
              background: @secondary-gradient;
              border-color: @secondary-color;
              color: white;
            }

            .step-label {
              color: @secondary-color;
            }
          }

          &:not(:last-child)::after {
            content: "";
            position: absolute;
            top: 18px;
            left: 45px;
            width: 60px;
            height: 2px;
            background: @accent-color;
            z-index: 1;
          }

          &.completed:not(:last-child)::after {
            background: @secondary-color;
          }
        }
      }

      // 交互区域
      .interaction-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: rgba(255, 255, 255, 0.5);
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        border: @futuristic-border;
        position: relative;
        z-index: 2;

        .step-title {
          font-size: 20px;
          font-weight: 600;
          color: @dark-color;
          margin-bottom: 10px;
        }

        .step-description {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);
          margin-bottom: 20px;
        }

        .input-area {
          flex: 1;
          margin-bottom: 20px;
        }

        .visualization-area {
          flex: 1;
          min-height: 300px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 20px;
        }

        .action-buttons {
          display: flex;
          justify-content: space-between;
          margin-top: auto;

          .ant-btn {
            min-width: 100px;
            height: 40px;
            border-radius: 20px;
            font-weight: 500;
            transition: all @transition-time;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            &.ant-btn-primary {
              background: @primary-gradient;
              border: none;
              box-shadow: @neon-glow;

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(33, 136, 232, 0.3);
              }
            }

            &.ant-btn-default {
              border: 1px solid @accent-color;
              color: @dark-color;

              &:hover {
                border-color: @primary-color;
                color: @primary-color;
              }
            }
          }
        }
      }
    }
  }

  // 已保存报价列表容器
  .saved-quotations-container {
    width: 35%;
    height: 100%;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 12px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.8);
    position: relative;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    // 3D背景容器
    .canvas-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 0;
    }

    // 列表头部
    .list-header {
      padding: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      z-index: 1;
      border-bottom: @futuristic-border;
      background: rgba(255, 255, 255, 0.5);
      backdrop-filter: blur(5px);
      -webkit-backdrop-filter: blur(5px);

      .panel-title {
        display: flex;
        align-items: center;
        gap: 10px;

        .panel-icon {
          font-size: 24px;
          color: @primary-color;
        }

        h4 {
          margin: 0;
          color: @dark-color;
        }
      }
    }

    // 列表内容区
    .list-content {
      flex: 1;
      padding: 20px;
      position: relative;
      z-index: 1;
      overflow-y: auto;

      .empty-list {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: rgba(0, 0, 0, 0.45);

        .empty-icon {
          font-size: 48px;
          margin-bottom: 16px;
          color: @accent-color;
        }
      }
    }
  }
}
