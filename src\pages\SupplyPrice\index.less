@card-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
@border-radius: 8px;
@transition-time: 0.3s;
@hover-bg: #f5f7fa;
@active-border: 2px solid @primary-color;
@card-glow: 0 0 10px rgba(0, 120, 255, 0.2);
@card-glow-hover: 0 0 12px rgba(0, 120, 255, 0.4);
@card-glow-active: 0 0 15px rgba(0, 120, 255, 0.4);

@large-screen: ~"(min-width: 1600px)";
@extra-large-screen: ~"(min-width: 1920px)";

.supply_price {
  display: flex;
  height: 100%;
  gap: 10px;
  overflow: hidden;

  .supply_price_left {
    width: 25%;
    background-color: rgba(255, 255, 255, 0.95);
    height: 100%;
    border-radius: 10px;
    box-shadow: @card-shadow;
    padding: 20px 0px 0px 20px;
    position: relative;
    transition: all 0.3s ease;
    overflow-y: auto;
    border: 1px solid rgba(0, 120, 255, 0.1);
    backdrop-filter: blur(10px);
    z-index: 1;
  }

  .supply_price_right {
    width: calc(75% - 10px);
    background-color: #fff;
    height: 100%;
    padding: 0;
    border-radius: @border-radius;
    box-shadow: @card-shadow;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .table_top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 20px;
      border-bottom: 1px solid #f0f0f0;
      flex-shrink: 0;
      gap: 16px;
      min-height: 60px;

      @media (max-width: 1200px) {
        gap: 12px;
        padding: 8px 16px;
      }

      @media (max-width: 1000px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        min-height: auto;
      }

      @media (max-width: 768px) {
        padding: 8px 12px;
        gap: 6px;
      }

      .table_title {
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
        position: relative;
        padding-left: 12px;
        display: flex;
        align-items: center;
        gap: 12px;
        flex-shrink: 0;
        white-space: nowrap;

        @media (max-width: 1200px) {
          font-size: 16px;
          gap: 10px;
        }

        @media (max-width: 1000px) {
          font-size: 15px;
          gap: 8px;
          margin-bottom: 4px;
        }

        @media (max-width: 768px) {
          font-size: 14px;
          gap: 6px;
        }

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 18px;
          background-color: @primary-color;
          border-radius: 2px;
        }

        .selected-tag {
          font-size: 13px;
          font-weight: normal;
          padding: 2px 8px;
          margin-left: 12px;
          border-radius: 4px;
          display: inline-flex;
          align-items: center;
          height: auto;
          line-height: 1.5;

          .anticon-close {
            font-size: 10px;
            margin-left: 6px;
          }
        }
      }

      .button-group {
        display: flex;
        gap: 12px;
      }

      .filter-sort-controls {
        display: flex;
        gap: 12px;
        align-items: center;
        justify-content: end;
        flex-wrap: wrap;
        flex: 1;
        min-width: 0;

        @media (max-width: 1400px) {
          gap: 10px;
        }

        @media (max-width: 1200px) {
          gap: 8px;
        }

        @media (max-width: 1000px) {
          gap: 6px;

          .ant-select,
          .ant-picker {
            min-width: 100px !important;
            max-width: 140px !important;
          }
        }

        @media (max-width: 768px) {
          justify-content: flex-start;

          .ant-select,
          .ant-picker {
            min-width: 90px !important;
            max-width: 120px !important;
          }
        }

        .ant-select {
          flex-shrink: 0;

          .ant-select-selector {
            border-radius: 6px;
            border: 1px solid #d9d9d9;
            transition: all 0.3s ease;

            &:hover {
              border-color: #1890ff;
            }
          }

          &.ant-select-focused .ant-select-selector {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }

        .ant-picker {
          flex-shrink: 0;
        }

        .ant-picker.ant-picker-multiple {
          overflow: hidden;

          &:hover {
            border-color: #1890ff;
          }

          &.ant-picker-focused {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }

          .ant-picker-selection-overflow {
            gap: 4px;
          }

          .ant-picker-selection-overflow-item {
            flex-shrink: 0;
            max-width: 65px;

            .ant-picker-selection-item {
              height: 20px;
              font-size: 12px;
              display: flex;
              align-items: center;

              .ant-picker-selection-item-remove {
                font-size: 10px;
                margin-left: 4px;
                opacity: 0.7;
                flex-shrink: 0;
                width: 12px;
                height: 12px;
                display: flex;
                align-items: center;
                justify-content: center;

                &:hover {
                  opacity: 1;
                  background: rgba(24, 144, 255, 0.2);
                  border-radius: 2px;
                }
              }
            }
          }

          .ant-picker-selection-overflow-item-suffix {
            flex-shrink: 0;

            .ant-picker-selection-item {
              background: rgba(140, 140, 140, 0.1);
              border-color: rgba(140, 140, 140, 0.2);
              color: #8c8c8c;
              font-size: 12px;
              padding: 0 6px;
              height: 20px;
              line-height: 18px;
              max-width: 40px;
              border-radius: 3px;
            }
          }

          .ant-picker-selection-placeholder {
            line-height: 24px;
            color: #bfbfbf;
            font-size: 14px;
            padding: 0;
            margin: 0;
            flex: 1;
          }

          .ant-picker-suffix {
            position: absolute;
            right: 11px;
            top: 50%;
            transform: translateY(-50%);
            color: #bfbfbf;
            font-size: 12px;
            z-index: 10;

            &:hover {
              color: #8c8c8c;
            }
          }
        }
      }

      .sort-buttons {
        display: flex;
        gap: 8px;
        align-items: center;
        flex-shrink: 0;

        @media (max-width: 1200px) {
          gap: 6px;
        }

        @media (max-width: 1000px) {
          flex-wrap: wrap;
          gap: 4px;
        }

        @media (max-width: 768px) {
          width: 100%;
          justify-content: flex-start;
          margin-top: 4px;
        }

        .sort-button {
          display: flex;
          align-items: center;
          gap: 4px;
          border-radius: 6px;
          transition: all 0.3s ease;
          position: relative;
          font-weight: 500;
          font-size: 12px;
          height: 28px;
          padding: 0 8px;
          white-space: nowrap;
          flex-shrink: 0;

          @media (max-width: 1200px) {
            font-size: 11px;
            height: 26px;
            padding: 0 6px;
            gap: 3px;
          }

          @media (max-width: 1000px) {
            font-size: 10px;
            height: 24px;
            padding: 0 4px;
            gap: 2px;
          }

          &.ant-btn-default {
            border: 1px solid #d9d9d9;
            background: #fff;
            color: #666;

            &:hover {
              border-color: #1890ff;
              color: #1890ff;
              transform: translateY(-1px);
              box-shadow: 0 2px 6px rgba(24, 144, 255, 0.15);
            }

            &::after {
              content: "↕";
              font-size: 12px;
              margin-left: 2px;
              opacity: 0.5;
            }
          }

          &.ant-btn-primary {
            background: linear-gradient(135deg, #1890ff, #096dd9);
            border: none;
            color: #fff;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);

            &:hover {
              background: linear-gradient(135deg, #40a9ff, #1890ff);
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
            }

            &::after {
              content: "↓";
              font-size: 12px;
              margin-left: 2px;
              opacity: 0.9;
              animation: sortPulse 2s ease-in-out infinite;
            }
          }

          .anticon {
            font-size: 14px;
          }
        }
      }

      @keyframes sortPulse {
        0%,
        100% {
          opacity: 0.9;
          transform: scale(1);
        }
        50% {
          opacity: 1;
          transform: scale(1.1);
        }
      }
    }

    .price-cards-container {
      padding: 20px 20px;
      flex: 1;
      overflow-y: auto;

      .ant-row {
        align-items: flex-start;
      }

      scrollbar-width: thin;
      scrollbar-color: #eaeaea transparent;
      scrollbar-gutter: stable;

      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #eaeaea;
        border-radius: 4px;
        border: 2px solid transparent;
      }

      &::-webkit-scrollbar-thumb:hover {
        background-color: #d0d0d0;
      }

      .price-card {
        position: relative;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 20px;
        transition: all 0.3s ease;
        cursor: pointer;
        border: 1px solid rgba(0, 120, 255, 0.1);
        box-shadow: @card-shadow;
        overflow: visible;
        z-index: 1;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border-radius: 12px;
          padding: 1px;
          background: linear-gradient(
            135deg,
            rgba(0, 120, 255, 0.15),
            rgba(0, 60, 255, 0.08),
            rgba(0, 120, 255, 0.15)
          );
          mask:
            linear-gradient(#fff 0 0) content-box,
            linear-gradient(#fff 0 0);
          -webkit-mask:
            linear-gradient(#fff 0 0) content-box,
            linear-gradient(#fff 0 0);
          -webkit-mask-composite: xor;
          mask-composite: exclude;
          pointer-events: none;
        }

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 25px rgba(0, 120, 255, 0.15);
          border-color: rgba(0, 120, 255, 0.25);
          z-index: 10;

          &::before {
            background: linear-gradient(
              135deg,
              rgba(0, 120, 255, 0.25),
              rgba(0, 60, 255, 0.12),
              rgba(0, 120, 255, 0.25)
            );
          }
        }

        .price-card-main {
          display: flex;
          align-items: stretch;
          gap: 20px;
          min-height: 80px;

          .price-card-left {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 12px;

            .card-row-primary {
              display: flex;
              align-items: center;
              gap: 20px;
              margin-top: 20px;

              .airline-section {
                flex-shrink: 0;
                width: 80px;

                .icon-plane {
                  font-size: 18px;
                  margin-right: 6px;
                  color: #2563eb;
                }

                .icon-label {
                  font-size: 14px;
                  color: #666;
                  font-weight: bold;
                }
              }

              .unit-price-section {
                flex-shrink: 0;
                text-align: center;
                min-width: 150px;
                margin-right: 40px;
                margin-left: 10px;

                .price-label {
                  font-size: 12px;
                  color: #666;
                  margin-bottom: 2px;
                }

                .price-value {
                  font-size: 14px;
                  font-weight: 600;
                  color: #1890ff;
                }
              }
            }

            .card-row-secondary {
              padding: 8px 12px;
              background-color: rgba(240, 248, 255, 0.3);
              border-radius: 6px;
              border: 1px solid rgba(0, 120, 255, 0.1);
              min-height: 36px;

              &.horizontal-layout {
                display: flex;
                align-items: center;
                gap: 20px;
                justify-content: space-between;
              }

              .info-item-horizontal {
                flex: 1;
                min-width: 0;

                .info-content {
                  display: flex;
                  align-items: center;
                  gap: 4px;
                  font-size: 12px;
                  line-height: 1.4;

                  .info-icon {
                    color: #1890ff;
                    font-size: 12px;
                    flex-shrink: 0;
                  }

                  .info-label {
                    color: #666;
                    font-weight: 500;
                    white-space: nowrap;
                    flex-shrink: 0;
                  }

                  .info-value {
                    color: #333;
                    font-weight: 600;
                    margin-left: 2px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }
                }
              }

              .info-item {
                flex: 1;
                min-width: 0;

                .info-label {
                  font-size: 11px;
                  color: #666;
                  margin-bottom: 3px;
                  display: flex;
                  align-items: center;
                  font-weight: 500;
                  line-height: 1.4;

                  .info-icon {
                    margin-right: 3px;
                    font-size: 11px;
                  }
                }

                .info-value {
                  font-size: 13px;
                  color: #333;
                  font-weight: 600;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  line-height: 1.5;
                }
              }
            }
          }

          .price-card-right {
            flex-shrink: 0;
            width: 180px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-left: 1px solid rgba(0, 120, 255, 0.1);
            padding-left: 20px;

            .total-price-section {
              text-align: center;
              width: 100%;

              .total-price {
                margin-bottom: 12px;
              }

              .action-buttons {
                .generate-button {
                  width: 100%;
                  height: 36px;
                  border-radius: 6px;
                  font-weight: 600;
                }
              }
            }
          }

          .route-section {
            flex: 1;
            min-width: 250px;
            max-width: 400px;

            .route-path {
              display: flex;
              align-items: center;
              gap: 8px;
              flex-wrap: wrap;

              .port {
                font-weight: 500;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 13px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;

                &.origin-port {
                  color: #1890ff;
                  background-color: rgba(24, 144, 255, 0.1);
                  max-width: 120px;
                }

                &.transfer-port {
                  color: #722ed1;
                  background-color: rgba(114, 46, 209, 0.1);
                  max-width: 120px;
                }

                &.destination-port {
                  color: #fa8c16;
                  background-color: rgba(250, 140, 22, 0.1);
                  max-width: 200px;
                }
              }

              .route-icon {
                color: #8c8c8c;
                font-size: 12px;
                flex-shrink: 0;
              }
            }
          }

          .unit-price-section {
            flex-shrink: 0;
            width: 305px;
            text-align: center;

            .price-label {
              font-size: 11px;
              color: #8c8c8c;
              margin-bottom: 3px;
            }

            .price-value {
              font-size: 13px;
              font-weight: 500;
              color: #1890ff;
              line-height: 1.3;

              .price-currency {
                font-size: 12px;
                margin-right: 1px;
              }

              .price-amount {
                font-weight: 600;
              }
            }
          }

          .validity-section {
            flex-shrink: 0;
            width: 100px;
            text-align: center;

            .info-label {
              font-size: 12px;
              color: #8c8c8c;
              margin-bottom: 4px;
            }

            .info-value {
              font-size: 14px;
              font-weight: 500;
              color: #1f2937;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 4px;

              .info-icon {
                font-size: 12px;
                color: #1890ff;
              }
            }
          }

          .departure-section {
            flex-shrink: 0;
            width: 100px;
            text-align: center;

            .info-label {
              font-size: 12px;
              color: #8c8c8c;
              margin-bottom: 4px;
              font-weight: 500;
            }

            .info-value {
              font-size: 15px;
              font-weight: 700;
              color: #fa8c16;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 4px;
              background: rgba(250, 140, 22, 0.1);
              padding: 4px 8px;
              border-radius: 4px;
              border: 1px solid rgba(250, 140, 22, 0.2);

              .info-icon {
                font-size: 13px;
                color: #fa8c16;
              }
            }
          }

          .price-divider {
            width: 1px;
            height: 50px;
            background: linear-gradient(
              to bottom,
              transparent,
              #e5e7eb,
              transparent
            );
            flex-shrink: 0;
            margin: 0 16px;
          }

          .total-price-section {
            flex-shrink: 0;
            width: 150px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 12px;

            .total-price {
              text-align: center;

              .ant-statistic {
                .ant-statistic-title {
                  font-size: 12px;
                  color: #8c8c8c;
                  margin-bottom: 4px;
                }

                .ant-statistic-content {
                  .ant-statistic-content-value {
                    color: #e53e3e !important;
                    font-weight: 800 !important;
                    font-size: 22px !important;
                  }

                  .ant-statistic-content-prefix {
                    color: #e53e3e !important;
                    font-weight: 700 !important;
                    font-size: 16px !important;
                  }
                }
              }
            }

            .action-buttons {
              display: flex;
              justify-content: center;

              .generate-button {
                background: #1890ff;
                border: none;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                font-size: 14px;
                height: 36px;
                padding: 0 24px;
                transition: all 0.3s;
                box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);

                &:hover {
                  background: #40a9ff;
                  transform: translateY(-1px);
                  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
                }
              }
            }
          }
        }

        .expanded-content {
          margin-top: 16px;
          padding: 16px;
          background-color: rgba(240, 248, 255, 0.3);
          border-radius: 8px;
          border: 1px solid rgba(0, 120, 255, 0.1);

          .card-divider {
            margin: 0 0 16px 0;
            border-color: rgba(0, 120, 255, 0.1);
          }

          .price-detail-grid {
            margin-bottom: 16px;

            .detail-item {
              background-color: rgba(255, 255, 255, 0.8);
              border-radius: 6px;
              padding: 8px 12px;
              height: 100%;
              border: 1px solid rgba(0, 120, 255, 0.05);
              transition: all 0.2s ease;

              &:hover {
                background-color: rgba(240, 248, 255, 0.8);
                border-color: rgba(0, 120, 255, 0.1);
              }

              .detail-label {
                font-size: 11px;
                color: #8c8c8c;
                margin-bottom: 4px;
                display: flex;
                align-items: center;
                gap: 4px;

                .anticon {
                  color: #1890ff;
                  font-size: 12px;
                }
              }

              .detail-value {
                font-size: 13px;
                color: #1f2937;
                font-weight: 500;
                line-height: 1.4;

                .ant-tag {
                  margin-right: 0;
                  padding: 0 6px;
                  line-height: 18px;
                  height: 20px;
                  font-size: 11px;
                }

                &.density-value {
                  line-height: 1.6;
                }
              }

              .size-limits {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                justify-content: space-between;

                .size-item {
                  display: flex;
                  align-items: center;
                  font-size: 12px;
                  flex: 1;
                  min-width: 0;

                  .size-label {
                    color: #8c8c8c;
                    font-weight: 500;
                    margin-right: 2px;
                  }

                  .size-value {
                    color: #1f2937;
                    font-weight: 500;
                    white-space: nowrap;
                  }
                }
              }
            }
          }

          .price-tiers-section {
            margin-bottom: 16px;

            .section-subtitle {
              font-size: 13px;
              font-weight: 500;
              color: #1f2937;
              margin-bottom: 12px;
              display: flex;
              align-items: center;

              &:before {
                content: "";
                display: inline-block;
                width: 3px;
                height: 12px;
                background-color: #1890ff;
                margin-right: 6px;
                border-radius: 2px;
              }
            }
          }

          .extra-charges-section {
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid rgba(0, 120, 255, 0.1);

            .charges-title {
              font-size: 13px;
              font-weight: 500;
              color: #1f2937;
              margin-bottom: 8px;
              display: flex;
              align-items: center;

              &:before {
                content: "";
                display: inline-block;
                width: 3px;
                height: 12px;
                background-color: #1890ff;
                margin-right: 6px;
                border-radius: 2px;
              }
            }

            .charges-list {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;

              .charge-item {
                display: flex;
                align-items: center;
                gap: 6px;
                background-color: rgba(255, 255, 255, 0.8);
                padding: 4px 8px;
                border-radius: 4px;
                border: 1px solid rgba(0, 120, 255, 0.05);

                .charge-name {
                  font-size: 12px;
                  color: #4b5563;
                }
              }
            }
          }
        }

        .expand-toggle {
          position: absolute;
          bottom: -12px;
          left: 50%;
          transform: translateX(-50%);
          background: rgba(255, 255, 255, 0.95);
          border: 1px solid rgba(0, 120, 255, 0.2);
          border-radius: 50%;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          z-index: 10;

          .expand-icon {
            font-size: 12px;
            color: #1890ff;
            transition: transform 0.3s ease;
          }

          &:hover {
            background: rgba(24, 144, 255, 0.1);
            border-color: rgba(0, 120, 255, 0.4);
            transform: translateX(-50%) scale(1.1);

            .expand-icon {
              transform: scale(1.2);
            }
          }
        }

        .feature-tags {
          position: absolute;
          top: 16px;
          left: 16px;
          display: flex;
          gap: 4px;
          flex-wrap: wrap;
          justify-content: flex-start;
          z-index: 5;

          .feature-tag {
            margin-right: 0;
            display: flex;
            align-items: center;
            gap: 2px;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            line-height: 1.2;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

            .anticon {
              font-size: 10px;
            }
          }
        }

        &.no-feature-tags {
          .price-card-main {
            .price-card-left {
              .card-row-primary {
                margin-top: 0;
              }
            }
          }
        }
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
      }

      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 300px;
        width: 100%;
      }

      .ant-empty {
        padding: 40px 0;

        .ant-empty-image {
          height: 80px;
        }

        .ant-empty-description {
          color: #6b7280;
          font-size: 15px;
        }
      }
    }

    .table-footer-info {
      padding: 16px 20px;
      color: #6b7280;
      font-size: 13px;
      display: flex;
      align-items: center;
      border-top: 1px solid #f0f0f0;
      flex-shrink: 0;

      .anticon {
        margin-right: 6px;
        color: @primary-color;
      }
    }

    .operation-buttons {
      .ant-btn {
        border-radius: 6px;

        &.edit-button {
          color: @primary-color;

          &:hover {
            background-color: #e6f7ff;
          }
        }

        &.delete-button {
          color: #ef4444;

          &:hover {
            background-color: #fef2f2;
          }
        }
      }
    }

    .select_button {
      background-color: @primary-color;
      color: #fff;
      padding: 4px 12px;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background-color: darken(@primary-color, 10%);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        color: #fff;
      }

      &:focus {
        color: #fff;
      }
    }
  }
}
