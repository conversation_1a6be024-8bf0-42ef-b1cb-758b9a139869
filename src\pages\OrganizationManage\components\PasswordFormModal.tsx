import React, { useEffect } from "react";
import { Modal, Form, Input, message } from "antd";
import { LockOutlined } from "@ant-design/icons";
import { SHA256 } from "crypto-js";
import { updatePassword } from "../services";

interface PasswordFormModalProps {
  visible: boolean;
  onCancel: () => void;
  userId: number | null;
  email: string;
}

const PasswordFormModal: React.FC<PasswordFormModalProps> = ({
  visible,
  onCancel,
  userId,
  email,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);

  // 重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();
    }
  }, [visible, form]);

  // 处理提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 检查两次密码是否一致
      if (values.newPassword !== values.confirmPassword) {
        message.error("两次输入的密码不一致");
        return;
      }

      setLoading(true);

      // 调用修改密码接口
      const response = await updatePassword({
        userid: userId,
        newpassword: SHA256(values.newPassword).toString(),
      });

      if (response.data?.resultCode === 200) {
        message.success("密码修改成功");
        onCancel(); // 关闭模态框
      } else {
        message.error(response.data?.message || "密码修改失败");
      }
    } catch (error) {
      console.error("密码修改出错:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="修改密码"
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      maskClosable={false}
      destroyOnClose
    >
      <Form form={form} layout="vertical" initialValues={{ email }}>
        <Form.Item label="用户名" name="email">
          <Input disabled />
        </Form.Item>

        <Form.Item
          label="新密码"
          name="newPassword"
          rules={[
            { required: true, message: "请输入新密码" },
            // { min: 6, message: "密码长度不能少于6个字符" },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请输入新密码"
            autoComplete="new-password"
          />
        </Form.Item>

        <Form.Item
          label="确认密码"
          name="confirmPassword"
          rules={[
            { required: true, message: "请确认新密码" },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue("newPassword") === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error("两次输入的密码不一致"));
              },
            }),
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请再次输入新密码"
            autoComplete="new-password"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PasswordFormModal;
