import React from "react";
import { Space, Tag, Button, Typography } from "antd";
import { SendOutlined } from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";

const { Text } = Typography;

// 表格列定义 - 与供应价格管理页面保持一致
export const getQuotationColumns = (): ColumnsType<any> => [
  {
    title: "编号",
    dataIndex: "priceid",
    key: "priceid",
    ellipsis: true,
    sorter: (a, b) => a.priceid?.localeCompare(b.priceid || ""),
    render: (text, record) => (
      <span className="id-column">{text || record.id}</span>
    ),
  },
  {
    title: "航空公司",
    dataIndex: "airlines",
    key: "airlines",
    width: 150,
    render: (_, record) => {
      return record.airline ? <Tag color="blue">{record.airline}</Tag> : "-";
    },
  },
  {
    title: "路线",
    key: "route",
    width: 250,
    render: (_, record) => {
      return record?.istransfer ? (
        <Space>
          <span>{record.originport || record.originPort}</span>
          <span>-</span>
          <span>{record.transfer}</span>
          <span>-</span>
          <span>{record.unloadingport || record.unloadingPort}</span>
        </Space>
      ) : (
        <Space>
          <span>{record.originport || record.originPort}</span>
          <span>-</span>
          <span>{record.unloadingport || record.unloadingPort}</span>
        </Space>
      );
    },
  },
  {
    title: "时效",
    dataIndex: "validity",
    key: "validity",
    width: 120,
    render: (text, record) =>
      record.transitTime ||
      (text ? `${text}${text > 1 ? "days" : "day"}` : "-"),
  },
  {
    title: "班次频率",
    dataIndex: "originschedules",
    key: "originschedules",
    width: 150,
    render: (text, record) =>
      record?.istransfer
        ? `${record?.originschedules}-${record?.transferschedules}`
        : text || record.flightNo || "-",
  },
  {
    title: "运费单价",
    dataIndex: "afprice",
    key: "afprice",
    width: 250,
    render: (text, record) => (
      <Text strong style={{ color: "#00CFFF" }}>
        {text || record.price} {record.currency || "CNY"}
      </Text>
    ),
  },
  {
    title: "计费重",
    dataIndex: "cwprice",
    key: "cwprice",
    width: 120,
    render: (text) => text || "-",
  },
  {
    title: "状态",
    dataIndex: "available",
    key: "available",
    filters: [
      { text: "可预订", value: true },
      { text: "已满仓", value: false },
    ],
    onFilter: (value: any, record: any) => record.available === value,
    render: (available: boolean) => (
      <Tag color={available ? "success" : "error"}>
        {available ? "可预订" : "已满仓"}
      </Tag>
    ),
  },
  {
    title: "操作",
    key: "action",
    fixed: "right",
    width: 100,
    className: "action-column",
    render: (_: any, record: any) => (
      <Space size="middle" className="operation-buttons">
        <Button
          type="primary"
          size="small"
          icon={<SendOutlined />}
          disabled={!record.available}
        >
          预订
        </Button>
      </Space>
    ),
  },
];
