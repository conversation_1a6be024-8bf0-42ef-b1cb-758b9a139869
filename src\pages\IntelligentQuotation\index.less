@primary-color: #2188e8;
@secondary-color: #76c7f2;
@accent-color: #d0d9e2;
@dark-color: #0d1b30;
@light-color: #ffffff;
@text-color: #333333;

@primary-gradient: linear-gradient(135deg, #52cafe, #2188e8);
@secondary-gradient: linear-gradient(135deg, #76c7f2, #52cafe);
@dark-gradient: linear-gradient(135deg, #0d1b30, #1a365d);
@futuristic-gradient: linear-gradient(
  90deg,
  rgba(82, 202, 254, 0.1),
  rgba(33, 136, 232, 0.1)
);
@holographic-gradient: linear-gradient(
  135deg,
  rgba(255, 255, 255, 0.1),
  rgba(255, 255, 255, 0.05)
);

@glow-effect: 0 0 15px rgba(82, 202, 254, 0.4);
@neon-glow: 0 0 10px rgba(33, 136, 232, 0.5);
@card-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
@futuristic-border: 1px solid rgba(82, 202, 254, 0.3);
@glass-effect: rgba(255, 255, 255, 0.05);
@glass-border: 1px solid rgba(255, 255, 255, 0.1);
@transition-time: 0.3s;

.intelligent-quotation-container {
  height: 100%;
  background: linear-gradient(135deg, #e9f0f9, #f5faff);
  position: relative;
  overflow: hidden;
  color: #333333;
  display: flex;
  flex-direction: column;

  .ant-spin-container {
    height: 100%;
  }

  .ant-spin {
    &.ant-spin-spinning {
      .ant-spin-dot {
        i {
          background-color: @primary-color;
        }
      }
    }
  }

  .ant-spin-text {
    color: @primary-color;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  }

  .ai-quotation-content {
    position: relative;
    z-index: 1;
    padding: 20px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0px !important;
    flex: 1;
  }

  .ai-interface-container {
    width: 80%;
    max-width: 1800px;
    height: calc(100vh - 120px);
    display: flex;
    gap: 20px;
    position: relative;
  }

  .ai-chat-container {
    flex: 1;
    min-width: 500px;
    height: 100%;
    background: rgba(255, 255, 255, 0);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid rgba(232, 232, 232, 0.8);
    position: relative;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .canvas-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    opacity: 1;
    border-radius: 8px;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.05);

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(
        circle at center,
        rgba(23, 151, 225, 0.05) 0%,
        transparent 70%
      );
      animation: pulse 8s ease-in-out infinite alternate;
      pointer-events: none;
    }
  }

  .ai-quotation-list-container {
    width: 450px;
    height: 100%;
    background: rgba(255, 255, 255, 0);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid rgba(232, 232, 232, 0.8);
    position: relative;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    .quotation-list-canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 0;
      opacity: 1;
      border-radius: 8px;
      overflow: hidden;
      background-color: rgba(0, 0, 0, 0.05);

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(
          circle at center,
          rgba(118, 199, 242, 0.05) 0%,
          transparent 70%
        );
        animation: pulse 8s ease-in-out infinite alternate;
        pointer-events: none;
      }
    }
  }

  .panel-header {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
    position: relative;
    overflow: hidden;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      position: relative;
      z-index: 1;
    }

    .panel-title {
      display: flex;
      align-items: center;
      position: relative;
      z-index: 1;

      h4 {
        margin: 0 0 0 12px;
        color: #333333;
        font-weight: 500;
        letter-spacing: 0.5px;
      }

      .panel-icon {
        font-size: 20px;
        color: #2188e8;
        background: rgba(82, 202, 254, 0.1);
        padding: 8px;
        border-radius: 8px;
        position: relative;
        overflow: hidden;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .new-conversation-button {
        height: 32px;
        border-radius: 6px;
        border: 1px solid rgba(82, 202, 254, 0.3);
        background: rgba(255, 255, 255, 0.8);
        color: @primary-color;
        font-size: 12px;
        font-weight: 500;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(82, 202, 254, 0.1);

        &:hover {
          background: rgba(82, 202, 254, 0.1);
          border-color: @primary-color;
          color: @primary-color;
          box-shadow: 0 4px 12px rgba(82, 202, 254, 0.2);
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          transform: none;
        }

        .anticon {
          font-size: 12px;
        }
      }
    }

    .panel-subtitle {
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
      position: relative;
      z-index: 1;
    }
  }

  .ai-chat-header {
    .panel-header();
    background: rgba(250, 250, 250, 0);
    border-bottom: 1px solid rgba(203, 203, 203, 0.8);
    position: relative;
    z-index: 1;
  }

  .ai-chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: transparent;
    position: relative;
    z-index: 1;

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.03);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(82, 202, 254, 0.2); // 冰蓝色滚动条
      border-radius: 3px;

      &:hover {
        background: rgba(33, 136, 232, 0.3); // 能量蓝悬停效果
      }
    }

    // 空状态
    .visual-response-empty {
      height: 30%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 0;

      .empty-icon {
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: @primary-gradient;
        border-radius: 50%;
        margin-bottom: 10px;
        box-shadow: @neon-glow;

        .anticon {
          color: white;
          font-size: 32px;
        }
      }

      h4 {
        margin-bottom: 10px;
      }

      .ant-typography {
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }

  // 报价列表头部
  .quotation-list-header {
    .panel-header();
    background: rgba(250, 250, 250, 0); // 半透明背景
    border-bottom: 1px solid rgba(203, 203, 203, 0.8);
    position: relative;
    z-index: 1; // 确保在3D背景之上

    .panel-title {
      h4 {
        color: @text-color;
      }

      .panel-icon {
        color: @secondary-color; // 冰蓝色
        background: rgba(118, 199, 242, 0.1); // 冰蓝透明背景
        box-shadow: 0 0 15px rgba(118, 199, 242, 0.2);

        &::after {
          background: radial-gradient(
            circle,
            rgba(118, 199, 242, 0.2) 0%,
            // 冰蓝色
            transparent 70%
          );
        }
      }
    }
  }

  // 报价列表内容
  .quotation-list-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: transparent; // 透明背景，显示3D效果
    position: relative;
    z-index: 1; // 确保在3D背景之上

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.03);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(118, 199, 242, 0.2); // 冰蓝色滚动条
      border-radius: 3px;

      &:hover {
        background: rgba(33, 136, 232, 0.3); // 能量蓝悬停效果
      }
    }

    // 空状态容器
    .empty-container {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px 0;

      .custom-empty {
        background: rgba(255, 255, 255, 0.5);
        padding: 30px;
        border-radius: 16px;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        box-shadow: 0 10px 20px rgba(118, 199, 242, 0.1);
        border: 1px solid rgba(118, 199, 242, 0.2);
        transform: translateZ(15px);

        .ant-empty-image {
          opacity: 0.7;
          filter: drop-shadow(0 4px 6px rgba(118, 199, 242, 0.2));
        }

        .empty-text {
          color: rgba(0, 0, 0, 0.6);
          font-size: 14px;
          text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  // 新对话轮次分隔线
  .conversation-divider {
    margin: 30px 0;
    position: relative;
    z-index: 1;

    .ant-divider {
      border-color: rgba(82, 202, 254, 0.3);
      margin: 20px 0;
    }

    .divider-content {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 20px;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      box-shadow: 0 4px 12px rgba(82, 202, 254, 0.15);
      border: 1px solid rgba(82, 202, 254, 0.2);

      .divider-icon {
        color: @primary-color;
        font-size: 14px;
        animation: rotate 2s linear infinite;
      }

      .ant-typography {
        margin: 0;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.6);
      }
    }
  }

  // 消息容器 - 3D效果
  .message-container {
    display: flex;
    margin-bottom: 32px; // 增加间距以容纳3D效果
    position: relative;
    z-index: 1;
    perspective: 1000px; // 添加透视效果，创建3D空间

    .message-bubble {
      max-width: 80%;
      padding: 12px 16px;
      border-radius: 12px;
      position: relative;
      animation: fadeIn 0.3s ease;

      &.ai {
        align-self: flex-start;
        background: @holographic-gradient;
        border: @glass-border;
        color: @dark-color;
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      }

      &.user {
        align-self: flex-end;
        background: @primary-gradient;
        color: white;
        box-shadow: 0 2px 10px rgba(33, 136, 232, 0.2);
      }

      .message-time {
        font-size: 10px;
        opacity: 0.7;
        margin-top: 5px;
        text-align: right;
      }
    }

    // 加载动画
    .typing-indicator {
      display: flex;
      max-width: 80%;
      align-items: center;
      gap: 5px;
      padding: 10px;
      margin-bottom: 20px;
      background: rgba(255, 255, 255, 0.5);

      .dot {
        width: 8px;
        height: 8px;
        background: @primary-color;
        border-radius: 50%;
        animation: pulse 1.5s infinite;

        &:nth-child(2) {
          animation-delay: 0.2s;
        }

        &:nth-child(3) {
          animation-delay: 0.4s;
        }
      }
    }

    &.ai-message {
      .ai-avatar {
        background: linear-gradient(135deg, #52cafe, #2188e8); // 渐变背景
        color: white;
        position: relative;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(82, 202, 254, 0.2); // 阴影增强3D效果
        transform: translateZ(5px); // 3D位置

        // 内部光效
        &::after {
          content: "";
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: radial-gradient(
            circle,
            rgba(255, 255, 255, 0.3),
            transparent 70%
          );
          opacity: 0.7;
          animation: rotateBackground 10s linear infinite;
        }
      }
    }

    &.user-message {
      flex-direction: row-reverse;

      .message-content {
        align-items: flex-end;
      }

      // 消息气泡
      .message-bubble {
        max-width: 80%;
        padding: 12px 16px;
        border-radius: 12px;
        position: relative;
        animation: fadeIn 0.3s ease;

        &.ai {
          align-self: flex-start;
          background: @holographic-gradient;
          border: @glass-border;
          color: @dark-color;
          backdrop-filter: blur(5px);
          -webkit-backdrop-filter: blur(5px);
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        &.user {
          align-self: flex-end;
          background: @primary-gradient;
          color: white;
          box-shadow: 0 2px 10px rgba(33, 136, 232, 0.2);
        }

        .message-time {
          font-size: 10px;
          opacity: 0.7;
          margin-top: 5px;
          text-align: right;
        }
      }

      // 加载动画
      .typing-indicator {
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 10px;

        .dot {
          width: 8px;
          height: 8px;
          background: @primary-color;
          border-radius: 50%;
          animation: pulse 1.5s infinite;

          &:nth-child(2) {
            animation-delay: 0.2s;
          }

          &:nth-child(3) {
            animation-delay: 0.4s;
          }
        }
      }

      .user-avatar {
        background: linear-gradient(135deg, #40a9ff, #2188e8); // 渐变背景
        color: white;
        position: relative;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(33, 136, 232, 0.2); // 阴影增强3D效果
        transform: translateZ(5px); // 3D位置

        // 内部光效
        &::after {
          content: "";
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: radial-gradient(
            circle,
            rgba(255, 255, 255, 0.3),
            transparent 70%
          );
          opacity: 0.7;
          animation: rotateBackground 10s linear infinite;
        }
      }
    }
  }

  // 消息头像 - 3D效果
  .message-avatar {
    margin: 0 16px; // 增加间距
    transform-style: preserve-3d; // 保持3D效果
    perspective: 800px; // 添加透视效果

    .ant-avatar {
      width: 44px; // 稍微增大尺寸
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      transition: all 0.3s ease;
      position: relative;
      box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.1),
        // 底部阴影增强3D感
        0 4px 8px rgba(0, 0, 0, 0.05); // 常规阴影
      transform: translateZ(15px); // 3D位置
      border: 2px solid rgba(255, 255, 255, 0.8); // 白色边框

      // 悬停效果
      &:hover {
        transform: translateZ(20px) scale(1.05); // 悬停时轻微放大
      }
    }
  }

  // 消息内容 - 3D效果
  .message-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: calc(100% - 100px); // 调整宽度以适应3D效果
    transform-style: preserve-3d; // 保持3D效果

    // AI报价列表样式 - 与供应价格管理页面完全一致
    .ai-quotation-list {
      .price-card {
        position: relative;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 20px;
        transition: all 0.3s ease;
        cursor: pointer;
        border: 1px solid rgba(0, 120, 255, 0.1);
        box-shadow: @card-shadow;
        overflow: visible;
        z-index: 1;
        margin-bottom: 16px;

        // 科幻风格边框发光效果
        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border-radius: 12px;
          padding: 1px;
          background: linear-gradient(
            135deg,
            rgba(0, 120, 255, 0.15),
            rgba(0, 60, 255, 0.08),
            rgba(0, 120, 255, 0.15)
          );
          mask:
            linear-gradient(#fff 0 0) content-box,
            linear-gradient(#fff 0 0);
          -webkit-mask:
            linear-gradient(#fff 0 0) content-box,
            linear-gradient(#fff 0 0);
          -webkit-mask-composite: xor;
          mask-composite: exclude;
          pointer-events: none;
        }

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 25px rgba(0, 120, 255, 0.15);
          border-color: rgba(0, 120, 255, 0.25);
          z-index: 10;

          &::before {
            background: linear-gradient(
              135deg,
              rgba(0, 120, 255, 0.25),
              rgba(0, 60, 255, 0.12),
              rgba(0, 120, 255, 0.25)
            );
          }
        }

        // 卡片顶部标签
        .price-card-tags {
          position: absolute;
          top: 16px;
          left: 16px;
          display: flex;
          gap: 4px;
          flex-wrap: wrap;
          justify-content: flex-start;
          z-index: 5;

          .feature-tag {
            margin-right: 0;
            display: flex;
            align-items: center;
            gap: 2px;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            line-height: 1.2;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

            .anticon {
              font-size: 10px;
            }
          }
        }

        // 卡片主体 - 左右布局
        .price-card-main {
          display: flex;
          align-items: stretch;
          gap: 20px;
          min-height: 80px;

          // 左侧：主要信息
          .price-card-left {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 12px;

            // 第一行：航司和路径
            .card-row-primary {
              display: flex;
              align-items: center;
              gap: 20px;
              margin-top: 20px; // 让航司与路径稍微靠下，与tag和下方信息垂直居中

              // 航司部分
              .airline-section {
                flex-shrink: 0;
                width: 80px;

                .icon-plane {
                  font-size: 18px;
                  margin-right: 6px;
                  color: #2563eb;
                }

                .icon-label {
                  font-size: 14px;
                  color: #666;
                  font-weight: bold;
                }
              }

              // 路径信息
              .route-section {
                flex: 1;
                min-width: 250px; // 增加最小宽度，给目的港更多空间
                max-width: 400px; // 设置最大宽度，避免过度拉伸

                .route-display {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  flex-wrap: wrap;

                  .port-code {
                    font-weight: 500;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 13px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;

                    &.origin-port {
                      color: #1890ff;
                      background-color: rgba(24, 144, 255, 0.1);
                      max-width: 120px; // 起始港保持适中宽度
                    }

                    &.destination-port {
                      color: #fa8c16;
                      background-color: rgba(250, 140, 22, 0.1);
                      max-width: 200px; // 目的港设置最大宽度，内容自适应
                    }
                  }

                  .route-arrow-container {
                    display: flex;
                    align-items: center;
                    gap: 4px;

                    .route-arrow {
                      color: #8c8c8c;
                      font-size: 12px;
                      flex-shrink: 0;

                      &.direct {
                        color: #52c41a;
                      }

                      &.transfer {
                        color: #722ed1;
                      }
                    }

                    .transfer-route {
                      display: flex;
                      align-items: center;
                      gap: 4px;

                      .transfer-port {
                        color: #722ed1;
                        background-color: rgba(114, 46, 209, 0.1);
                        padding: 2px 6px;
                        border-radius: 4px;
                        font-size: 12px;
                        max-width: 120px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                      }
                    }
                  }
                }
              }

              // 单价信息
              .price-section {
                flex-shrink: 0;
                text-align: center;
                width: 150px; // 进一步加大展示宽度
                margin-right: 40px; // 增加右边距，让它更往左边展示
                margin-left: 10px; // 添加左边距，适当左移

                .price-label {
                  font-size: 12px;
                  color: #666;
                  margin-bottom: 2px;
                }

                .price-display {
                  .price-amount {
                    .price-value {
                      color: #1890ff;
                      .price-currency {
                        font-size: 12px;
                        margin-right: 1px;
                      }

                      .price-amount {
                        font-weight: 600;
                      }
                    }
                  }

                  .price-unit {
                    font-size: 12px;
                    color: #8c8c8c;
                    margin-left: 2px;
                  }
                }
              }
            }

            // 第二行：补充信息
            .card-row-secondary {
              display: flex;
              align-items: center;
              gap: 16px;
              padding: 10px 12px;
              background-color: rgba(240, 248, 255, 0.3);
              border-radius: 6px;
              border: 1px solid rgba(0, 120, 255, 0.05);
              min-height: 44px;

              .info-item {
                flex: 1;
                min-width: 0;

                .info-label {
                  font-size: 11px;
                  color: #666;
                  margin-bottom: 3px;
                  display: flex;
                  align-items: center;
                  font-weight: 500;
                  line-height: 1.4;

                  .info-icon {
                    margin-right: 3px;
                    font-size: 11px;
                  }
                }

                .info-value {
                  font-size: 13px;
                  color: #333;
                  font-weight: 600;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  line-height: 1.5;
                }

                &:last-child .info-value {
                  .flight-date-tag {
                    display: inline-block;
                    background: linear-gradient(
                      135deg,
                      #667eea 0%,
                      #764ba2 100%
                    );
                    color: white;
                    padding: 2px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                    font-weight: 600;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                  }
                }
              }
            }
          }

          // 右侧：总价和操作按钮
          .price-card-right {
            flex-shrink: 0;
            width: 180px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-left: 1px solid rgba(0, 120, 255, 0.1);
            padding-left: 20px;

            .total-price-section {
              text-align: center;
              width: 100%;

              .total-price {
                margin-bottom: 12px;

                .ant-statistic {
                  .ant-statistic-title {
                    font-size: 12px;
                    color: #8c8c8c;
                    margin-bottom: 4px;
                  }

                  .ant-statistic-content {
                    .ant-statistic-content-value {
                      color: #e53e3e !important;
                      font-weight: 800 !important;
                      font-size: 22px !important;
                    }

                    .ant-statistic-content-prefix {
                      color: #e53e3e !important;
                      font-weight: 700 !important;
                      font-size: 16px !important;
                    }
                  }
                }

                .weight-note {
                  font-size: 11px;
                  color: #666;
                  margin-top: 4px;
                  font-style: italic;
                }
              }

              .action-buttons {
                .generate-button {
                  height: 36px;
                  border-radius: 6px;
                  font-weight: 600;
                  background: #1890ff;
                  border: none;
                  color: white;
                  font-size: 14px;
                  transition: all 0.3s;
                  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);

                  &:hover {
                    background: #40a9ff;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
                  }
                }
              }
            }
          }
        }

        // 折叠内容区域
        .expanded-content {
          margin-top: 16px;
          padding: 16px;
          background-color: rgba(240, 248, 255, 0.3);
          border-radius: 8px;
          border: 1px solid rgba(0, 120, 255, 0.1);

          .card-divider {
            margin: 0 0 16px 0;
            border-color: rgba(0, 120, 255, 0.1);
          }

          .price-detail-grid {
            margin-bottom: 16px;

            .detail-item {
              background-color: rgba(255, 255, 255, 0.8);
              border-radius: 6px;
              padding: 8px 12px;
              height: 100%;
              border: 1px solid rgba(0, 120, 255, 0.05);
              transition: all 0.2s ease;

              &:hover {
                background-color: rgba(240, 248, 255, 0.8);
                border-color: rgba(0, 120, 255, 0.1);
              }

              .detail-label {
                font-size: 11px;
                color: #8c8c8c;
                margin-bottom: 4px;
                display: flex;
                align-items: center;
                gap: 4px;

                .anticon {
                  color: #1890ff;
                  font-size: 12px;
                }
              }

              .detail-value {
                font-size: 13px;
                color: #1f2937;
                font-weight: 500;
                line-height: 1.4;

                .ant-tag {
                  margin-right: 0;
                  padding: 0 6px;
                  line-height: 18px;
                  height: 20px;
                  font-size: 11px;
                }

                &.density-value {
                  line-height: 1.6;
                }
              }

              .size-limits {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                justify-content: space-between;

                .size-item {
                  display: flex;
                  align-items: center;
                  font-size: 12px;
                  flex: 1;
                  min-width: 0;

                  .size-label {
                    color: #8c8c8c;
                    font-weight: 500;
                    margin-right: 2px;
                  }

                  .size-value {
                    color: #1f2937;
                    font-weight: 500;
                    white-space: nowrap;
                  }
                }
              }
            }
          }

          .price-tiers-section {
            margin-bottom: 16px;

            .section-subtitle {
              font-size: 13px;
              font-weight: 500;
              color: #1f2937;
              margin-bottom: 12px;
              display: flex;
              align-items: center;

              &:before {
                content: "";
                display: inline-block;
                width: 3px;
                height: 12px;
                background-color: #1890ff;
                margin-right: 6px;
                border-radius: 2px;
              }
            }

            .price-tiers-wrapper {
              display: flex;
              flex-direction: column;
              gap: 8px;
              width: 100%;
              padding: 8px 0;

              .price-tiers-row {
                display: flex;
                gap: 8px;
                justify-content: flex-start;
                flex-wrap: wrap;

                &.single-row {
                  margin-bottom: 0;
                }
              }

              .price-tier-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                background-color: rgba(240, 248, 255, 0.5);
                padding: 6px 8px;
                border-radius: 4px;
                border: 1px solid rgba(0, 120, 255, 0.05);
                min-width: 70px;
                flex: 0 0 auto;
                box-sizing: border-box;
                transition: all 0.2s ease;

                &:hover {
                  background-color: rgba(240, 248, 255, 0.8);
                  border-color: rgba(0, 120, 255, 0.1);
                }

                .ant-badge {
                  margin-bottom: 4px;
                  width: 100%;
                  text-align: center;

                  .ant-badge-status-dot {
                    width: 8px;
                    height: 8px;
                  }

                  .ant-badge-status-text {
                    font-size: 11px;
                    font-weight: 500;
                    margin-left: 6px;
                  }
                }

                .price-tier-value {
                  font-size: 12px;
                  font-weight: 600;
                  color: #1f2937;
                  text-align: center;
                  line-height: 1.3;

                  .price-tier-unit {
                    font-size: 10px;
                    color: #8c8c8c;
                    font-weight: normal;
                    margin-left: 2px;
                  }
                }
              }
            }
          }

          .extra-charges-section {
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid rgba(0, 120, 255, 0.1);

            .charges-title {
              font-size: 13px;
              font-weight: 500;
              color: #1f2937;
              margin-bottom: 8px;
              display: flex;
              align-items: center;

              &:before {
                content: "";
                display: inline-block;
                width: 3px;
                height: 12px;
                background-color: #1890ff;
                margin-right: 6px;
                border-radius: 2px;
              }
            }

            .charges-list {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;

              .charge-item {
                display: flex;
                align-items: center;
                gap: 6px;
                background-color: rgba(255, 255, 255, 0.8);
                padding: 4px 8px;
                border-radius: 4px;
                border: 1px solid rgba(0, 120, 255, 0.05);

                .charge-name {
                  font-size: 12px;
                  color: #4b5563;
                }
              }
            }
          }
        }

        // 展开收起图标
        .expand-toggle {
          position: absolute;
          bottom: -12px;
          left: 50%;
          transform: translateX(-50%);
          background: rgba(255, 255, 255, 0.95);
          border: 1px solid rgba(0, 120, 255, 0.2);
          border-radius: 50%;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          z-index: 10;

          .expand-icon {
            font-size: 12px;
            color: #1890ff;
            transition: transform 0.3s ease;
          }

          &:hover {
            background: rgba(24, 144, 255, 0.1);
            border-color: rgba(0, 120, 255, 0.4);
            transform: translateX(-50%) scale(1.1);

            .expand-icon {
              transform: scale(1.2);
            }
          }
        }
      }
    }
  }

  // 输入提示
  // .typing-indicator {
  //   display: flex;
  //   align-items: center;
  //   padding: 14px 18px;
  //   background: rgba(24, 144, 255, 0.05);
  //   border-radius: 16px;
  //   box-shadow:
  //     0 5px 15px rgba(0, 0, 0, 0.05),
  //     0 0 10px rgba(24, 144, 255, 0.05);
  //   backdrop-filter: blur(5px);
  //   position: relative;
  //   overflow: hidden;

  //   // 动态背景效果
  //   &::before {
  //     content: "";
  //     position: absolute;
  //     top: -100%;
  //     left: -100%;
  //     width: 300%;
  //     height: 300%;
  //     background: radial-gradient(
  //       circle,
  //       rgba(24, 144, 255, 0.03) 0%,
  //       transparent 70%
  //     );
  //     opacity: 0.5;
  //     animation: rotateBackground 20s linear infinite;
  //     pointer-events: none;
  //   }

  //   .ant-spin {
  //     margin-right: 12px;
  //     .ant-spin-dot-item {
  //       background-color: @primary-color;
  //     }
  //   }

  //   .typing-text {
  //     color: rgba(51, 51, 51, 0.7);
  //     position: relative;
  //     z-index: 1;
  //   }
  // }

  // 建议按钮区域
  .message-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 12px;

    .suggestion-button {
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(82, 202, 254, 0.2); // 冰蓝色边框
      border-radius: 20px;
      padding: 6px 14px;
      height: auto;
      font-size: 13px;
      color: @text-color;
      transition: all @transition-time ease;
      position: relative;
      overflow: hidden;

      // 按钮内部发光效果
      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 50%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transform: skewX(-25deg);
        transition: all 0.5s ease;
      }

      &:hover {
        background: rgba(82, 202, 254, 0.1); // 冰蓝色背景
        border-color: @primary-color;
        transform: translateY(-3px);
        box-shadow:
          0 5px 15px rgba(0, 0, 0, 0.08),
          0 0 10px rgba(82, 202, 254, 0.1); // 冰蓝色阴影

        &::after {
          left: 150%;
          transition: all 0.5s ease;
        }
      }
    }
  }

  // 信息收集完成后的操作按钮
  .info-collection-actions {
    padding: 16px;
    border-radius: 12px;

    .action-button {
      height: 40px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      min-width: 120px;
      position: relative;
      overflow: hidden;

      // 按钮内部发光效果
      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 50%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.3),
          transparent
        );
        transform: skewX(-25deg);
        transition: all 0.5s ease;
      }

      &:hover::after {
        left: 150%;
        transition: all 0.5s ease;
      }

      &.create-inquiry-button {
        background: @primary-gradient;
        border: none;
        color: white;
        box-shadow: 0 4px 12px rgba(33, 136, 232, 0.3);

        &:hover {
          background: linear-gradient(135deg, #4bb8ff, #1976d2);
          box-shadow: 0 6px 16px rgba(33, 136, 232, 0.4);
          transform: translateY(-2px);
        }

        &:active {
          transform: translateY(0);
        }
      }

      &.query-quotation-button {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(82, 202, 254, 0.4);
        color: @primary-color;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        box-shadow: 0 2px 8px rgba(82, 202, 254, 0.15);

        &:hover {
          background: rgba(82, 202, 254, 0.1);
          border-color: @primary-color;
          color: @primary-color;
          box-shadow: 0 4px 12px rgba(82, 202, 254, 0.25);
          transform: translateY(-2px);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }

  // 紧凑的结构化信息展示
  .compact-structured-info {
    margin-top: 8px;
    margin-bottom: 8px;
    padding: 8px 10px;
    background: rgba(240, 250, 255, 0.3);
    border-radius: 6px;
    border: 1px solid rgba(24, 144, 255, 0.15);

    .info-header {
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      padding-bottom: 4px;
      border-bottom: 1px solid rgba(24, 144, 255, 0.1);
    }

    .info-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .info-item-compact {
        display: flex;
        align-items: center;
        padding: 2px 6px;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 4px;
        border: 1px solid rgba(24, 144, 255, 0.1);
        transition: all 0.2s ease;

        &:hover {
          background: rgba(240, 250, 255, 0.8);
          border-color: rgba(24, 144, 255, 0.3);
        }

        .anticon {
          font-size: 10px;
        }
      }
    }
  }

  // 报价结果区域 - 聊天面板内
  .quotation-results {
    margin-top: 16px;
    width: 100%;

    .ant-list {
      width: 100%;
    }

    // 报价卡片 - 聊天面板内
    .quotation-card {
      border-radius: 8px;
      border: 1px solid #e8e8e8;
      background: transparent;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      overflow: hidden;
      position: relative;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 12px rgba(82, 202, 254, 0.1); // 冰蓝色阴影
        // border-color: #52cafe; // 冰蓝色边框
      }

      .ant-card-body {
        padding: 18px;
        position: relative;
        z-index: 1;
        // background: linear-gradient(
        //   145deg,
        //   rgba(255, 255, 255, 0.7),
        //   rgba(240, 250, 255, 0.6)
        // ); // 更透明的渐变背景
        // backdrop-filter: blur(10px);
        background: linear-gradient(
          145deg,
          rgba(255, 255, 255, 0.7),
          rgba(240, 250, 255, 0.6)
        );
        border-radius: 0 16px 16px 16px;
        border-left: 3px solid #52cafe;
        box-shadow:
          0 10px 20px rgba(82, 202, 254, 0.15),
          0 5px 8px rgba(0, 0, 0, 0.07),
          inset 0 -2px 0 rgba(255, 255, 255, 0.8);
        position: relative;
        overflow: hidden;
        transform: translateZ(15px) rotateX(2deg);
        transform-style: preserve-3d;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px); // 增强玻璃拟态效果
        -webkit-backdrop-filter: blur(10px);
        animation: messagePop 0.5s ease-out forwards;
      }

      // 报价卡片头部
      .quotation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 14px;
        position: relative;

        .quotation-airline {
          display: flex;
          align-items: center;
          gap: 10px;

          .airline-name {
            color: #333333;
            font-weight: 500;
            letter-spacing: 0.5px;
          }
        }

        .recommendation-badge {
          .ant-badge-status-dot {
            background: #1890ff;
          }

          .ant-badge-status-text {
            color: #1890ff;
            font-size: 12px;
            font-weight: 500;
          }
        }

        .quotation-price {
          display: flex;
          align-items: baseline;
          gap: 4px;

          .price-value {
            font-size: 14px;
            color: #2188e8; // 能量蓝
            font-weight: 600;
          }

          .price-currency {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
          }
        }
      }

      // 报价路线
      .quotation-route {
        margin-bottom: 14px;
        position: relative;

        span {
          display: block;

          &:first-child {
            font-weight: 500;
            margin-bottom: 6px;
            color: #333333;
          }

          &:last-child {
            color: rgba(0, 0, 0, 0.45);
          }
        }
      }

      // 报价详情
      .quotation-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;

        .schedule-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;

          .ant-tag {
            margin-right: 0;
            border-radius: 4px;
            background: rgba(82, 202, 254, 0.05); // 冰蓝色背景
            border: 1px solid rgba(82, 202, 254, 0.2); // 冰蓝色边框
            color: #333333;
          }
        }

        .validity {
          color: rgba(0, 0, 0, 0.45);
          font-size: 13px;
        }
      }
    }
  }

  // 报价列表卡片 - 右侧面板
  .quotation-list-card {
    border-radius: 8px;
    margin-bottom: 24px;
    position: relative;
    perspective: 1000px; // 添加透视效果，创建3D空间

    &:hover {
      transform: translateY(-3px);
    }

    .card-content {
      padding: 18px;
      position: relative;
      z-index: 1;
      background: linear-gradient(
        145deg,
        rgba(255, 255, 255, 0.7),
        rgba(240, 250, 255, 0.6)
      );
      border-radius: 16px;
      border-left: 3px solid #76c7f2; // 冰蓝色边框
      box-shadow:
        0 10px 20px rgba(118, 199, 242, 0.15),
        0 5px 8px rgba(0, 0, 0, 0.07),
        inset 0 -2px 0 rgba(255, 255, 255, 0.8);
      position: relative;
      overflow: hidden;
      transform: translateZ(15px) rotateX(2deg);
      transform-style: preserve-3d;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px); // 增强玻璃拟态效果
      -webkit-backdrop-filter: blur(10px);
      animation: messagePop 0.5s ease-out forwards;

      // 3D边缘效果
      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.8),
          transparent
        );
        transform: translateZ(5px); // 3D位置
      }

      // 3D底部效果
      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(
          to bottom,
          transparent,
          rgba(118, 199, 242, 0.05)
        );
        border-radius: 0 0 16px 16px;
        transform: translateZ(-2px); // 3D位置
      }

      // 悬停效果
      &:hover {
        transform: translateZ(15px) rotateX(1deg) scale(1.01); // 悬停时轻微放大和旋转
        box-shadow:
          0 15px 25px rgba(118, 199, 242, 0.15),
          0 5px 10px rgba(0, 0, 0, 0.05),
          inset 0 -2px 0 rgba(255, 255, 255, 0.8);
      }
    }

    // 报价卡片头部
    .quotation-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 14px;
      position: relative;

      .quotation-airline {
        display: flex;
        align-items: center;
        gap: 10px;

        .airline-name {
          color: #333333; /* 修改为深色文字 */
          font-weight: 500;
          letter-spacing: 0.5px;
        }
      }

      .recommendation-badge {
        .ant-badge-status-dot {
          background: #1890ff; /* 修改为蓝色 */
          box-shadow: 0 0 8px rgba(24, 144, 255, 0.5);
          animation: pulseBadge 2s infinite;
        }

        .ant-badge-status-text {
          color: #1890ff; /* 修改为蓝色 */
          font-size: 12px;
          font-weight: 500;
          text-shadow: none; /* 移除文字阴影 */
        }
      }

      .quotation-price {
        display: flex;
        align-items: baseline;
        gap: 4px;

        .price-value {
          font-size: 14px;
          color: #2188e8; /* 修改为能量蓝色 */
          font-weight: 600;
          text-shadow: 0 0 10px rgba(33, 136, 232, 0.5);
        }

        .price-currency {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65); /* 修改为深色文字 */
        }
      }
    }

    // 报价路线
    .quotation-route {
      margin-bottom: 14px;
      position: relative;

      span {
        display: block;

        &:first-child {
          font-weight: 500;
          margin-bottom: 6px;
          color: #333333; /* 修改为深色文字 */
        }

        &:last-child {
          color: rgba(0, 0, 0, 0.65); /* 修改为深色文字 */
        }
      }
    }

    // 报价详情
    .quotation-details {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;

      .schedule-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;

        .ant-tag {
          margin-right: 0;
          border-radius: 4px;
          background: rgba(82, 202, 254, 0.05); /* 修改为冰蓝色背景 */
          border: 1px solid rgba(82, 202, 254, 0.2); /* 修改为冰蓝色边框 */
          color: #333333; /* 修改为深色文字 */
        }
      }

      .validity {
        color: rgba(0, 0, 0, 0.65); /* 修改为深色文字 */
        font-size: 13px;
      }
    }

    // 卡片操作按钮
    .card-actions {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 14px;
      padding-top: 14px;
      border-top: 1px solid rgba(118, 199, 242, 0.1);
      position: relative;
      z-index: 2;
      transform: translateZ(5px); // 3D位置

      .ant-btn {
        border-radius: 8px;
        height: 32px;
        padding: 0 12px;
        font-size: 13px;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;

        &::after {
          content: "";
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: radial-gradient(
            circle,
            rgba(255, 255, 255, 0.3) 0%,
            transparent 70%
          );
          opacity: 0;
          transform: scale(0.5);
          transition: all 0.5s ease;
        }

        &:hover::after {
          opacity: 0.5;
          transform: scale(1);
        }

        &.ant-btn-default {
          background: rgba(255, 255, 255, 0.7);
          backdrop-filter: blur(5px);
          -webkit-backdrop-filter: blur(5px);
          border: 1px solid rgba(118, 199, 242, 0.3);
          color: #333333;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);

          &:hover {
            background: rgba(255, 255, 255, 0.9);
            border-color: #76c7f2;
            color: #2188e8;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(118, 199, 242, 0.2);
          }
        }

        &.ant-btn-primary {
          background: linear-gradient(90deg, #76c7f2, #2188e8);
          border: none;
          box-shadow: 0 4px 10px rgba(118, 199, 242, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(118, 199, 242, 0.4);
          }
        }
      }
    }
  }

  // 聊天输入区域
  .ai-chat-input {
    padding: 16px 24px;
    background: transparent; // 透明背景
    border-top: 1px solid rgba(240, 240, 240, 0.8);
    position: relative;
    overflow: hidden;
    z-index: 1;

    .command-input-container {
      position: relative;
      width: 100%;

      .input-wrapper {
        position: relative;
        width: 100%;

        .textarea-container {
          position: relative;
          width: 100%;
        }

        .command-input {
          border-radius: 24px;
          padding: 12px 50px 12px 16px; // 右侧留出按钮空间
          height: auto;
          background: rgba(255, 255, 255, 0.8);
          border: 1px solid rgba(208, 217, 226, 0.8);
          transition: all 0.3s ease;
          color: #333333;
          font-size: 14px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          backdrop-filter: blur(5px);
          -webkit-backdrop-filter: blur(5px);
          width: 100%;
          min-height: 72px; // 最小3行高度
          line-height: 1.5;

          &::placeholder {
            color: rgba(0, 0, 0, 0.25);
          }

          &:hover,
          &:focus {
            border-color: #52cafe;
            box-shadow: 0 0 0 2px rgba(82, 202, 254, 0.1);
            background: rgba(255, 255, 255, 0.9);
          }

          &:disabled {
            background: rgba(245, 245, 245, 0.8);
            cursor: not-allowed;
          }
        }

        .send-button-overlay {
          position: absolute;
          right: 8px;
          bottom: 8px;
          z-index: 2;

          // 确保按钮始终在文本框的右下角
          .send-button {
            transition: all 0.3s ease;
          }
        }

        .input-icon {
          color: rgba(0, 0, 0, 0.45);
          font-size: 16px;
          cursor: pointer;
          transition: all 0.3s ease;
          margin: 0 8px;

          &:hover {
            color: #52cafe;
          }

          &.info-icon {
            &:hover {
              transform: rotate(15deg);
            }
          }

          &.voice-icon {
            &.recording {
              color: #52cafe;
              animation: pulse 1.5s infinite;
            }

            &:hover {
              transform: scale(1.1);
            }
          }
        }

        .send-button {
          border-radius: 50%;
          width: 36px;
          height: 36px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0;
          background: linear-gradient(90deg, #52cafe, #2188e8);
          border: none;
          box-shadow: 0 4px 12px rgba(82, 202, 254, 0.4);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: "";
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(
              circle,
              rgba(255, 255, 255, 0.3) 0%,
              transparent 70%
            );
            opacity: 0.5;
            animation: rotateBackground 10s linear infinite;
          }

          &:hover {
            transform: scale(1.15);
            box-shadow:
              0 6px 15px rgba(82, 202, 254, 0.5),
              0 0 20px rgba(33, 136, 232, 0.3);
          }

          &:disabled {
            background: #d9d9d9;
            box-shadow: none;
            opacity: 0.7;

            &::before {
              display: none;
            }
          }

          .anticon {
            font-size: 16px;
            position: relative;
            z-index: 1;
          }
        }
      }

      // 建议模板容器
      .suggestions-container {
        position: absolute;
        top: calc(100% + 10px);
        left: 0;
        width: 100%;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        z-index: 10;
        overflow: hidden;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(82, 202, 254, 0.2);
        animation: fadeIn 0.3s ease;

        .suggestions-header {
          padding: 12px 16px;
          background: rgba(82, 202, 254, 0.1);
          border-bottom: 1px solid rgba(82, 202, 254, 0.2);
          display: flex;
          align-items: center;
          gap: 8px;

          .suggestions-icon {
            color: #2188e8;
            font-size: 16px;
          }

          span {
            color: #333333;
            font-weight: 500;
          }
        }

        .suggestions-list {
          max-height: 300px;
          overflow-y: auto;
          padding: 8px 0;

          .suggestion-item {
            padding: 10px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;

            &:hover {
              background: rgba(82, 202, 254, 0.1);

              &::before {
                content: "";
                position: absolute;
                left: 0;
                top: 0;
                height: 100%;
                width: 3px;
                background: #52cafe;
              }
            }

            span {
              color: #333333;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .ant-input-suffix {
    .send-button {
      border-radius: 50%;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      background: linear-gradient(90deg, #52cafe, #2188e8); // 能量蓝光渐变
      border: none;
      box-shadow: 0 4px 12px rgba(82, 202, 254, 0.4);
      transition: all @transition-time ease;
      position: relative;
      overflow: hidden;

      // 按钮内部发光效果
      &::after {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(
          circle,
          rgba(255, 255, 255, 0.3) 0%,
          transparent 70%
        );
        opacity: 0.5;
        animation: rotateBackground 10s linear infinite;
      }

      &:hover {
        transform: scale(1.15);
        box-shadow:
          0 6px 15px rgba(82, 202, 254, 0.5),
          // 冰蓝色阴影
          0 0 20px rgba(33, 136, 232, 0.3); // 能量蓝阴影
      }

      .anticon {
        font-size: 16px;
        position: relative;
        z-index: 1;
      }
    }
  }

  // 报价列表底部操作区
  .quotation-list-actions {
    padding: 16px 24px;
    background: #ffffff;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    position: relative;
    overflow: hidden;

    .action-buttons {
      display: flex;
      gap: 12px;
      position: relative;
      z-index: 1;

      .ant-btn {
        border-radius: 4px;
        height: 32px;

        &.ant-btn-default {
          background: #ffffff;
          border: 1px solid #d9d9d9;
          color: #333333;

          &:hover {
            background: #f5f5f5;
            border-color: #52cafe; // 冰蓝色边框
            color: #2188e8; // 能量蓝文字
          }
        }

        &.ant-btn-primary {
          background: linear-gradient(90deg, #52cafe, #2188e8); // 能量蓝光渐变
          border: none;

          &:hover {
            background: linear-gradient(
              90deg,
              lighten(#52cafe, 5%),
              lighten(#2188e8, 5%)
            ); // 亮一点的能量蓝光渐变
          }
        }
      }
    }
  }

  // 报价单详情模态框
  .price-detail-modal {
    .ant-modal-content {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(0, 0, 0, 0.06);
    }

    .ant-modal-header {
      padding: 16px 24px;
      background: linear-gradient(to right, #f0f7ff, #f9fcff);
      border-bottom: 1px solid #e6f0fa;
    }

    .price-detail-title {
      display: flex;
      align-items: center;
      gap: 10px;
      position: relative;
      z-index: 1;

      .title-icon {
        color: #1890ff;
        font-size: 18px;
      }

      .price-id-tag {
        margin-left: 12px;
      }
    }

    .ant-modal-body {
      padding: 20px;
      max-height: 70vh;
      overflow-y: auto;
      background-color: #f9fafc;

      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }
    }

    .price-detail-content {
      .detail-card {
        margin-bottom: 16px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        background-color: #fff;
        border: 1px solid #e6e8eb;
        overflow: hidden;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .ant-card-body {
          padding: 16px;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;

        .anticon {
          margin-right: 8px;
          color: #1890ff;
        }
      }

      .info-item {
        margin-bottom: 12px;

        .ant-typography {
          display: block;
          margin-bottom: 4px;
        }

        .info-value {
          font-size: 14px;
          color: #1f2937;
          font-weight: 500;

          &.highlight {
            font-size: 18px;
            color: #1890ff;
            font-weight: 600;

            .price-currency {
              font-size: 14px;
              margin-right: 2px;
            }

            .price-amount {
              font-weight: 700;
            }
          }
        }
      }

      .schedule-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
      }

      .terms-note {
        margin-top: 8px;
        font-style: italic;
        color: rgba(0, 0, 0, 0.65);
        line-height: 1.5;
      }
    }

    .ant-modal-footer {
      border-top: 1px solid #f0f0f0;
      padding: 16px 24px;
      background: #ffffff;
      position: relative;
      overflow: hidden;

      .ant-btn {
        border-radius: 4px;
        height: 32px;
        position: relative;
        z-index: 1;

        &.ant-btn-default {
          background: #ffffff;
          border: 1px solid #d9d9d9;
          color: #333333;

          &:hover {
            background: #f5f5f5;
            border-color: #1890ff;
            color: #1890ff;
          }
        }

        &.ant-btn-primary {
          background: #1890ff;
          border: none;

          &:hover {
            background: #40a9ff;
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes pulseBadge {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

@keyframes messagePop {
  0% {
    opacity: 0;
    transform: translateZ(-20px) scale(0.95);
  }
  50% {
    opacity: 1;
    transform: translateZ(15px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateZ(10px) scale(1);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.3;
    transform: scale(0.98);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.02);
  }
  100% {
    opacity: 0.3;
    transform: scale(0.98);
  }
}

@keyframes rotateBackground {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
