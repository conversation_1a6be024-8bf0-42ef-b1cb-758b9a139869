import dayjs, { Dayjs } from "dayjs";
import pinyin from "pinyin";
// 时间格式化工具函数
export const formatDateToTimestamp = (
  date: string | number | Date | Dayjs | null | undefined
): number | undefined => {
  if (!date) return undefined;

  // 如果已经是时间戳，直接返回
  if (typeof date === "number") return date;

  // 如果是dayjs对象，转换为时间戳
  if (dayjs.isDayjs(date)) {
    return date.valueOf();
  }

  // 如果是字符串或Date对象，先转换为dayjs再转为时间戳
  return dayjs(date).valueOf();
};

// 日期范围格式化工具函数 - 专门用于RangePicker组件
export const formatDateRangeToTimestamp = (
  startDate: Dayjs | null | undefined,
  endDate: Dayjs | null | undefined
): { startTimestamp?: number; endTimestamp?: number } => {
  const result: { startTimestamp?: number; endTimestamp?: number } = {};

  // 处理起始日期 - 设置为当天的00:00:00
  if (startDate) {
    result.startTimestamp = startDate.startOf("day").valueOf();
  }

  // 处理结束日期 - 设置为当天的23:59:59
  if (endDate) {
    result.endTimestamp = endDate.endOf("day").valueOf();
  }

  return result;
};

// 当前时间戳
export const getCurrentTimestamp = (): number => {
  return dayjs().valueOf();
};

// 单位换算函数：将各种长度单位转换为厘米(cm)
export const convertToCentimeters = (value: number, unit: string): number => {
  if (!value || value <= 0) return 0;

  const normalizedUnit = unit.toLowerCase().trim();

  // 单位换算表（转换为厘米）
  const conversionTable: { [key: string]: number } = {
    // 厘米
    cm: 1,
    // 毫米
    mm: 0.1,
    // 米
    m: 100,
    // 英寸
    in: 2.54,
    // 英尺
    ft: 30.48,
    // 码
    yd: 91.44,
  };

  const conversionFactor = conversionTable[normalizedUnit];

  if (conversionFactor !== undefined) {
    return Math.round(value * conversionFactor * 100) / 100; // 保留两位小数
  }

  // 如果单位不在转换表中，默认当作厘米处理
  console.warn(`未知的长度单位: ${unit}，默认当作厘米处理`);
  return value;
};

// 转换发货地为英文
export const convertShippedPlaceToEnglish = (shippedPlace: string): string => {
  if (!shippedPlace) return "";

  const cleanPlace = shippedPlace.trim();
  // 检查是否包含中文字符
  const hasChinese = /[\u4e00-\u9fa5]/.test(cleanPlace);

  if (!hasChinese) {
    return cleanPlace.toLowerCase();
  }

  // 如果包含中文，使用 pinyin 库转换为拼音
  try {
    const pinyinResult = pinyin(cleanPlace, {
      style: pinyin.STYLE_NORMAL, // 不带声调的拼音
      heteronym: false, // 不返回多音字的所有读音
      segment: true, // 启用分词
    });

    // 将二维数组扁平化并连接成字符串
    const pinyinString = pinyinResult.flat().join("").toLowerCase();

    // 移除特殊字符，只保留字母和数字
    return pinyinString.replace(/[^a-z0-9]/g, "");
  } catch (error) {
    console.warn(error);
    // 如果转换失败，返回原文的小写形式
    return cleanPlace.toLowerCase().replace(/[^a-z0-9]/g, "");
  }
};

// 将时间戳数组转换为特定格式的日期字符串
export const formatDepartureDates = (optionalDates: string[] | undefined) => {
  if (!optionalDates || optionalDates.length === 0) {
    return "";
  }

  // 将时间戳转换为dayjs对象并按月份分组
  const dateGroups: { [key: string]: dayjs.Dayjs[] } = {};

  optionalDates.forEach((timestamp) => {
    const date = dayjs(parseInt(timestamp));
    // 强制使用英文月份格式，避免中文显示
    const monthKey = date.locale("en").format("MMMM").toUpperCase();

    if (!dateGroups[monthKey]) {
      dateGroups[monthKey] = [];
    }
    dateGroups[monthKey].push(date);
  });

  // 格式化输出
  const formattedParts: string[] = [];

  // 按月份顺序处理（确保时间顺序）
  const sortedMonths = Object.keys(dateGroups).sort((a, b) => {
    const dateA = dateGroups[a][0];
    const dateB = dateGroups[b][0];
    return dateA.valueOf() - dateB.valueOf();
  });

  sortedMonths.forEach((month) => {
    const dates = dateGroups[month].sort((a, b) => a.valueOf() - b.valueOf());
    const dayNumbers = dates.map((date) => date.format("D"));

    // 根据日期数量和连续性决定格式
    if (dayNumbers.length === 1) {
      // 单个日期：如 "2JULY"
      formattedParts.push(`${dayNumbers[0]}${month}`);
    } else {
      // 多个日期：如 "28/29JUNE"
      formattedParts.push(`${dayNumbers.join("/")}${month}`);
    }
  });

  return formattedParts.join("；");
};

/**
 * 安全的复制到剪贴板函数
 * 兼容不同浏览器环境和HTTPS/HTTP协议
 * @param text 要复制的文本内容
 * @returns Promise<boolean> 复制是否成功
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  if (!text) {
    return false;
  }

  try {
    // 优先使用现代 Clipboard API（需要 HTTPS 或 localhost）
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(text);
      return true;
    }

    // 降级方案：使用传统的 document.execCommand
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.position = "fixed";
    textArea.style.left = "-999999px";
    textArea.style.top = "-999999px";
    textArea.style.opacity = "0";
    textArea.style.pointerEvents = "none";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      const successful = document.execCommand("copy");
      document.body.removeChild(textArea);
      return successful;
    } catch (err) {
      document.body.removeChild(textArea);
      return false;
    }
  } catch (error) {
    console.error("复制到剪贴板失败:", error);
    return false;
  }
};
