import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  Tabs,
  Button,
  Table,
  Space,
  Form,
  Input,
  Popconfirm,
  message,
  Tree,
  Row,
  Col,
  Badge,
  Typography,
  Divider,
  Tag,
  Switch,
  Tooltip,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  TeamOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  PartitionOutlined,
  MailOutlined,
  PhoneOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { Department, User, userIdentityMap } from "../types";
import type { ColumnsType } from "antd/es/table";
import DepartmentFormModal from "./DepartmentFormModal";
import {
  getAllDepartment,
  addDepartment,
  updateDepartment,
  delDepartment,
} from "../services";
import TableCom from "@/components/TableCom";

interface DepartmentItem {
  departmentid: number;
  departname: string;
}

const DepartmentManagement: React.FC = () => {
  const { t } = useTranslation();
  const [departmentForm] = Form.useForm();
  const [editingDepartment, setEditingDepartment] = useState<Department | null>(
    null
  );
  const [departmentModalVisible, setDepartmentModalVisible] = useState(false);
  const [departmentsList, setDepartmentsList] = useState([]);

  const queryDepartmentList = async () => {
    // setDepartmentsList(mockDepartments);
    const res = await getAllDepartment();
    const { data } = res;
    if (data.resultCode === 200) {
      setDepartmentsList(data.data);
    } else {
      message.error(data.message);
    }
  };

  useEffect(() => {
    queryDepartmentList();
  }, []);

  // 部门表格列定义
  const departmentColumns: ColumnsType<Department> = [
    {
      title: t(
        "organizationManagement.departmentManagement.columns.departmentName"
      ),
      dataIndex: "departname",
      key: "departname",
    },
    {
      title: t("organizationManagement.departmentManagement.columns.actions"),
      key: "action",
      width: 200,
      render: (_, record: Department) => (
        <Space size="middle" className="operation-buttons">
          <Tooltip
            title={t(
              "organizationManagement.departmentManagement.actions.edit"
            )}
          >
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditDepartment(record)}
              className="edit-button"
              size="small"
            >
              {/* 编辑 */}
            </Button>
          </Tooltip>
          <Tooltip
            title={t(
              "organizationManagement.departmentManagement.actions.delete"
            )}
          >
            <Popconfirm
              title={t(
                "organizationManagement.departmentManagement.deleteConfirm.title"
              )}
              description={t(
                "organizationManagement.departmentManagement.deleteConfirm.description"
              )}
              onConfirm={() => handleDeleteDepartment(record.departmentid)}
              // onCancel={handleCancel}
              okText={t(
                "organizationManagement.departmentManagement.deleteConfirm.okText"
              )}
              cancelText={t(
                "organizationManagement.departmentManagement.deleteConfirm.cancelText"
              )}
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                className="delete-button"
                size="small"
              >
                {/* 删除 */}
              </Button>
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];
  // 处理添加部门
  const handleAddDepartment = () => {
    setEditingDepartment(null);
    departmentForm.resetFields();
    setDepartmentModalVisible(true);
  };

  // 处理编辑部门
  const handleEditDepartment = (department: Department) => {
    setEditingDepartment(department);
    departmentForm.setFieldsValue(department);
    setDepartmentModalVisible(true);
  };

  // 处理删除部门
  const handleDeleteDepartment = async (departmentId: number) => {
    const res = await delDepartment({ departmentid: departmentId });
    const { data } = res;
    if (data.resultCode === 200) {
      queryDepartmentList();
      message.success(
        t("organizationManagement.departmentManagement.messages.deleteSuccess")
      );
    } else {
      message.error(data.message);
    }
  };

  // 处理保存部门
  const handleSaveDepartment = () => {
    departmentForm
      .validateFields()
      .then(async (values) => {
        const res = editingDepartment
          ? await updateDepartment(values)
          : await addDepartment(values);
        const { data } = res;
        if (data.resultCode === 200) {
          queryDepartmentList();
          setDepartmentModalVisible(false);
          message.success(
            editingDepartment
              ? t(
                  "organizationManagement.departmentManagement.messages.updateSuccess"
                )
              : t(
                  "organizationManagement.departmentManagement.messages.addSuccess"
                )
          );
        } else {
          message.error(
            editingDepartment
              ? t(
                  "organizationManagement.departmentManagement.messages.updateFailed"
                )
              : t(
                  "organizationManagement.departmentManagement.messages.addFailed"
                )
          );
        }
      })
      .catch((info) => {
        console.log("验证失败:", info);
      });
  };

  const filterRender = () => {
    return (
      <div className="filter_box">
        <div className="title">
          {t("organizationManagement.departmentManagement.title")}
        </div>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddDepartment}
          >
            {t("organizationManagement.departmentManagement.newDepartment")}
          </Button>
        </Space>
      </div>
    );
  };

  return (
    <div className="department-container">
      <TableCom<DepartmentItem>
        columns={departmentColumns}
        data={departmentsList}
        filterRender={filterRender}
      />
      {/* 部门表单模态框 */}
      <DepartmentFormModal
        visible={departmentModalVisible}
        onCancel={() => setDepartmentModalVisible(false)}
        onOk={handleSaveDepartment}
        editingDepartment={editingDepartment}
        form={departmentForm}
      />
    </div>
  );
};

export default DepartmentManagement;
