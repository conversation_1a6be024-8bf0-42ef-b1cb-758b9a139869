import React, { useEffect, useRef } from "react";
import * as echarts from "echarts";
import { Select, Button, Space, message } from "antd";
import { Canvas } from "@react-three/fiber";
import { OrbitControls } from "@react-three/drei";
import { FuturisticEarth } from "@/components/ThreeScene/FuturisticBackground";

interface DataVisualizationProps {
  type: "flowchart" | "globe" | "comparison";
  data: Record<string, any>;
  onDataChange: (key: string, value: any) => void;
}

const DataVisualization: React.FC<DataVisualizationProps> = ({
  type,
  data,
  onDataChange,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  // 初始化图表
  useEffect(() => {
    if (type === "flowchart" || type === "comparison") {
      if (chartRef.current) {
        chartInstance.current = echarts.init(chartRef.current);
        renderChart();
      }

      // 清理函数
      return () => {
        if (chartInstance.current) {
          chartInstance.current.dispose();
        }
      };
    }
  }, [type, data]);

  // 窗口大小变化时重新调整图表大小
  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  // 渲染不同类型的图表
  const renderChart = () => {
    if (!chartInstance.current) return;

    if (type === "flowchart") {
      // 流程图配置
      const option = {
        title: {
          text: "智能报价流程",
          left: "center",
          textStyle: {
            color: "#333",
          },
        },
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b} : {c}%",
        },
        series: [
          {
            name: "报价流程",
            type: "funnel",
            left: "10%",
            top: 60,
            bottom: 60,
            width: "80%",
            min: 0,
            max: 100,
            minSize: "0%",
            maxSize: "100%",
            sort: "descending",
            gap: 2,
            label: {
              show: true,
              position: "inside",
            },
            labelLine: {
              length: 10,
              lineStyle: {
                width: 1,
                type: "solid",
              },
            },
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 1,
            },
            emphasis: {
              label: {
                fontSize: 20,
              },
            },
            data: [
              { value: 100, name: "需求收集" },
              { value: 80, name: "路线规划" },
              { value: 60, name: "价格计算" },
              { value: 40, name: "方案比较" },
              { value: 20, name: "最终报价" },
            ],
          },
        ],
      };

      chartInstance.current.setOption(option);
    } else if (type === "comparison") {
      // 报价比较图表
      const option = {
        title: {
          text: "报价方案比较",
          left: "center",
          textStyle: {
            color: "#333",
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          data: ["价格 (USD/kg)", "时效 (天)", "服务评分"],
          top: 30,
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: ["南方航空", "东方航空", "国际航空"],
        },
        yAxis: [
          {
            type: "value",
            name: "价格/时效",
            position: "left",
          },
          {
            type: "value",
            name: "评分",
            position: "right",
            min: 0,
            max: 5,
          },
        ],
        series: [
          {
            name: "价格 (USD/kg)",
            type: "bar",
            data: [5.8, 6.2, 5.5],
            itemStyle: {
              color: "#52cafe",
            },
          },
          {
            name: "时效 (天)",
            type: "bar",
            data: [6, 7, 8],
            itemStyle: {
              color: "#1797e1",
            },
          },
          {
            name: "服务评分",
            type: "line",
            yAxisIndex: 1,
            data: [4.5, 4.2, 3.8],
            itemStyle: {
              color: "#ff7a45",
            },
            lineStyle: {
              width: 3,
            },
            symbol: "circle",
            symbolSize: 10,
          },
        ],
      };

      chartInstance.current.setOption(option);
    }
  };

  // 渲染地球选择组件
  const renderGlobeSelection = () => {
    // 预设的起始地和目的地选项
    const originOptions = [
      { label: "上海浦东", value: "上海浦东" },
      { label: "北京首都", value: "北京首都" },
      { label: "广州白云", value: "广州白云" },
      { label: "深圳宝安", value: "深圳宝安" },
    ];

    const destinationOptions = [
      { label: "纽约肯尼迪", value: "纽约肯尼迪" },
      { label: "洛杉矶国际", value: "洛杉矶国际" },
      { label: "伦敦希思罗", value: "伦敦希思罗" },
      { label: "东京成田", value: "东京成田" },
      { label: "新加坡樟宜", value: "新加坡樟宜" },
    ];

    return (
      <div style={{ height: "100%", display: "flex", flexDirection: "column" }}>
        <div style={{ height: "70%", position: "relative" }}>
          <Canvas
            camera={{ position: [0, 0, 5], fov: 45 }}
            style={{ width: "100%", height: "100%" }}
          >
            <ambientLight intensity={0.5} />
            <FuturisticEarth />
            <OrbitControls
              enableZoom={true}
              enablePan={true}
              enableRotate={true}
              autoRotate={true}
              autoRotateSpeed={0.5}
            />
          </Canvas>
        </div>
        <div
          style={{
            padding: "20px",
            display: "flex",
            flexDirection: "column",
            gap: "15px",
          }}
        >
          <Space direction="vertical" style={{ width: "100%" }}>
            <div>起始地:</div>
            <Select
              style={{ width: "100%" }}
              placeholder="请选择起始地"
              options={originOptions}
              value={data.route?.origin}
              onChange={(value) =>
                onDataChange("route", { ...data.route, origin: value })
              }
            />
          </Space>
          <Space direction="vertical" style={{ width: "100%" }}>
            <div>目的地:</div>
            <Select
              style={{ width: "100%" }}
              placeholder="请选择目的地"
              options={destinationOptions}
              value={data.route?.destination}
              onChange={(value) =>
                onDataChange("route", { ...data.route, destination: value })
              }
            />
          </Space>
          <Button
            type="primary"
            disabled={!data.route?.origin || !data.route?.destination}
            onClick={() => {
              // 确认选择
              message.success("路线已确认");
            }}
            style={{ marginTop: "10px" }}
          >
            确认路线
          </Button>
        </div>
      </div>
    );
  };

  // 根据类型渲染不同的可视化组件
  if (type === "globe") {
    return renderGlobeSelection();
  }

  return (
    <div
      ref={chartRef}
      style={{ width: "100%", height: "300px", marginBottom: "20px" }}
    />
  );
};

export default DataVisualization;
