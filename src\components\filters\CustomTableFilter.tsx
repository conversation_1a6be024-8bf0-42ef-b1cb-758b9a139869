import React, { useState, useEffect } from "react";
import { Checkbox, Radio, Button, Space } from "antd";
import type { CheckboxChangeEvent } from "antd/es/checkbox";
import type { RadioChangeEvent } from "antd/es/radio";
import "./CustomTableFilter.less";

interface FilterOption {
  text: string;
  value: any;
}

interface CustomTableFilterProps {
  setSelectedKeys: (selectedKeys: React.Key[]) => void;
  selectedKeys: React.Key[];
  confirm: () => void;
  clearFilters: () => void;
  options: FilterOption[];
  multiple?: boolean; // 是否多选，默认为true
  onFilterChange?: (filteredValue: any) => void; // 筛选变化回调
}

/**
 * 自定义表格筛选面板组件
 * 模拟Antd Table默认筛选面板的外观和行为
 * 支持多选和单选模式
 * 点击重置后直接重置筛选状态，无需再点击确认
 */
const CustomTableFilter: React.FC<CustomTableFilterProps> = ({
  setSelectedKeys,
  selectedKeys,
  confirm,
  clearFilters,
  options,
  multiple = true,
  onFilterChange,
}) => {
  const [checkedValues, setCheckedValues] = useState<React.Key[]>(selectedKeys);

  useEffect(() => {
    setCheckedValues(selectedKeys);
  }, [selectedKeys]);

  // 多选模式的处理
  const handleCheckboxChange = (e: CheckboxChangeEvent) => {
    const { value, checked } = e.target;
    let newValues: React.Key[];

    if (checked) {
      newValues = [...checkedValues, value];
    } else {
      newValues = checkedValues.filter((item) => item !== value);
    }

    setCheckedValues(newValues);
    setSelectedKeys(newValues);
  };

  // 单选模式的处理
  const handleRadioChange = (e: RadioChangeEvent) => {
    const { value } = e.target;
    const newValues = [value];
    setCheckedValues(newValues);
    setSelectedKeys(newValues);
  };

  // 确认筛选
  const handleConfirm = () => {
    if (onFilterChange) {
      onFilterChange(checkedValues);
    }
    confirm();
  };

  // 重置筛选 - 点击后直接重置并确认
  const handleReset = () => {
    setCheckedValues([]);
    setSelectedKeys([]);
    clearFilters();
    if (onFilterChange) {
      onFilterChange([]);
    }
    // 直接确认，不需要再点击确认按钮
    confirm();
  };

  return (
    <div
      className="custom-table-filter"
      style={{
        padding: "8px 12px",
        minWidth: 120,
        backgroundColor: "#fff",
        border: "1px solid #d9d9d9",
        borderRadius: "6px",
        boxShadow:
          "0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)",
      }}
    >
      <div
        className="filter-options-container"
        style={{ maxHeight: "300px", overflowY: "auto" }}
      >
        {multiple ? (
          // 多选模式
          <div>
            {options.map((option) => (
              <div
                key={option.value}
                style={{
                  padding: "4px 0",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Checkbox
                  value={option.value}
                  checked={checkedValues.includes(option.value)}
                  onChange={handleCheckboxChange}
                  style={{ fontSize: "14px" }}
                >
                  {option.text}
                </Checkbox>
              </div>
            ))}
          </div>
        ) : (
          // 单选模式
          <Radio.Group
            value={checkedValues[0]}
            onChange={handleRadioChange}
            style={{ width: "100%" }}
          >
            {options.map((option) => (
              <div
                key={option.value}
                style={{
                  padding: "4px 0",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Radio value={option.value} style={{ fontSize: "14px" }}>
                  {option.text}
                </Radio>
              </div>
            ))}
          </Radio.Group>
        )}
      </div>

      {/* 底部按钮区域 */}
      <div
        style={{
          borderTop: "1px solid #f0f0f0",
          paddingTop: "8px",
          marginTop: "8px",
          display: "flex",
          justifyContent: "space-between",
        }}
      >
        <Button size="small" onClick={handleReset} style={{ fontSize: "12px" }}>
          重置
        </Button>
        <Button
          type="primary"
          size="small"
          onClick={handleConfirm}
          style={{ fontSize: "12px" }}
        >
          确定
        </Button>
      </div>
    </div>
  );
};

export default CustomTableFilter;
