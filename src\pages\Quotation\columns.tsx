import React from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popconfirm, Input } from "antd";
import { TableProps } from "antd";
import {
  EditOutlined,
  DeleteOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  DollarOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { DateRangeFilter, SelectFilter } from "@/components/filters";
import { useTranslation } from "react-i18next";
import { useAppSelector } from "@/store/hooks";

// 公共文本筛选器
const createTextFilter = (placeholder: string) => {
  const { t } = useTranslation();
  const TextFilter = ({
    setSelectedKeys,
    selectedKeys,
    confirm,
    clearFilters,
  }: any) => (
    <div style={{ padding: 8 }}>
      <Input
        placeholder={placeholder}
        value={selectedKeys[0]}
        onChange={(e) =>
          setSelectedKeys(e.target.value ? [e.target.value] : [])
        }
        onPressEnter={() => confirm()}
        style={{ width: 188, marginBottom: 8, display: "block" }}
      />
      <Space>
        <Button
          type="primary"
          onClick={() => confirm()}
          size="small"
          style={{ width: 90 }}
        >
          {t("common.confirm")}
        </Button>
        <Button
          onClick={() => {
            clearFilters && clearFilters();
            confirm();
          }}
          size="small"
          style={{ width: 90 }}
        >
          {t("common.reset")}
        </Button>
      </Space>
    </div>
  );

  TextFilter.displayName = "TextFilter";
  return TextFilter;
};
const getBooleanOptions = (t: any) => [
  { text: t("quotationTable.booleanValues.yes"), value: true },
  { text: t("quotationTable.booleanValues.no"), value: false },
];

interface DataType {
  inquiryid: string;
  freightmethod: string; // 货运方式
  goodstype: string; // 货物类型
  tradeterms: string; // 贸易术语
  companyname: string; // 公司名
  inquirer: string; // 询价人
  inquirytime: string; // 询价时间
  shippedplace: string; // 发货地
  originport: string; // POL: 起始港
  unloadingport: string; // POD: 目的港
  grossweight: number; // 货物毛重
  goodsvolume: number; // 货物体积
  singlemaxweight?: number | null; // 单件货最大重量
  isbrand: boolean; // 是否品牌货
  ensurecabin: boolean; // 保舱
  isvalidity: boolean; // 要求ETD(发货日期)
  shipmentdate?: string; // 发货日期
  packagetype: string; // 包装形式
  cargolength?: number; // 货物长度
  cargowidth?: number; // 货物宽
  cargoheight?: number; // 货物高
  specialcargo?: string; // 特殊货物
  inquirycreatetime: string; // 记录生成时间
  inquirystate: number; // 询价状态
  userid: number; // 用户ID
  departmentid: number; // 所在部门id
  status?: "quoted" | "unquoted" | "deal" | "nodeal"; // 状态：已报价、未报价、已成交、未成交
  [key: string]: any;
}

interface ColumnParams {
  handleEdit: (record: DataType) => void;
  handleDel: (record: DataType) => void;
  handleCancel: () => void;
  handleQueryPrice: (record: DataType) => void;
  handleManualQuote: (record: DataType) => void;
  user: any;
  selectedUserId: number | null;
  portOptions: { label: string; value: string }[];
  shipmentPlaceOptions: { label: string; value: string }[];
  packageTypeOptions: { label: string; value: string }[];
  specialItemOptions: { label: string; value: string }[];
}

export const createColumns = ({
  handleEdit,
  handleDel,
  handleCancel,
  handleQueryPrice,
  handleManualQuote,
  user,
  selectedUserId,
  portOptions,
  shipmentPlaceOptions,
  packageTypeOptions,
  specialItemOptions,
}: ColumnParams): TableProps<DataType>["columns"] => {
  const { t } = useTranslation();
  const booleanOptions = getBooleanOptions(t);
  const { currentLanguage } = useAppSelector((state) => state.language);
  const isEnglish = currentLanguage === "en-US";
  const getColumnWidth = (zhWidth: number, enWidth: number) => {
    return isEnglish ? enWidth : zhWidth;
  };

  return [
    {
      title: t("quotationTable.columns.inquiryTime"),
      dataIndex: "inquirytime",
      key: "inquirytime",
      width: getColumnWidth(130, 160),
      render: (text) => (
        <span>
          {text ? dayjs(text).format("YYYY-MM-DD") : t("quotationTable.noData")}
        </span>
      ),
      filterDropdown: ({ setSelectedKeys, confirm, clearFilters }) => (
        <DateRangeFilter
          setSelectedKeys={setSelectedKeys}
          confirm={confirm}
          clearFilters={clearFilters!}
          startParamName="leftinquirytime"
          endParamName="rightinquirytime"
        />
      ),
    },
    {
      title: t("quotationTable.columns.freightMethod"),
      dataIndex: "freightmethod",
      key: "freightmethod",
      width: getColumnWidth(80, 130),
      render: (text) => {
        return text ? text : t("quotationTable.noData");
      },
    },
    {
      title: t("quotationTable.columns.shippedPlace"),
      dataIndex: "shippedplace",
      key: "shippedplace",
      width: getColumnWidth(100, 130),
      render: (text) => {
        return text ? text : t("quotationTable.noData");
      },
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }) => (
        <SelectFilter
          setSelectedKeys={setSelectedKeys}
          selectedKeys={selectedKeys}
          confirm={confirm}
          clearFilters={clearFilters!}
          options={shipmentPlaceOptions}
          placeholder={t("quotationTable.filters.selectShippedPlace")}
          paramName="shippedplace"
          showSearch={true}
        />
      ),
      filterIcon: (filtered) => (
        <span style={{ color: filtered ? "#1890ff" : undefined }}>🔍</span>
      ),
    },
    {
      title: t("quotationTable.columns.originPort"),
      dataIndex: "originport",
      key: "originport",
      width: getColumnWidth(120, 150),
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }) => (
        <SelectFilter
          setSelectedKeys={setSelectedKeys}
          selectedKeys={selectedKeys}
          confirm={confirm}
          clearFilters={clearFilters!}
          options={portOptions}
          placeholder={t("quotationTable.filters.selectOriginPort")}
          paramName="originport"
          showSearch={true}
        />
      ),
      filterIcon: (filtered) => (
        <span style={{ color: filtered ? "#1890ff" : undefined }}>🔍</span>
      ),
    },
    {
      title: t("quotationTable.columns.destinationPort"),
      dataIndex: "unloadingport",
      key: "unloadingport",
      width: getColumnWidth(100, 130),
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }) => (
        <SelectFilter
          setSelectedKeys={setSelectedKeys}
          selectedKeys={selectedKeys}
          confirm={confirm}
          clearFilters={clearFilters!}
          options={portOptions}
          placeholder={t("quotationTable.filters.selectDestinationPort")}
          paramName="unloadingport"
          showSearch={true}
        />
      ),
      filterIcon: (filtered) => (
        <span style={{ color: filtered ? "#1890ff" : undefined }}>🔍</span>
      ),
    },
    {
      title: t("quotationTable.columns.goodsType"),
      dataIndex: "goodstype",
      key: "goodstype",
      width: getColumnWidth(90, 120),
      render: (text) => {
        return text ? text : t("quotationTable.noData");
      },
      filters: [
        { text: t("quotationTable.goodsTypes.fcl"), value: "FCL" },
        { text: t("quotationTable.goodsTypes.lcl"), value: "LCL" },
      ],
    },
    {
      title: t("quotationTable.columns.grossWeight"),
      dataIndex: "grossweight",
      key: "grossweight",
      width: getColumnWidth(90, 120),
      sorter: (a, b) => {
        const aValue = Number(a.grossweight) || 0;
        const bValue = Number(b.grossweight) || 0;
        return aValue - bValue;
      },
    },
    {
      title: t("quotationTable.columns.goodsVolume"),
      dataIndex: "goodsvolume",
      key: "goodsvolume",
      width: getColumnWidth(90, 120),
      sorter: (a, b) => {
        const aValue = Number(a.goodsvolume) || 0;
        const bValue = Number(b.goodsvolume) || 0;
        return aValue - bValue;
      },
      render: (text) => {
        return text ? text : t("quotationTable.noData");
      },
    },
    {
      title: t("quotationTable.columns.singleMaxWeight"),
      dataIndex: "singlemaxweight",
      key: "singlemaxweight",
      width: getColumnWidth(130, 140),
      sorter: (a, b) => {
        const aValue = Number(a.singlemaxweight) || 0;
        const bValue = Number(b.singlemaxweight) || 0;
        return aValue - bValue;
      },
      render: (text) => {
        return text ? text : t("quotationTable.noData");
      },
    },
    {
      title: t("quotationTable.columns.cargoSize"),
      dataIndex: "cargosize",
      key: "cargosize",
      width: getColumnWidth(150, 160),
      render: (_, record) => {
        if (record?.cargowidth && record?.cargolength && record?.cargoheight) {
          return `${record.cargolength}cm*${record.cargowidth}cm*${record.cargoheight}cm`;
        } else {
          return t("quotationTable.noData");
        }
      },
    },
    {
      title: t("quotationTable.columns.companyName"),
      dataIndex: "companyname",
      key: "companyname",
      width: getColumnWidth(100, 120),
      filterDropdown: createTextFilter(
        t("quotationTable.filters.inputCompanyName")
      ),
      filterIcon: (filtered) => (
        <span style={{ color: filtered ? "#1890ff" : undefined }}>🔍</span>
      ),
      render: (text) => {
        return text ? text : t("quotationTable.noData");
      },
    },
    {
      title: t("quotationTable.columns.inquirer"),
      dataIndex: "inquirer",
      key: "inquirer",
      width: getColumnWidth(100, 120),
      filterDropdown: createTextFilter(
        t("quotationTable.filters.inputInquirer")
      ),
      filterIcon: (filtered) => (
        <span style={{ color: filtered ? "#1890ff" : undefined }}>🔍</span>
      ),
      render: (text) => {
        return text ? text : t("quotationTable.noData");
      },
    },
    {
      title: t("quotationTable.columns.isBrand"),
      dataIndex: "isbrand",
      key: "isbrand",
      width: getColumnWidth(90, 120),
      filters: booleanOptions,
      filterMultiple: false,
      render: (value) => (
        <Badge
          status={value ? "success" : "default"}
          text={
            value
              ? t("quotationTable.booleanValues.yes")
              : t("quotationTable.booleanValues.no")
          }
        />
      ),
    },
    {
      title: t("quotationTable.columns.ensureCabin"),
      dataIndex: "ensurecabin",
      key: "ensurecabin",
      width: getColumnWidth(90, 120),
      filters: booleanOptions,
      render: (value) => (
        <Badge
          status={value ? "success" : "default"}
          text={
            value
              ? t("quotationTable.booleanValues.yes")
              : t("quotationTable.booleanValues.no")
          }
        />
      ),
    },
    {
      title: t("quotationTable.columns.requireETD"),
      dataIndex: "isvalidity",
      key: "isvalidity",
      width: getColumnWidth(100, 120),
      filters: booleanOptions,
      render: (value) => (
        <Badge
          status={value ? "success" : "default"}
          text={
            value
              ? t("quotationTable.booleanValues.yes")
              : t("quotationTable.booleanValues.no")
          }
        />
      ),
    },
    {
      title: t("quotationTable.columns.shipmentDate"),
      dataIndex: "shipmentdate",
      key: "shipmentdate",
      width: getColumnWidth(100, 130),
      render: (text) => (
        <span>
          {text ? dayjs(text).format("YYYY-MM-DD") : t("quotationTable.noData")}
        </span>
      ),
    },
    {
      title: t("quotationTable.columns.packageType"),
      dataIndex: "packagetype",
      key: "packagetype",
      width: getColumnWidth(110, 130),
      filters: packageTypeOptions?.map((item) => {
        return {
          text: item.label,
          value: item.value,
        };
      }),
      render: (value) => (
        <Tag color="blue">{value || t("quotationTable.noValue")}</Tag>
      ),
    },
    {
      title: t("quotationTable.columns.specialCargo"),
      dataIndex: "specialcargo",
      key: "specialcargo",
      width: 200,
      filters: specialItemOptions?.map((item) => {
        return {
          text: item.label,
          value: item.value,
        };
      }),
      render: (value) => {
        if (!value) return <Tag>{t("quotationTable.noValue")}</Tag>;
        return (
          <Tooltip title={value} key={value}>
            {value.split(",").map((item: string, index: number) => (
              <Tag color="orange" key={index}>
                {item}
              </Tag>
            ))}
          </Tooltip>
        );
      },
    },
    {
      title: t("quotationTable.columns.status"),
      dataIndex: "inquirystate",
      key: "inquirystate",
      width: getColumnWidth(110, 120),
      render: (status) => {
        let color = "";
        let text = "";
        let icon = null;
        switch (status) {
          case 0:
            color = "warning";
            text = t("quotationTable.status.notQuoted");
            icon = <ClockCircleOutlined />;
            break;
          case 2:
            color = "processing";
            text = t("quotationTable.status.deal");
            icon = <DollarOutlined />;
            break;
          case 1:
            color = "error";
            text = t("quotationTable.status.noDeal");
            icon = <CloseCircleOutlined />;
            break;
          default:
            color = "default";
            text = t("quotationTable.status.unknown");
        }

        return (
          <Tag color={color} icon={icon} className="status-tag">
            {text}
          </Tag>
        );
      },
    },
    {
      title: t("quotationTable.columns.actions"),
      key: "action",
      fixed: "right",
      width: currentLanguage === "zh-CN" ? 240 : 300,
      render: (_, record) => {
        // 判断当前记录是否可以编辑和删除
        // 如果是询价部门主管且选择了特定用户，则不能编辑和删除其他人的询价
        const canEditAndDelete = !(
          user?.useridentity === 3 &&
          selectedUserId &&
          record.userid !== user.userid
        );

        return (
          <Space size={10} className="operation-buttons">
            {canEditAndDelete && (
              <>
                <Tooltip title={t("quotationTable.actions.edit")}>
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    onClick={() => handleEdit(record)}
                    className="edit-button"
                    size="small"
                  ></Button>
                </Tooltip>
                <Tooltip title={t("quotationTable.actions.delete")}>
                  <Popconfirm
                    title={t("quotationTable.confirmDelete.title")}
                    description={t("quotationTable.confirmDelete.description")}
                    onConfirm={() => handleDel(record)}
                    onCancel={handleCancel}
                    okText={t("quotationTable.confirmDelete.okText")}
                    cancelText={t("quotationTable.confirmDelete.cancelText")}
                  >
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      className="delete-button"
                      size="small"
                    ></Button>
                  </Popconfirm>
                </Tooltip>
              </>
            )}
            <Space>
              <Button
                type="primary"
                onClick={() => handleQueryPrice(record)}
                size="small"
              >
                {t("quotationTable.actions.queryPrice")}
              </Button>
              <Button
                type="default"
                onClick={() => handleManualQuote(record)}
                size="small"
              >
                {t("quotationTable.actions.manualQuote")}
              </Button>
            </Space>
          </Space>
        );
      },
    },
  ];
};
