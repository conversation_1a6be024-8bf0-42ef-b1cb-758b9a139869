import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// 定义航司数据类型
export interface Airline {
  airlineid: number;
  cnairlinename: string;
  enairlinename: string;
}

// 定义港口数据类型
export interface Port {
  portid: number;
  cnportname: string;
  enportname: string;
}

// 定义包装类型数据类型
export interface PackageType {
  packagetypeid: number;
  packagename: string;
}

// 定义特殊货物数据类型
export interface SpecialItem {
  specialitemsid: number;
  specialitemsname: string;
}

// 定义发货地数据类型
export interface ShipmentPlace {
  placeid: number;
  enplace: string;
  enprovince: string;
}

// 定义供应商数据类型
export interface Supplier {
  supplierid: number;
  suppliername: string;
}

// 定义基础数据状态类型
export interface BaseDataState {
  airlines: Airline[];
  ports: Port[];
  packageTypes: PackageType[];
  specialItems: SpecialItem[];
  shipmentPlaces: ShipmentPlace[];
  suppliers: Supplier[];
}

// 初始状态
const initialState: BaseDataState = {
  airlines: [],
  ports: [],
  packageTypes: [],
  specialItems: [],
  shipmentPlaces: [],
  suppliers: [],
};

// 创建slice
const baseDataSlice = createSlice({
  name: "baseData",
  initialState,
  reducers: {
    // 设置航司数据
    setAirlines: (state, action: PayloadAction<Airline[]>) => {
      state.airlines = action.payload;
    },
    // 设置港口数据
    setPorts: (state, action: PayloadAction<Port[]>) => {
      state.ports = action.payload;
    },
    // 设置包装类型数据
    setPackageTypes: (state, action: PayloadAction<PackageType[]>) => {
      state.packageTypes = action.payload;
    },
    // 设置特殊货物数据
    setSpecialItems: (state, action: PayloadAction<SpecialItem[]>) => {
      state.specialItems = action.payload;
    },
    // 设置发货地数据
    setShipmentPlaces: (state, action: PayloadAction<ShipmentPlace[]>) => {
      state.shipmentPlaces = action.payload;
    },
    // 设置供应商数据
    setSuppliers: (state, action: PayloadAction<Supplier[]>) => {
      state.suppliers = action.payload;
    },
  },
});

export const {
  setAirlines,
  setPorts,
  setPackageTypes,
  setSpecialItems,
  setShipmentPlaces,
  setSuppliers,
} = baseDataSlice.actions;

export default baseDataSlice.reducer;
