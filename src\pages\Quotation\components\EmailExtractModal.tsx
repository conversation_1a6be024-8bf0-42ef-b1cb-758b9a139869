import React, { useState } from "react";
import {
  Modal,
  Steps,
  Input,
  Button,
  Form,
  Card,
  Row,
  Col,
  message,
  Alert,
  Typography,
  Progress,
  DatePicker,
  InputNumber,
  Switch,
  Select,
} from "antd";
import {
  MailOutlined,
  RobotOutlined,
  EditOutlined,
  CheckOutlined,
  LoadingOutlined,
  ThunderboltOutlined,
  FileTextOutlined,
} from "@ant-design/icons";
import { chatCompletions } from "@/services/secondaryService";
import useBaseData from "@/hooks/useBaseData";
import { useWatch } from "antd/es/form/Form";
import dayjs from "dayjs";
import "./index.less";
import { useTranslation } from "react-i18next";
import { useAppSelector } from "@/store/hooks";
import {
  formatDateToTimestamp,
  convertToCentimeters,
  convertShippedPlaceToEnglish,
} from "@/utils/util";
import { addInquiry } from "../services";

const { TextArea } = Input;
const { Step } = Steps;
const { Title, Paragraph } = Typography;

// 邮件提取的系统提示词
const EMAIL_EXTRACT_SYSTEM_PROMPT = `请你从我输入的信息中提取以下有效信息：
提货城市（与提货/发货/货物地址相关的才是提货城市），目的城市（城市名和机场名均可，可多个，不能是中国城市及机场，如香港、澳门、台湾和中国的其他城市，无需考虑托运信息），货物件数，货物毛重（记录毛重和件数的关系。有多个总毛重，则每个总毛重均记录1件，不记录真实件数。无则输出-1），货物体积（记录体积和件数的关系。有多个总体积，则每个总体积均记录1件，不记录真实件数。无则输出-1，禁止使用尺寸计算体积），货物尺寸(需记录件数和尺寸的关系。若为给出4个维度数值相乘，则最后一个数值表示该尺寸对应的件数)，包装形式（可根据件数后的包装来判断：纸箱、木箱、布卷、麻袋、桶、托盘，"boxes"和"packages"均判定为有效，记录为纸箱，包装形式只记录一种），货物品牌（"brands"判定为有效，其它情况均记录为-1），，特殊货物（仅6类：液体、锂电、带电、航材、温控、生鲜活体，可记录多类。明确标注的"general cargo"则判定有效，记录为无，除"general cargo"外的其他所有情况均记录为“-1”），要求ETD（若给出具体日期，当日及过去日期则判定无效，记录为-1；若给出日期范围则记录最小日期（从明日起）。需提供可选时间则记录为-1；无要求则记录为-1；时间紧急则记录为明日实际日期。提货时间也可作为ETD），是否保舱，信息无效的类目内容输出为-1

有多件货物则计算总件数，其他任何信息（除包装外）若有多个，则均记录，禁止计算总毛重和总体积

输出格式（毛重以数字+kg (件数)输出，体积以数字+cbm (件数)输出，尺寸以长x宽x高+单位(件数)输出，没有件数关系时默认为1件）：
<提货城市：，目的城市：，货物件数：，货物毛重：10kg(1件)，货物体积：2cbm(1件)，货物尺寸：30x20x40cm(1件)，包装形式：，货物品牌：，特殊货物：，要求ETD：，保舱：>，信息无效的类目内容输出为-1`;

// 第二轮对话：港口转换的系统提示词
const generatePortConvertSystemPrompt = (supplementInfo1: string) => `
Current date: ${dayjs().format("YYYY-MM-DD")}
阅读补充信息1中的内容，将其严格按照输出格式进行输出，禁止输出解释。
逐条对比以下规则，调整格式：
1、要求ETD：将具体日期转化为输出格式，转化后若为当日或过去日期，则输出-1，否则按格式输出，补充后的日期需为未来日期。
2、品牌货：对应补充信息1中的货物品牌。
3、发货地：对应补充信息1中的提货城市。
4、货物毛重：对应补充信息1中的货物毛重，输出单位需为kg，无毛重则输出-1。禁止计算总毛重。
5、货物体积：对应补充信息1中的货物体积，输出单位需为cbm，无体积则输出-1。禁止计算总体积。
6、货物尺寸：均以长x宽x高+单位(件数)输出。无尺寸则输出-1。
7、保舱：若为否，则输出-1。
8、从补充信息2中获取：
目的港缩写：目的城市小写需转为大写。根据目的城市确定目的港，支持记录多个目的港。

补充信息1：
${supplementInfo1}

补充信息2：
目的港：
莫斯科对应的目的港为SVO
圣彼得堡对应的目的港为LED
其他城市对应的目的港为SVO/LED

输出格式:
<目的港：SVO/LED
货物件数：
货物毛重：220kg(1件)
货物体积：1.03cbm(1件)
货物尺寸：20x30x40cm(3件)
包装形式：
品牌货：
特殊货物：
发货地：深圳
要求ETD：2025-06-08
保舱：是>
信息无效的类目内容输出为-1`;

interface EmailExtractModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void; // 刷新列表
}

interface ExtractedData {
  companyname?: string;
  inquirer?: string;
  originport?: string;
  unloadingport?: string | string[]; // 支持多个目的港
  shippedplace?: string;
  grossweight?: number;
  goodsvolume?: number;
  goodsnumber?: string;
  singlemaxweight?: number | null;
  cargolength?: number;
  cargowidth?: number;
  cargoheight?: number;
  calculatesizes?: string;
  isbrand?: boolean;
  specialcargo?: string | string[];
  isvalidity?: boolean;
  shipmentdate?: string;
  ensurecabin?: boolean;
  packagetype?: string;
}

interface InquiryRecord extends ExtractedData {
  unloadingport: string;
}

const EmailExtractModal: React.FC<EmailExtractModalProps> = ({
  open,
  onClose,
  onConfirm,
}) => {
  const { t } = useTranslation();
  const { user } = useAppSelector((state) => state.user);
  const [currentStep, setCurrentStep] = useState(0);
  const [emailContent, setEmailContent] = useState("");
  const [inquiryRecords, setInquiryRecords] = useState<InquiryRecord[]>([]); // 将要创建的询价记录列表
  const [loading, setLoading] = useState(false);
  const [extractProgress, setExtractProgress] = useState(0);
  const [createProgress, setCreateProgress] = useState(0);
  const [isCreating, setIsCreating] = useState(false);
  const [form] = Form.useForm();

  const {
    portOptions: portList,
    shipmentPlaceOptions: shipmentPlaceList,
    packageTypeOptions: packageTypeList,
    specialItemOptions: specialItemsList,
    loadAll,
  } = useBaseData();

  const isValidity = useWatch("isvalidity", form);

  React.useEffect(() => {
    loadAll();
  }, [loadAll]);

  // 从信息字符串中提取括号内的件数总和
  const extractPieceCountFromInfo = (infoString: string): number => {
    if (!infoString || infoString === "-1") {
      return 0;
    }

    try {
      // 匹配所有括号内的件数，支持格式：(10件)、(3件)等
      const pieceMatches = infoString.match(/\((\d+)件\)/g);
      if (!pieceMatches) {
        return 0;
      }

      let totalPieces = 0;
      pieceMatches.forEach((match) => {
        const pieceMatch = match.match(/\((\d+)件\)/);
        if (pieceMatch) {
          totalPieces += parseInt(pieceMatch[1], 10);
        }
      });

      return totalPieces;
    } catch (error) {
      console.error("提取件数信息时出错:", error);
      return 0;
    }
  };

  // 从货物毛重信息中计算总毛重（每个重量乘以对应件数的总和）
  const calculateTotalWeightFromInfo = (weightInfo: string): number => {
    if (!weightInfo || weightInfo === "-1") {
      return 0;
    }

    try {
      // 匹配重量和件数的组合，支持格式：220kg(10件)、100kg(5件)等
      const weightMatches = weightInfo.match(/(\d+(?:\.\d+)?)kg\((\d+)件\)/g);
      if (!weightMatches) {
        // 如果没有匹配到带件数的格式，尝试匹配单纯的重量
        const simpleWeightMatch = weightInfo.match(/(\d+(?:\.\d+)?)kg/);
        return simpleWeightMatch ? parseFloat(simpleWeightMatch[1]) : 0;
      }

      let totalWeight = 0;
      weightMatches.forEach((match) => {
        const weightMatch = match.match(/(\d+(?:\.\d+)?)kg\((\d+)件\)/);
        if (weightMatch) {
          const weight = parseFloat(weightMatch[1]);
          const pieces = parseInt(weightMatch[2], 10);
          totalWeight += weight * pieces;
        }
      });

      return totalWeight;
    } catch (error) {
      console.error("计算总毛重时出错:", error);
      return 0;
    }
  };

  // 从货物尺寸信息中计算总体积（每个尺寸的体积乘以对应件数的总和）
  const calculateTotalVolumeFromSizeInfo = (sizeInfo: string): number => {
    if (!sizeInfo || sizeInfo === "-1") {
      return 0;
    }

    try {
      // 匹配尺寸和件数的组合，支持格式：78x41x41cm(3件)、41x41x45cm(4件)等
      const sizeMatches = sizeInfo.match(
        /(\d+(?:\.\d+)?)\s*[x*×]\s*(\d+(?:\.\d+)?)\s*[x*×]\s*(\d+(?:\.\d+)?)(?:\s*([a-zA-Z"']+|厘米|毫米|米|英寸|英尺|码))?\((\d+)件\)/gi
      );
      if (!sizeMatches) {
        return 0;
      }

      let totalVolume = 0;
      sizeMatches.forEach((match) => {
        const sizeMatch = match.match(
          /(\d+(?:\.\d+)?)\s*[x*×]\s*(\d+(?:\.\d+)?)\s*[x*×]\s*(\d+(?:\.\d+)?)(?:\s*([a-zA-Z"']+|厘米|毫米|米|英寸|英尺|码))?\((\d+)件\)/i
        );
        if (sizeMatch) {
          const length = parseFloat(sizeMatch[1]);
          const width = parseFloat(sizeMatch[2]);
          const height = parseFloat(sizeMatch[3]);
          const unit = sizeMatch[4] || "cm";
          const pieces = parseInt(sizeMatch[5], 10);

          // 将所有尺寸转换为厘米
          const lengthCm = convertToCentimeters(length, unit);
          const widthCm = convertToCentimeters(width, unit);
          const heightCm = convertToCentimeters(height, unit);

          // 计算单个体积（立方厘米）
          const volumeCm3 = lengthCm * widthCm * heightCm;
          // 转换为立方米（1立方米 = 1,000,000立方厘米）
          const volumeM3 = volumeCm3 / 1000000;

          // 乘以件数并累加
          totalVolume += volumeM3 * pieces;
        }
      });

      return Math.round(totalVolume * 1000) / 1000; // 保留3位小数
    } catch (error) {
      console.error("计算总体积时出错:", error);
      return 0;
    }
  };

  // 从货物尺寸信息中提取最大的长、宽、高
  const extractMaxDimensionsFromSizeInfo = (
    sizeInfo: string
  ): { length: number; width: number; height: number } => {
    if (!sizeInfo || sizeInfo === "-1") {
      return { length: 0, width: 0, height: 0 };
    }

    try {
      // 匹配尺寸和件数的组合，支持格式：78x41x41cm(3件)、41x41x45cm(4件)等
      const sizeMatches = sizeInfo.match(
        /(\d+(?:\.\d+)?)\s*[x*×]\s*(\d+(?:\.\d+)?)\s*[x*×]\s*(\d+(?:\.\d+)?)(?:\s*([a-zA-Z"']+|厘米|毫米|米|英寸|英尺|码))?\((\d+)件\)/gi
      );
      if (!sizeMatches) {
        return { length: 0, width: 0, height: 0 };
      }

      let maxLength = 0;
      let maxWidth = 0;
      let maxHeight = 0;

      sizeMatches.forEach((match) => {
        const sizeMatch = match.match(
          /(\d+(?:\.\d+)?)\s*[x*×]\s*(\d+(?:\.\d+)?)\s*[x*×]\s*(\d+(?:\.\d+)?)(?:\s*([a-zA-Z"']+|厘米|毫米|米|英寸|英尺|码))?\((\d+)件\)/i
        );
        if (sizeMatch) {
          const length = parseFloat(sizeMatch[1]);
          const width = parseFloat(sizeMatch[2]);
          const height = parseFloat(sizeMatch[3]);
          const unit = sizeMatch[4] || "cm";

          // 将所有尺寸转换为厘米
          const lengthCm = convertToCentimeters(length, unit);
          const widthCm = convertToCentimeters(width, unit);
          const heightCm = convertToCentimeters(height, unit);

          // 更新最大值
          maxLength = Math.max(maxLength, lengthCm);
          maxWidth = Math.max(maxWidth, widthCm);
          maxHeight = Math.max(maxHeight, heightCm);
        }
      });

      return {
        length: Math.round(maxLength * 1000) / 1000, // 保留3位小数
        width: Math.round(maxWidth * 1000) / 1000,
        height: Math.round(maxHeight * 1000) / 1000,
      };
    } catch (error) {
      console.error("提取最大尺寸时出错:", error);
      return { length: 0, width: 0, height: 0 };
    }
  };

  // 计算多个尺寸的总体积（立方米）
  const calculateVolumeFromSizes = (sizesString: string): number => {
    if (!sizesString || sizesString === "-1") {
      return 0;
    }

    try {
      // 按逗号分割多个尺寸
      const sizes = sizesString
        .split(",")
        .map((size) => size.trim())
        .filter((size) => size);
      let totalVolume = 0;

      sizes.forEach((sizeStr) => {
        // 匹配尺寸和单位，支持多种格式：20x30x40cm, 20*30*40 inches等
        const sizeMatch = sizeStr.match(
          /(\d+(?:\.\d+)?)\s*[x*×]\s*(\d+(?:\.\d+)?)\s*[x*×]\s*(\d+(?:\.\d+)?)(?:\s*([a-zA-Z"']+|厘米|毫米|米|英寸|英尺|码))?/i
        );

        if (sizeMatch) {
          const length = parseFloat(sizeMatch[1]);
          const width = parseFloat(sizeMatch[2]);
          const height = parseFloat(sizeMatch[3]);
          const unit = sizeMatch[4] || "cm";

          // 将所有尺寸转换为厘米
          const lengthCm = convertToCentimeters(length, unit);
          const widthCm = convertToCentimeters(width, unit);
          const heightCm = convertToCentimeters(height, unit);

          // 计算体积（立方厘米）
          const volumeCm3 = lengthCm * widthCm * heightCm;

          // 转换为立方米（1立方米 = 1,000,000立方厘米）
          const volumeM3 = volumeCm3 / 1000000;

          totalVolume += volumeM3;
        }
      });
      return Math.round(totalVolume * 1000) / 1000; // 保留6位小数
    } catch (error) {
      console.error("计算尺寸体积时出错:", error);
      return 0;
    }
  };

  // 解析第二轮AI返回结果
  const parsePortConvertResponse = (aiResponse: string): ExtractedData => {
    try {
      // 查找<>包围的内容
      const match = aiResponse.match(/<([^>]+)>/);
      if (!match) {
        throw new Error(
          t("emailExtractModal.errors.aiResponseFormatIncorrect")
        );
      }

      const content = match[1];
      const lines = content.split("\n").filter((line) => line.trim());

      const result: ExtractedData = {};

      // 用于存储货物毛重和货物尺寸信息，以便后续计算件数
      let weightInfo = "";
      let sizeInfo = "";

      lines.forEach((line) => {
        const [key, value] = line.split("：");
        if (key && value !== undefined) {
          const trimmedValue = value.trim();

          switch (key.trim()) {
            case "目的港":
              if (trimmedValue === "-1") {
                result.unloadingport = "";
              } else {
                // 处理多个目的港的情况，如 "SVO/LED"
                const ports = trimmedValue
                  .split("/")
                  .map((port) => port.trim())
                  .filter((port) => port);
                result.unloadingport = ports.length > 1 ? ports : trimmedValue;
              }
              break;
            case "货物件数":
              // 暂时存储原始值，后续会重新计算
              result.goodsnumber = trimmedValue === "-1" ? "" : trimmedValue;
              break;
            case "包装形式":
              result.packagetype = trimmedValue === "-1" ? "" : trimmedValue;
              break;
            case "货物毛重":
              if (trimmedValue !== "-1") {
                // 存储原始毛重信息用于后续计算件数
                weightInfo = trimmedValue;
                // 计算总毛重（每个重量乘以对应件数的总和）
                const totalWeight = calculateTotalWeightFromInfo(trimmedValue);
                result.grossweight = totalWeight > 0 ? totalWeight : undefined;
              }
              break;
            case "货物体积":
              if (trimmedValue !== "-1") {
                // 优先从尺寸信息计算总体积，如果没有尺寸信息则使用原始体积值
                const calculatedVolume =
                  calculateTotalVolumeFromSizeInfo(sizeInfo);
                if (calculatedVolume > 0) {
                  result.goodsvolume = calculatedVolume;
                } else {
                  const volumeMatch = trimmedValue.match(/(\d+(?:\.\d+)?)/);
                  result.goodsvolume = volumeMatch
                    ? parseFloat(volumeMatch[1])
                    : undefined;
                }
              }
              break;
            case "货物尺寸":
              if (trimmedValue !== "-1") {
                // 存储原始尺寸信息用于后续计算件数和最大尺寸
                sizeInfo = trimmedValue;
                // 暂时使用第一个尺寸作为默认值，后续会重新计算最大尺寸
                const sizeMatch = trimmedValue.match(
                  /(\d+(?:\.\d+)?)\s*[x*×]\s*(\d+(?:\.\d+)?)\s*[x*×]\s*(\d+(?:\.\d+)?)(?:\s*([a-zA-Z"']+|厘米|毫米|米|英寸|英尺|码))?/i
                );
                if (sizeMatch) {
                  const length = parseFloat(sizeMatch[1]);
                  const width = parseFloat(sizeMatch[2]);
                  const height = parseFloat(sizeMatch[3]);
                  const unit = sizeMatch[4] || "cm";

                  // 使用单位换算函数将所有尺寸转换为厘米
                  result.cargolength = convertToCentimeters(length, unit);
                  result.cargowidth = convertToCentimeters(width, unit);
                  result.cargoheight = convertToCentimeters(height, unit);
                }
              }
              break;
            case "计算尺寸":
              result.calculatesizes = trimmedValue === "-1" ? "" : trimmedValue;
              break;
            case "品牌货":
              result.isbrand = trimmedValue !== "-1";
              break;
            case "特殊货物":
              if (trimmedValue === "-1" || trimmedValue === "无") {
                result.specialcargo = undefined;
              } else {
                const specialcargo = trimmedValue
                  .split(",")
                  .map((item) => item.trim())
                  .filter((item) => item);
                result.specialcargo =
                  specialcargo.length > 1 ? specialcargo : trimmedValue;
              }
              break;
            case "发货地":
              if (trimmedValue === "-1") {
                result.shippedplace = "";
              } else {
                // 转换发货地为英文并确保首字母大写
                const englishPlace = convertShippedPlaceToEnglish(trimmedValue);
                result.shippedplace = englishPlace
                  ? englishPlace.charAt(0).toUpperCase() +
                    englishPlace.slice(1).toLowerCase()
                  : "";
              }
              break;
            case "要求ETD":
              if (trimmedValue !== "-1") {
                result.isvalidity = true;
                result.shipmentdate = dayjs(trimmedValue).format("YYYY-MM-DD");
              } else {
                result.isvalidity = false;
              }
              break;
            case "保舱":
              result.ensurecabin = trimmedValue !== "-1";
              break;
          }
        }
      });

      // 重新计算货物体积：从尺寸信息计算总体积（每个尺寸的体积乘以对应件数的总和）
      if (sizeInfo) {
        const calculatedVolume = calculateTotalVolumeFromSizeInfo(sizeInfo);
        if (calculatedVolume > 0) {
          result.goodsvolume = calculatedVolume;
          console.log(`从尺寸信息重新计算货物体积: ${calculatedVolume}m³`);
        }
      }

      // 重新计算货物尺寸：提取所有尺寸中的最大长、宽、高
      if (sizeInfo) {
        const maxDimensions = extractMaxDimensionsFromSizeInfo(sizeInfo);
        if (
          maxDimensions.length > 0 ||
          maxDimensions.width > 0 ||
          maxDimensions.height > 0
        ) {
          result.cargolength = maxDimensions.length;
          result.cargowidth = maxDimensions.width;
          result.cargoheight = maxDimensions.height;
          console.log(
            `从尺寸信息重新计算货物尺寸: 长=${maxDimensions.length}cm, 宽=${maxDimensions.width}cm, 高=${maxDimensions.height}cm`
          );
        }
      }

      // 重新计算货物件数：取货物毛重和货物尺寸信息括号内件数总和的最大值
      if (weightInfo || sizeInfo) {
        const weightPieceCount = extractPieceCountFromInfo(weightInfo);
        const sizePieceCount = extractPieceCountFromInfo(sizeInfo);
        const maxPieceCount = Math.max(weightPieceCount, sizePieceCount);

        if (maxPieceCount > 0) {
          result.goodsnumber = maxPieceCount.toString();
          console.log(
            `重新计算货物件数: 毛重件数=${weightPieceCount}, 尺寸件数=${sizePieceCount}, 最终件数=${maxPieceCount}`
          );
        }
      }

      return result;
    } catch (error) {
      console.error(
        t("emailExtractModal.console.parseSecondRoundFailed"),
        error
      );
      throw new Error(t("emailExtractModal.errors.parseSecondRoundFailed"));
    }
  };

  const generateInquiryRecords = (
    extractedInfo: ExtractedData
  ): InquiryRecord[] => {
    const { unloadingport, ...baseData } = extractedInfo;

    if (!unloadingport) {
      return [];
    }

    // 确保发货地已经转换为英文
    const processedBaseData = {
      ...baseData,
      shippedplace: baseData.shippedplace
        ? convertShippedPlaceToEnglish(baseData.shippedplace)
        : baseData.shippedplace,
    };

    // 如果是字符串数组，为每个目的港创建一条记录
    if (Array.isArray(unloadingport)) {
      return unloadingport.map((port) => ({
        ...processedBaseData,
        unloadingport: port,
      }));
    }

    // 如果是单个字符串，创建一条记录
    return [
      {
        ...processedBaseData,
        unloadingport: unloadingport as string,
      },
    ];
  };

  const resetState = () => {
    setCurrentStep(0);
    setEmailContent("");
    setInquiryRecords([]);
    setExtractProgress(0);
    setCreateProgress(0);
    setIsCreating(false);
    form.resetFields();
  };

  const handleClose = () => {
    resetState();
    onClose();
  };

  // AI提取信息（两轮对话）
  const extractInformation = async () => {
    if (!emailContent.trim()) {
      message.error(t("emailExtractModal.messages.inputRequired"));
      return;
    }

    setLoading(true);
    setExtractProgress(0);

    // 第一轮对话的进度更新（0% → 60%），约11秒，占总时间59.5%
    const firstProgressInterval = setInterval(() => {
      setExtractProgress((prev) => {
        if (prev >= 60) {
          clearInterval(firstProgressInterval);
          return 60;
        }
        return prev + 2;
      });
    }, 370);

    try {
      // 第一轮对话：提取基础信息
      const firstMessages = [
        {
          role: "system",
          content: EMAIL_EXTRACT_SYSTEM_PROMPT,
        },
        {
          role: "user",
          content: emailContent,
        },
      ];

      console.log(t("emailExtractModal.console.startFirstRound"));
      const firstResponse = await chatCompletions(firstMessages);
      const content =
        firstResponse?.data?.data?.body?.choices?.[0]?.message?.content;

      if (!content) {
        throw new Error(t("emailExtractModal.errors.firstRoundFormatError"));
      }

      const firstAiContent = content;
      console.log(
        t("emailExtractModal.console.firstRoundResult"),
        firstAiContent
      );

      // 清除第一轮进度更新，设置到60%
      clearInterval(firstProgressInterval);
      setExtractProgress(60);

      // 第二轮对话的进度更新（60% → 100%），约7.5秒，占总时间40.5%
      const secondProgressInterval = setInterval(() => {
        setExtractProgress((prev) => {
          if (prev >= 95) {
            clearInterval(secondProgressInterval);
            return 95;
          }
          return prev + 2;
        });
      }, 430);

      // 第二轮对话：转换为港口代码
      const secondMessages = [
        {
          role: "user",
          content: generatePortConvertSystemPrompt(firstAiContent),
        },
      ];

      console.log(t("emailExtractModal.console.startSecondRound"));
      const secondResponse = await chatCompletions(secondMessages);

      const secondAiContent =
        secondResponse?.data?.data?.body?.choices?.[0]?.message?.content;

      // 检查第二轮响应格式
      if (!secondAiContent) {
        throw new Error(t("emailExtractModal.errors.secondRoundFormatError"));
      }
      console.log(
        t("emailExtractModal.console.secondRoundResult"),
        secondAiContent
      );

      // 解析第二轮AI返回的结果
      const extractedInfo = parsePortConvertResponse(secondAiContent);

      const records = generateInquiryRecords(extractedInfo);
      setInquiryRecords(records);
      // 为表单设置第一条记录的数据（用于编辑）
      const formData = records.length > 0 ? records[0] : extractedInfo;

      // 处理日期字段，将字符串转换为dayjs对象
      const processedFormData = {
        ...formData,
        shipmentdate: formData.shipmentdate
          ? dayjs(formData.shipmentdate)
          : undefined,
      };

      console.log("处理后的数据：", processedFormData);
      form.setFieldsValue(processedFormData);

      // 清除第二轮进度更新，设置到100%
      clearInterval(secondProgressInterval);
      setExtractProgress(100);

      setCurrentStep(1);
      message.success(t("emailExtractModal.messages.extractSuccess"));
    } catch (error) {
      console.error(t("emailExtractModal.console.extractFailed"), error);
      setCurrentStep(0);

      if (error instanceof Error) {
        if (error.message.includes("API Key")) {
          message.error(t("emailExtractModal.messages.apiKeyInvalid"));
        } else if (
          error.message.includes(t("emailExtractModal.errors.parseKeyword"))
        ) {
          message.error(t("emailExtractModal.messages.parseFormatFailed"));
        } else {
          message.error(
            t("emailExtractModal.messages.extractFailedWithError", {
              error: error.message,
            })
          );
        }
      } else {
        message.error(t("emailExtractModal.messages.extractFailed"));
      }
    } finally {
      setLoading(false);
    }
  };

  // 格式化询价数据
  const formatInquiryData = (data: any) => {
    return {
      ...data,
      inquirytime: formatDateToTimestamp(dayjs()),
      shipmentdate: data?.shipmentdate
        ? formatDateToTimestamp(dayjs(data?.shipmentdate))
        : undefined,
      freightmethod: "AF",
      userid: user?.userid,
      departmentid: user?.departmentid,
      specialcargo: data?.specialcargo
        ? Array.isArray(data.specialcargo)
          ? data.specialcargo.join(",")
          : data.specialcargo
        : undefined,
    };
  };

  // 处理数据更新
  const updateRecordWithFormValues = (
    record: InquiryRecord,
    values: any,
    isFirstRecord: boolean = false
  ) => {
    const updatedShippedPlace = values.shippedplace
      ? convertShippedPlaceToEnglish(values.shippedplace)
      : record.shippedplace;

    if (isFirstRecord) {
      // 第一条记录使用表单中的所有数据
      return {
        ...record,
        ...values,
        shippedplace: updatedShippedPlace,
      };
    } else {
      // 其他记录只更新公共字段
      return {
        ...record,
        ...values,
        shippedplace: updatedShippedPlace,
        unloadingport: record?.unloadingport,
      };
    }
  };

  // 确认提取的信息并创建询价
  const handleConfirm = () => {
    form
      .validateFields()
      .then(async (values) => {
        setCurrentStep(2);
        setIsCreating(true);
        setCreateProgress(0);

        // 模拟创建询价的进度更新
        const createProgressInterval = setInterval(() => {
          setCreateProgress((prev) => {
            if (prev >= 90) {
              clearInterval(createProgressInterval);
              return 90;
            }
            return prev + 15;
          });
        }, 200);

        try {
          let recordsToCreate: any[];
          if (inquiryRecords.length > 1) {
            // 多条记录：更新第一条记录的数据，其他记录更新公共字段
            recordsToCreate = inquiryRecords.map((record, index) =>
              updateRecordWithFormValues(record, values, index === 0)
            );
          } else {
            // 单条记录：直接使用表单数据并转换发货地
            recordsToCreate = [
              {
                ...values,
                shippedplace: values.shippedplace
                  ? convertShippedPlaceToEnglish(values.shippedplace)
                  : values.shippedplace,
              },
            ];
          }

          // 批量创建询价记录
          const promises = recordsToCreate.map(async (data) => {
            const formattedData = formatInquiryData(data);
            return addInquiry(formattedData);
          });

          const results = await Promise.all(promises);
          const allSuccess = results.every(
            (res) => res.data?.resultCode === 200
          );

          if (!allSuccess) {
            throw new Error(t("emailExtractModal.messages.createFailed"));
          }

          // 模拟接口处理时间
          await new Promise((resolve) => setTimeout(resolve, 1500));
          clearInterval(createProgressInterval);
          setCreateProgress(100);

          // 延迟一下让用户看到100%完成状态，然后关闭模态框
          setTimeout(() => {
            handleClose();
            const recordCount = recordsToCreate.length;
            message.success(
              recordCount > 1
                ? t("emailExtractModal.messages.createMultipleSuccess", {
                    count: recordCount,
                  })
                : t("emailExtractModal.messages.createSuccess")
            );
          }, 800);
          onConfirm();
        } catch (error) {
          console.error(t("emailExtractModal.console.createFailed"), error);
          clearInterval(createProgressInterval);
          setIsCreating(false);
          setCurrentStep(1);
          message.error(t("emailExtractModal.messages.createFailed"));
        }
      })
      .catch(() => {
        message.error(t("emailExtractModal.messages.formValidationFailed"));
      });
  };

  const renderFooterButtons = () => {
    switch (currentStep) {
      case 0:
        return [
          <Button key="cancel" onClick={handleClose} disabled={loading}>
            {t("emailExtractModal.buttons.cancel")}
          </Button>,
          <Button
            key="extract"
            type="primary"
            onClick={extractInformation}
            loading={loading}
            icon={loading ? <LoadingOutlined /> : <RobotOutlined />}
            disabled={!emailContent.trim() || loading}
          >
            {loading
              ? t("emailExtractModal.buttons.extracting")
              : t("emailExtractModal.buttons.startExtract")}
          </Button>,
        ];
      case 1:
        return [
          <Button
            key="back"
            onClick={() => {
              setCurrentStep(0);
              form.resetFields();
            }}
            disabled={loading || isCreating}
          >
            {t("emailExtractModal.buttons.backToPrevious")}
          </Button>,
          <Button
            key="cancel"
            onClick={handleClose}
            disabled={loading || isCreating}
          >
            {t("emailExtractModal.buttons.cancel")}
          </Button>,
          <Button
            key="confirm"
            type="primary"
            onClick={handleConfirm}
            disabled={loading || isCreating}
            loading={isCreating}
          >
            {isCreating
              ? t("emailExtractModal.buttons.creating")
              : t("emailExtractModal.buttons.confirmAndCreate")}
          </Button>,
        ];
      case 2:
        return [
          <Button key="cancel" onClick={handleClose} disabled={isCreating}>
            {t("emailExtractModal.buttons.cancel")}
          </Button>,
        ];
      default:
        return [];
    }
  };

  const steps = [
    {
      title: t("emailExtractModal.steps.inputContent"),
      icon: <MailOutlined />,
    },
    {
      title: t("emailExtractModal.steps.confirmInfo"),
      icon: <EditOutlined />,
    },
    {
      title: t("emailExtractModal.steps.createQuotation"),
      icon: <CheckOutlined />,
    },
  ];

  return (
    <Modal
      title={t("emailExtractModal.title")}
      open={open}
      onCancel={loading || isCreating ? undefined : handleClose}
      width={900}
      centered
      footer={renderFooterButtons()}
      className="email-extract-modal"
      destroyOnClose
      maskClosable={!loading && !isCreating}
      closable={!loading && !isCreating}
    >
      <div className="modal-content">
        {/* 步骤指示器 */}
        <Steps
          current={currentStep}
          className="steps-container"
          size="small"
          responsive={false}
        >
          {steps.map((step, index) => (
            <Step key={index} title={step.title} icon={step.icon} />
          ))}
        </Steps>

        {/* 步骤内容容器 */}
        <div className="step-content-container">
          {/* 第一步：输入内容 */}
          {currentStep === 0 && (
            <Card
              title={t("emailExtractModal.stepCards.inputContentTitle")}
              className="step-card"
            >
              <Alert
                message={t("emailExtractModal.alerts.inputTip")}
                description={t("emailExtractModal.alerts.inputDescription")}
                type="info"
                showIcon
                style={{ marginBottom: 10 }}
              />
              <TextArea
                value={emailContent}
                onChange={(e) => setEmailContent(e.target.value)}
                placeholder={t("emailExtractModal.placeholders.emailContent")}
                rows={10}
              />
            </Card>
          )}

          {/* 第二步：显示提取结果并允许编辑 */}
          {currentStep === 1 && (
            <Card
              title={t("emailExtractModal.stepCards.confirmInfoTitle")}
              className="step-card"
            >
              <Alert
                message={t("emailExtractModal.alerts.confirmTitle")}
                description={t("emailExtractModal.alerts.confirmDescription")}
                type="success"
                showIcon
                style={{ marginBottom: 16 }}
              />

              {/* 显示将要创建的记录数量 */}
              {inquiryRecords.length > 1 && (
                <Alert
                  message={t("emailExtractModal.alerts.multiplePortsDetected", {
                    count: inquiryRecords.length,
                  })}
                  description={
                    <div>
                      <p>
                        {t("emailExtractModal.alerts.portListLabel")}
                        {inquiryRecords
                          .map((record) => record.unloadingport)
                          .join(", ")}
                      </p>
                      <div>
                        {t("emailExtractModal.alerts.editFirstRecordTip")}
                      </div>
                    </div>
                  }
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
              )}

              <Form
                form={form}
                layout="vertical"
                initialValues={{
                  inquirytime: dayjs(),
                  freightmethod: "AF",
                  goodstype: "FCL",
                  cargolength: 0,
                  cargowidth: 0,
                  cargoheight: 0,
                }}
              >
                <Row gutter={[24, 0]}>
                  <Col xs={24} sm={24} md={8}>
                    <Form.Item name="originport" label={t("common.originport")}>
                      <Select
                        className="full-width"
                        options={portList}
                        placeholder={t(
                          "supplyPrice.placeholders.selectOriginPort"
                        )}
                        showSearch
                        allowClear
                        optionFilterProp="label"
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={24} md={8}>
                    <Form.Item
                      name="unloadingport"
                      label={t("common.destinationport")}
                      rules={[
                        {
                          required: true,
                          message: t(
                            "supplyPrice.placeholders.selectDestinationPort"
                          ),
                        },
                      ]}
                    >
                      <Select
                        className="full-width"
                        options={portList}
                        placeholder={t(
                          "supplyPrice.placeholders.selectDestinationPort"
                        )}
                        showSearch
                        allowClear
                        optionFilterProp="label"
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={24} md={8}>
                    <Form.Item
                      name="shippedplace"
                      label={t("supplyPrice.fields.shippedPlace")}
                    >
                      <Select
                        className="full-width"
                        options={shipmentPlaceList}
                        placeholder={t(
                          "supplyPrice.placeholders.inputShippedPlace"
                        )}
                        showSearch
                        allowClear
                        optionFilterProp="label"
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={[24, 0]}>
                  <Col xs={24} sm={24} md={8}>
                    <Form.Item
                      name="grossweight"
                      label={t("supplyPrice.fields.grossWeight")}
                      rules={[
                        {
                          required: true,
                          message: t(
                            "emailExtractModal.validationMessages.inputGrossWeight"
                          ),
                        },
                      ]}
                    >
                      <InputNumber
                        className="full-width"
                        min={0}
                        placeholder={t(
                          "supplyPrice.placeholders.inputGrossWeight"
                        )}
                        precision={3}
                        step={0.01}
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={24} md={8}>
                    <Form.Item
                      name="goodsvolume"
                      label={t("supplyPrice.fields.goodsVolume")}
                    >
                      <InputNumber
                        className="full-width"
                        min={0}
                        placeholder={t(
                          "supplyPrice.placeholders.inputGoodsVolume"
                        )}
                        precision={3}
                        step={0.01}
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={24} md={8}>
                    <Form.Item
                      name="goodsnumber"
                      label={t("supplyPrice.fields.NumPackages")}
                    >
                      <Input
                        className="full-width"
                        placeholder={t(
                          "supplyPrice.placeholders.inputnumpackages"
                        )}
                        maxLength={200}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={[24, 0]}>
                  <Col xs={24} sm={8} md={8}>
                    <Form.Item
                      name="cargolength"
                      label={`${t("priceCard.fields.length")}(cm)`}
                    >
                      <InputNumber
                        className="full-width"
                        min={0}
                        placeholder={t(
                          "emailExtractModal.formPlaceholders.length"
                        )}
                        precision={3}
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={8} md={8}>
                    <Form.Item
                      name="cargowidth"
                      label={`${t("priceCard.fields.width")}(cm)`}
                    >
                      <InputNumber
                        className="full-width"
                        min={0}
                        placeholder={t(
                          "emailExtractModal.formPlaceholders.width"
                        )}
                        precision={3}
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={8} md={8}>
                    <Form.Item
                      name="cargoheight"
                      label={`${t("priceCard.fields.height")}(cm)`}
                    >
                      <InputNumber
                        className="full-width"
                        min={0}
                        placeholder={t(
                          "emailExtractModal.formPlaceholders.height"
                        )}
                        precision={3}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={[24, 0]}>
                  <Col xs={24} sm={6} md={6}>
                    <Form.Item
                      name="isbrand"
                      valuePropName="checked"
                      label={t("supplyPrice.fields.isBrand")}
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={6} md={6}>
                    <Form.Item
                      name="ensurecabin"
                      valuePropName="checked"
                      label={t("supplyPrice.fields.ensureCabin")}
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={6} md={6}>
                    <Form.Item
                      name="isvalidity"
                      valuePropName="checked"
                      label={t("supplyPrice.fields.requireETD")}
                    >
                      <Switch />
                    </Form.Item>
                  </Col>

                  <Col xs={24} sm={6} md={6}>
                    <Form.Item
                      name="sanctioned"
                      valuePropName="checked"
                      label={t("supplyPrice.fields.sanctioned")}
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                {isValidity && (
                  <Row gutter={[24, 0]}>
                    <Col xs={24} sm={12} md={12}>
                      <Form.Item
                        name="shipmentdate"
                        label={t("supplyPrice.fields.shippingDate")}
                        rules={[
                          {
                            required: true,
                            message: t(
                              "emailExtractModal.validationMessages.selectShipmentDate"
                            ),
                          },
                        ]}
                      >
                        <DatePicker
                          className="full-width"
                          format="YYYY-MM-DD"
                          placeholder={t(
                            "supplyPrice.placeholders.selectShipmentDate"
                          )}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                )}

                <Row gutter={[24, 0]}>
                  <Col xs={24} sm={24} md={8}>
                    <Form.Item
                      name="specialcargo"
                      label={t("common.specialcargo")}
                    >
                      <Select
                        className="full-width"
                        mode="multiple"
                        options={specialItemsList}
                        placeholder={t(
                          "supplyPrice.placeholders.selectSpecialCargo"
                        )}
                        optionFilterProp="label"
                        maxTagCount={5}
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={24} md={8}>
                    <Form.Item
                      name="packagetype"
                      label={t("supplyPrice.fields.packageType")}
                    >
                      <Select
                        className="full-width"
                        options={packageTypeList}
                        placeholder={t(
                          "supplyPrice.placeholders.selectPackageType"
                        )}
                        showSearch
                        allowClear
                        optionFilterProp="label"
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={24} md={8}>
                    <Form.Item
                      name="companyname"
                      label={t("common.companyname")}
                    >
                      <Input
                        className="full-width"
                        placeholder={t(
                          "supplyPrice.placeholders.inputcompanyname"
                        )}
                        maxLength={200}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            </Card>
          )}

          {/* 第三步：创建询价 */}
          {currentStep === 2 && (
            <Card
              title={t("emailExtractModal.stepCards.createQuotationTitle")}
              className="step-card"
            >
              <div
                className="create-quotation-content"
                style={{ textAlign: "center", padding: "20px 20px" }}
              >
                <div style={{ marginBottom: 24 }}>
                  <FileTextOutlined
                    style={{
                      fontSize: 48,
                      color: "#52c41a",
                      marginBottom: 16,
                    }}
                  />
                </div>
                <Title level={4} style={{ color: "#52c41a", marginBottom: 16 }}>
                  {t("emailExtractModal.loadingTexts.creatingQuotation")}
                </Title>
                <Paragraph style={{ color: "#666", marginBottom: 24 }}>
                  {t("emailExtractModal.loadingTexts.creatingQuotationDesc")}
                </Paragraph>
                <Progress
                  percent={createProgress}
                  status="active"
                  strokeColor={{
                    "0%": "#52c41a",
                    "100%": "#389e0d",
                  }}
                  style={{ marginBottom: 16 }}
                />
                <div style={{ color: "#666", fontSize: 12 }}>
                  {t("emailExtractModal.loadingTexts.creatingProgress")}
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>

      {/* AI提取信息加载覆盖层 */}
      {loading && (
        <div className="ai-extract-overlay">
          <div className="ai-extract-content">
            <div style={{ marginBottom: 24 }}>
              <ThunderboltOutlined
                style={{
                  fontSize: 48,
                  color: "#1890ff",
                  marginBottom: 16,
                }}
              />
            </div>
            <Title level={4} style={{ color: "#1890ff", marginBottom: 16 }}>
              {t("emailExtractModal.loadingTexts.aiAnalyzing")}
            </Title>
            <Paragraph style={{ color: "#666", marginBottom: 24 }}>
              {t("emailExtractModal.loadingTexts.aiAnalyzingDesc")}
            </Paragraph>
            <Progress
              percent={extractProgress}
              status="active"
              strokeColor={{
                "0%": "#108ee9",
                "100%": "#87d068",
              }}
              style={{ marginBottom: 16 }}
            />
            <div style={{ color: "#666", fontSize: 12 }}>
              {t("emailExtractModal.loadingTexts.analyzingProgress")}
            </div>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default EmailExtractModal;
