import React, { useState, useEffect, useRef } from "react";
import { message, InputRef } from "antd";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "@/store/hooks";
import "./index.less";
import CommandConsole from "./components/CommandConsole";
import SavedQuotations from "./components/SavedQuotations";
import QuotationDetailModal from "./components/QuotationDetailModal";
import NoAccessResult from "@/pages/AIQuotation/components/NoAccessResult";

// 导入类型
import { QuotationResult } from "@/pages/AIQuotation/types";

// 定义交互状态类型
export interface InteractionState {
  currentStep: number;
  completedSteps: number[];
  userInputs: Record<string, any>;
}

// 定义交互步骤类型
export interface InteractionStep {
  id: number;
  title: string;
  description: string;
  inputType:
    | "select"
    | "input"
    | "dateRange"
    | "numberInput"
    | "multiSelect"
    | "visualization";
  options?: Array<{ label: string; value: string | number }>;
  required?: boolean;
  key: string;
  visualizationType?: "flowchart" | "globe" | "comparison";
}

const SmartQuotation3: React.FC = () => {
  // 交互状态
  const [interactionState, setInteractionState] = useState<InteractionState>({
    currentStep: 0,
    completedSteps: [],
    userInputs: {},
  });

  // 已保存的报价列表
  const [savedQuotations, setSavedQuotations] = useState<QuotationResult[]>([]);

  // 选中的报价
  const [selectedQuotation, setSelectedQuotation] =
    useState<QuotationResult | null>(null);

  // 报价详情模态框可见性
  const [quotationModalVisible, setQuotationModalVisible] = useState(false);

  // 获取用户信息和导航函数
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.user);

  // 检查用户权限
  const hasAccess = user?.useridentity === 0;

  // 交互步骤定义
  const interactionSteps: InteractionStep[] = [
    {
      id: 0,
      title: "欢迎使用智能报价系统",
      description:
        "我是您的AI助手，将引导您完成报价流程。请选择您需要的运输方式：",
      inputType: "select",
      options: [
        { label: "空运", value: "air" },
        { label: "海运", value: "sea" },
        { label: "铁路", value: "rail" },
      ],
      required: true,
      key: "transportType",
    },
    {
      id: 1,
      title: "起始地和目的地",
      description: "请选择货物的起始地和目的地：",
      inputType: "visualization",
      visualizationType: "globe",
      key: "route",
    },
    {
      id: 2,
      title: "货物信息",
      description: "请提供货物的基本信息：",
      inputType: "multiSelect",
      options: [
        { label: "普通货物", value: "general" },
        { label: "危险品", value: "dangerous" },
        { label: "易碎品", value: "fragile" },
        { label: "温控货物", value: "temperature" },
      ],
      required: true,
      key: "cargoType",
    },
    {
      id: 3,
      title: "货物重量和体积",
      description: "请输入货物的重量和体积：",
      inputType: "numberInput",
      required: true,
      key: "cargoDetails",
    },
    {
      id: 4,
      title: "运输时间要求",
      description: "请选择您期望的运输时间范围：",
      inputType: "dateRange",
      required: true,
      key: "timeRequirement",
    },
    {
      id: 5,
      title: "报价比较",
      description: "根据您提供的信息，我们为您找到了以下最佳报价选项：",
      inputType: "visualization",
      visualizationType: "comparison",
      key: "quotationComparison",
    },
  ];

  // 处理用户输入
  const handleUserInput = (key: string, value: any) => {
    setInteractionState((prev) => ({
      ...prev,
      userInputs: {
        ...prev.userInputs,
        [key]: value,
      },
    }));
  };

  // 进入下一步
  const handleNextStep = () => {
    const currentStepData = interactionSteps[interactionState.currentStep];

    // 检查当前步骤是否已完成
    if (
      currentStepData.required &&
      !interactionState.userInputs[currentStepData.key]
    ) {
      message.warning("请完成当前步骤再继续");
      return;
    }

    setInteractionState((prev) => ({
      ...prev,
      currentStep: prev.currentStep + 1,
      completedSteps: [...prev.completedSteps, prev.currentStep],
    }));
  };

  // 返回上一步
  const handlePrevStep = () => {
    setInteractionState((prev) => ({
      ...prev,
      currentStep: Math.max(0, prev.currentStep - 1),
    }));
  };

  // 模拟生成报价
  const handleGenerateQuotation = () => {
    // 模拟报价生成
    const mockQuotation: QuotationResult = {
      id: `quote-${Date.now()}`,
      airline: "南方航空",
      originPort: interactionState.userInputs.route?.origin || "上海浦东",
      destinationPort:
        interactionState.userInputs.route?.destination || "纽约肯尼迪",
      price: 5.8,
      currency: "USD/kg",
      transitTime: "5-7天",
      departureSchedule: ["周一", "周三", "周五"],
      validUntil: "2023-12-31",
      rating: 4.5,
      serviceLevel: "Premium",
      recommended: true,
    };

    setSelectedQuotation(mockQuotation);
    setQuotationModalVisible(true);
  };

  // 保存报价
  const handleSaveQuotation = () => {
    if (selectedQuotation) {
      // 检查是否已经保存过
      const isAlreadySaved = savedQuotations.some(
        (q) => q.id === selectedQuotation.id
      );

      if (!isAlreadySaved) {
        setSavedQuotations((prev) => [...prev, selectedQuotation]);
        message.success("报价已保存到我的报价列表");
      } else {
        message.info("该报价已在您的报价列表中");
      }

      setQuotationModalVisible(false);
    }
  };

  // 如果用户没有权限，显示无权限提示
  if (!hasAccess) {
    return <NoAccessResult />;
  }

  return (
    <div className="smart-quotation-container">
      <div className="smart-quotation-content">
        <div className="smart-interface-container">
          {/* 交互控制台 */}
          <CommandConsole
            interactionState={interactionState}
            interactionSteps={interactionSteps}
            onUserInput={handleUserInput}
            onNextStep={handleNextStep}
            onPrevStep={handlePrevStep}
            onGenerateQuotation={handleGenerateQuotation}
          />

          {/* 已保存报价列表 */}
          <SavedQuotations
            savedQuotations={savedQuotations}
            onSelectQuotation={(quotation) => {
              setSelectedQuotation(quotation);
              setQuotationModalVisible(true);
            }}
          />
        </div>
      </div>

      {/* 报价详情模态框 */}
      <QuotationDetailModal
        visible={quotationModalVisible}
        quotation={selectedQuotation}
        onClose={() => setQuotationModalVisible(false)}
        onSave={handleSaveQuotation}
      />
    </div>
  );
};

export default SmartQuotation3;
