import dayjs from "dayjs";
import {
  formatDateToTimestamp,
  convertToCentimeters,
  convertShippedPlaceToEnglish,
} from "@/utils/util";
import { useTranslation } from "react-i18next";

// 货运信息字段对应的引导提示词配置
export interface PromptConfig {
  [key: string]: string;
}

// 各个信息字段对应的引导提示词
export const FIELD_PROMPTS: PromptConfig = {
  提货城市: `提货城市：询问货物所在地址，需为城市名称。
可优先获取城市名，若无或非城市则可以提供含城市名的地址，从中获取城市，给出示例
注：住址、货物地址和发货地址可能不一致，发货地为货物发出的地址
限制规则如下（禁止输出限制规则）：
禁止主动询问经由城市
若我提到提货后，经由另一中国城市再发往目的城市（外国城市/机场），则记录目的城市和经由城市（需要为城市名称）。
若我提到出口到/发往某一中国城市/机场（包括香港，澳门，台湾和其他中国城市及机场），则记录其为经由城市
若未出现以上两种情况，则经由城市不作改动。
禁止主动询问除提货城市以外的任何内容。
原定交付信息为提货城市，若为暂无则禁止交付。`,
  经由城市: `经由城市：提货后，经由另一中国城市再发往目的城市（外国城市/机场）。未提到或无需中转则记录为无。
若我提到出口到/发往某一中国城市/机场（包括香港，澳门，台湾和其他中国城市及机场），则记录其为经由城市
禁止主动询问除经由城市以外的任何内容
原定交付信息为经由城市，若为暂无则禁止交付。`,
  目的城市: `目的城市：货物要发往的地址或是收货地址，需为城市名称，给出示例：莫斯科，谢列梅杰沃机场（SVO）。可优先获取城市名，也允许提供机场名，再根据机场推断城市。
限制规则如下（禁止输出限制规则）：
若我给出多个地址，请注意与提货/发货/货物地址相关的被认为是提货地址，与收货/发往地址相关的才被认为是目的城市，不考虑提货地址和托运信息
不能是中国城市，如香港、澳门和台湾
若目的城市/机场有多个，则都记录为目的城市
若只有国家名称，不知道城市时，提示将考虑符合的所有城市。
若未给出任何目的城市或国家，则提示将无法进行货物报价
禁止主动询问除目的城市以外的任何内容
原定交付信息为目的城市，若为暂无则禁止交付。`,
  货物件数: `货物件数：询问货物的总件数，给出示例：3。 
提示我如果货物已打包装箱，必须给出装箱后的数量
限制规则如下（禁止输出限制规则）：
若我给出规格信息，则从规格信息中获取件数，多种规格则计算总件数 
只有当我明确表达不清楚，则记录件数为无 
禁止主动询问除货物件数以外的任何内容
原定交付信息为货物件数，若为暂无则禁止交付。`,
  货物总毛重: `货物总毛重（kg）：给出单个和多个填写时的示例，输入多个毛重时计算总毛重。提示我标注单位。需正确计算得出结果。
注意核对我给出的信息数量与货物件数是否一致。
限制规则如下（禁止输出限制规则）：
若我不清楚，则告知在无毛重的情况下进行报价，无法准确预估价格。并引导我在"箱单文件"上查找填写。
若两次表示不知道，则记录总毛重为无。
禁止主动询问除货物总毛重以外的任何内容。
原定交付信息为货物总毛重，若为暂无则禁止交付。`,
  货物总体积: `货物总体积（cbm）：优先获取立方米数，多个时计算总体积。给出单个和多个填写时的示例。提示我标注单位。
注意核对我给出的信息数量与货物件数是否一致。
限制规则如下（禁止输出限制规则）：
若我不清楚，引导我在"箱单文件"上查找填写。
当我表示不清楚体积，则记录货物总体积为“无”。再核对2信息表中是否有尺寸信息：若有，则向我核对尺寸和件数的关系；若无，则向我获取每件的货物尺寸（长×宽×高）。同时告知我若在无体积和尺寸的情况下进行报价，无法准确预估价格。
若我表示不清楚尺寸，则记录尺寸为无。
禁止主动询问除货物总体积和货物尺寸以外的任何内容
若提供了体积数值，则原定交付信息为货物总体积，总体积若为暂无则禁止交付。
若未提供体积，则原定交付信息为货物总体积和所有货物的尺寸，其中货物总体积记录为“无”。禁止通过尺寸计算体积，总体积或尺寸若未为暂无则禁止交付。`,
  货物尺寸: `货物尺寸（L×W×H）：询问货物尺寸，提示我标注单位。给出单个和多个填写时的示例。
注意核对我给出的信息数量与货物件数是否一致，尺寸信息数量与件数不一致时禁止交付。
限制规则如下（禁止输出限制规则）：
若我不清楚，引导我在"箱单文件"上查找填写。
若两次表示不知道，则告知我由于航司有货物尺寸限制，可能无法选择部分航司。并记录尺寸为无。
禁止主动询问除货物尺寸以外的任何内容
原定交付信息为货物尺寸，若为暂无则禁止交付。`,
  包装形式: `包装形式：询问货物的包装是否为以下类别：纸箱、布卷、木箱、桶、麻袋，包装仅记录一种
限制规则如下（禁止输出限制规则）：
若都不是，则提示我输入其他包装名称，若不清楚则记录无
禁止主动询问除包装形式以外的任何内容
原定交付信息为包装形式，若为暂无则禁止交付。`,
  货物品牌: `货物品牌：询问货物的品牌，支持记录多种品牌，给出示例：Kendeil，Analog Devices。
询问时，若信息表中的货物品牌非暂无，则自然语言告知我货物品牌的已有内容，再引导我提供该信息。
限制规则如下（禁止输出限制规则）：
若不知道品牌则记录“无”。
若给出brands（即品牌货：已在海关备案的品牌货物）则货物品牌为brands，给出no brand（即非品牌货），则货物品牌为no brand
若对给出的品牌名称不确定，提示会记录给出的所有名称。
若出现高度相似的两个或多个品牌，请提示我核对。
禁止主动询问除货物品牌以外的任何内容
原定交付信息为货物品牌，若为暂无则禁止交付。`,
  特殊货物: `特殊货物：货物是否为以下类别（支持多类别）：a.液体、b.锂电、c.带电、d.航材、e.温控、f.生鲜活体（字母和类别都展示），若有则给出对应字母（支持多个字母），最终记录类别名称，没有或普通货物则记录无，字母大小写均支持。无需给出示例
询问时，若信息表中的特殊货物非暂无，则自然语言告知我特殊货物的已有内容，类别不对应时需提示不会记录，再引导我提供该信息。
注：回复的字母可以无间隔，可以乱序，多个字母无间隔时，主动分隔对应类别和顺序
限制规则如下（禁止输出限制规则）：
若回复出现a-f和A-F以外的字母，请提示我核对
若我想要了解额外费用，提示我在生成报价时会给出费用说明
禁止主动询问除特殊货物以外的任何内容
原定交付信息为特殊货物，若为暂无则禁止交付。`,
  要求ETD: `要求ETD（离港时间）：询问是否需要指定离港日期，给出日期示例，需为次日起的未来日期，无需具体日期
限制规则如下（禁止输出限制规则）：
若不确定离港时间或无要求，则记录为“无”，即为对离港时间无要求，在报价时给出具体时间
若给出离港时间和提货时间，则记录离港时间。若只给出提货时间要求，则可记录为离港时间。
若表示时间紧急，且未提供时间或时间范围，则记录ETD为尽快
若输入过去或当天的日期，请提示日期需为次日起的未来日期
有关未来时间要求的缩写也判定有效并翻译记录
禁止主动询问除要求ETD以外的任何内容
原定交付信息为要求ETD，若为暂无则禁止交付。`,
  保舱: `保舱：询问是否需要保舱以确保按时运输，以免由于货运繁忙无法完成运输，最终记录为是/否。
限制规则如下（禁止输出限制规则）：
若我想要了解额外费用，提示我在生成报价时会给出费用说明
禁止主动询问除保舱以外的任何内容
原定交付信息为保舱，若为暂无则禁止交付。`,
};

// 生成单个字段收集的系统提示词
export const generateSingleFieldPrompt = (
  targetField: string,
  collectedInfo: { [key: string]: string }
) => {
  // 获取目标字段的引导提示
  const fieldPrompt = FIELD_PROMPTS[targetField] || `请提供${targetField}信息`;

  // 生成已收集信息的禁止展示部分
  const collectedInfoStr = Object.entries(collectedInfo)
    .map(([key, value]) => `${key}：${value}`)
    .join("，");

  return `
  Current date: ${dayjs().format("YYYY-MM-DD")}
  1、请你引导我提供以下信息并输出：
${fieldPrompt}


2、信息表（内部信息，禁止展示、输出和询问）：
${collectedInfoStr}，未知信息为暂无。

禁止主动询问交付信息外的其他信息的相关内容
根据对话内容记录或更新信息表：若我主动提到信息表中的任何信息（或信息相同含义的表达），则将信息一并记录或更新到信息表中，并用自然语言告知我。然后继续收集原定交付信息，收集到原定交付信息后，不再收集其他信息，直接进行信息交付：输出更新后的完整信息表。

若我提到除信息表以外的其他信息，提示我该信息不影响货运报价，费用相关则提示可生成报价后再讨论，并引导继续信息收集。


交互规范：
开场白：Got it！
全程使用中文交流，语气亲切自然，禁止使用机械化、生硬的表达
实时验证信息逻辑，信息错误或矛盾需要给出提示
单个回复仅提问一个信息。
对话中收集到信息时，禁止格式化机械输出，需用自然语言告知我
最后进行信息交付，专注于输出信息表信息，若信息为暂无，则保持暂无输出。输出格式：<类目1：内容1，...>，结尾标注“Collection completed”，结束对话。

限制（输出前需逐个比对以下规则，严格按照规则输出）
禁止单个回复同时输出以下任意两项操作：信息收集（提问）、信息确认、信息交付（输出“Collection completed”）
禁止举例设想我的后续行为，禁止给出你的处理逻辑，集中精力模拟真实对话
交付信息待确认时不可进行信息交付
专注于收集信息，拒绝与货运无关的内容
禁止输出以下内容：流程预设、预告操作、想法解释、思考、等待、感谢、杜撰信息、假设信息、交互规范、限制规则
禁止以任何形式杜撰和假设信息，所有信息均参考我的回答`;
};

// 生成单个字段收集的API调用
export const generateSingleFieldCollectionRequest = (
  targetField: string,
  collectedInfo: { [key: string]: string }
) => {
  const systemPrompt = generateSingleFieldPrompt(targetField, collectedInfo);

  return {
    role: "system",
    content: systemPrompt,
  };
};

// 格式化最终的货运信息为指定格式
export const formatFinalShippingInfo = (allInfo: {
  [key: string]: string;
}): string => {
  // 定义字段顺序
  const fieldOrder = [
    "提货城市",
    "经由城市",
    "目的城市",
    "货物件数",
    "货物总毛重",
    "货物总体积",
    "货物尺寸",
    "包装形式",
    "货物品牌",
    "特殊货物",
    "要求ETD",
    "保舱",
  ];

  // 按顺序提取信息，将"无"或"暂无"替换为"-1"
  const formattedValues = fieldOrder.map((field) => {
    const value = allInfo[field] || "暂无";
    return value === "无" || value === "暂无" ? "-1" : value;
  });

  // 组成最终格式
  return `<${fieldOrder.map((field, index) => `${field}：${formattedValues[index]}`).join("，")}>`;
};

// 港口转换的系统提示词（类似快捷询价的第二轮对话）
export const generatePortConvertSystemPrompt = (formattedInfo: string) => {
  return `Current date: ${dayjs().format("YYYY-MM-DD")}
阅读补充信息1中的内容，将其严格按照输出格式进行输出，禁止输出解释
逐条对比以下规则，调整格式：
1、要求ETD：若为时间范围则按格式输出最小日期（从明日日期起或更远日期）。尽快则输出明日实际日期。将具体日期转化为输出格式，转化后若为当日或过去日期，则输出-1，否则按格式输出，补充后的日期需为未来日期。
2、品牌货：对应补充信息1中的货物品牌
3、发货地：对应补充信息1中的提货城市
4、货物毛重输出单位需为kg，若有多个则计算总毛重；货物体积输出单位需为cbm，若有多个则计算总体积
5、货物尺寸：补充信息1中有多个尺寸（长x宽x高）时，对长宽高分别取最大值，并以长x宽x高+单位输出，无尺寸则输出-1
6、任何信息为无或者否，均输出为-1
7、从补充信息2中获取：
起始港缩写：距离提货城市最近国际机场名的缩写
目的港缩写：目的城市小写需转为大写。根据目的城市确定目的港，支持记录多个目的港

补充信息1：
${formattedInfo}

补充信息2：
目的港：
莫斯科对应的目的港为SVO
圣彼得堡对应的目的港为LED
其他城市对应的目的港为SVO/LED
起始港：
CGO：郑州新郑国际机场
HAK：海口美兰国际机场
HGH：杭州萧山国际机场
HRB：哈尔滨太平国际机场
PEK：北京首都国际机场
PKX：北京大兴国际机场
PVG：上海浦东国际机场
TAO：青岛胶东国际机场
URC：乌鲁木齐地窝堡国际机场
XIY：西安咸阳国际机场
CAN：广州白云国际机场
HKG：香港国际机场
SZX：深圳宝安国际机场
TFU：成都天府国际机场
CKG：重庆江北国际机场
SHE：沈阳桃仙国际机场
输出格式:
<起始港：SZX
目的港：SVO/LED
货物件数：
货物毛重：220kg
货物体积：1.03cbm
货物尺寸：20x30x40cm
包装形式：
品牌货：
特殊货物：
发货地：深圳
要求ETD：2025-06-08
保舱：>
信息无效的类目内容输出为-1`;
};

// 计算多个尺寸的总体积（立方米）
const calculateVolumeFromSizes = (sizesString: string): number => {
  if (!sizesString || sizesString === "-1") {
    return 0;
  }

  try {
    // 按逗号分割多个尺寸
    const sizes = sizesString
      .split(",")
      .map((size) => size.trim())
      .filter((size) => size);
    let totalVolume = 0;

    sizes.forEach((sizeStr) => {
      // 匹配尺寸和单位，支持多种格式：20x30x40cm, 20*30*40 inches等
      const sizeMatch = sizeStr.match(
        /(\d+(?:\.\d+)?)\s*[x*×]\s*(\d+(?:\.\d+)?)\s*[x*×]\s*(\d+(?:\.\d+)?)(?:\s*([a-zA-Z"']+|厘米|毫米|米|英寸|英尺|码))?/i
      );

      if (sizeMatch) {
        const length = parseFloat(sizeMatch[1]);
        const width = parseFloat(sizeMatch[2]);
        const height = parseFloat(sizeMatch[3]);
        const unit = sizeMatch[4] || "cm"; // 默认单位为厘米

        // 将所有尺寸转换为厘米
        const lengthCm = convertToCentimeters(length, unit);
        const widthCm = convertToCentimeters(width, unit);
        const heightCm = convertToCentimeters(height, unit);

        // 计算体积（立方厘米）
        const volumeCm3 = lengthCm * widthCm * heightCm;

        // 转换为立方米（1立方米 = 1,000,000立方厘米）
        const volumeM3 = volumeCm3 / 1000000;

        totalVolume += volumeM3;

        console.log(
          `尺寸体积计算: ${sizeStr} -> ${lengthCm}x${widthCm}x${heightCm}cm = ${volumeCm3}cm³ = ${volumeM3}m³`
        );
      }
    });

    console.log(`总体积计算: ${totalVolume}m³`);
    return Math.round(totalVolume * 1000) / 1000; // 保留6位小数
  } catch (error) {
    console.error("计算尺寸体积时出错:", error);
    return 0;
  }
};

// 定义转换后的数据结构
export interface ConvertedShippingData {
  originport?: string; // 起始港
  unloadingport?: string | string[]; // 目的港
  goodsnumber?: string; // 货物件数
  packagetype?: string; // 包装形式
  grossweight?: number; // 货物毛重（数字）
  goodsvolume?: number; // 货物体积（数字）
  cargolength?: number; //货物尺寸
  cargowidth?: number;
  cargoheight?: number;
  isbrand?: boolean; // 品牌货
  specialcargo?: string | string[]; // 特殊货物
  shippedplace?: string; // 发货地
  isvalidity?: boolean; //是否要求ETD
  shipmentdate?: string; // 要求ETD日期
  ensurecabin?: boolean; // 保舱
  calculatesizes?: string; // 计算尺寸字符串，如：19x37x27cm,40x40x21cm
}

// 解析港口转换AI返回的结果
export const parsePortConvertResponse = (
  aiResponse: string
): ConvertedShippingData => {
  try {
    // 查找<>包围的内容
    const match = aiResponse.match(/<([^>]+)>/);
    if (!match) {
      throw new Error("港口转换失败");
    }

    const content = match[1];
    const lines = content.split("\n").filter((line) => line.trim());

    const result: ConvertedShippingData = {};

    lines.forEach((line) => {
      const [key, value] = line.split("：");
      if (key && value !== undefined) {
        const trimmedValue = value.trim();

        switch (key.trim()) {
          case "起始港":
            result.originport = trimmedValue === "-1" ? "" : trimmedValue;
            break;
          case "目的港":
            if (trimmedValue === "-1") {
              result.unloadingport = "";
            } else {
              // 处理多个目的港的情况，如 "SVO/LED"
              const ports = trimmedValue
                .split("/")
                .map((port) => port.trim())
                .filter((port) => port);
              result.unloadingport = ports.length > 1 ? ports : trimmedValue;
            }
            break;
          case "货物件数":
            result.goodsnumber = trimmedValue === "-1" ? "" : trimmedValue;
            break;
          case "包装形式":
            result.packagetype = trimmedValue === "-1" ? "" : trimmedValue;
            break;
          case "货物毛重":
            if (trimmedValue !== "-1") {
              const weightMatch = trimmedValue.match(/(\d+(?:\.\d+)?)/);
              result.grossweight = weightMatch
                ? parseFloat(weightMatch[1])
                : undefined;
            }
            break;
          case "货物体积":
            if (trimmedValue !== "-1") {
              const volumeMatch = trimmedValue.match(/(\d+(?:\.\d+)?)/);
              result.goodsvolume = volumeMatch
                ? parseFloat(volumeMatch[1])
                : undefined;
            }
            break;
          case "货物尺寸":
            if (trimmedValue !== "-1") {
              // 匹配尺寸和单位，支持多种格式：20x30x40cm, 20*30*40 inches, 20 x 30 x 40 mm等
              const sizeMatch = trimmedValue.match(
                /(\d+(?:\.\d+)?)\s*[x*×]\s*(\d+(?:\.\d+)?)\s*[x*×]\s*(\d+(?:\.\d+)?)(?:\s*([a-zA-Z"']+|厘米|毫米|米|英寸|英尺|码))?/i
              );
              if (sizeMatch) {
                const length = parseFloat(sizeMatch[1]);
                const width = parseFloat(sizeMatch[2]);
                const height = parseFloat(sizeMatch[3]);
                const unit = sizeMatch[4] || "cm"; // 默认单位为厘米

                // 使用单位换算函数将所有尺寸转换为厘米
                result.cargolength = convertToCentimeters(length, unit);
                result.cargowidth = convertToCentimeters(width, unit);
                result.cargoheight = convertToCentimeters(height, unit);

                console.log(
                  `尺寸单位换算: ${length}x${width}x${height}${unit} -> ${result.cargolength}x${result.cargowidth}x${result.cargoheight}cm`
                );
              }
            }
            break;
          case "计算尺寸":
            result.calculatesizes = trimmedValue === "-1" ? "" : trimmedValue;
            break;
          case "品牌货":
            result.isbrand = trimmedValue !== "-1";
            break;
          case "特殊货物":
            if (trimmedValue === "-1" || trimmedValue === "无") {
              result.specialcargo = undefined;
            } else {
              // 处理多个特殊货物的情况，如 "SVO/LED"
              const specialcargo = trimmedValue
                .split(",")
                .map((item) => item.trim())
                .filter((item) => item);
              result.specialcargo =
                specialcargo.length > 1 ? specialcargo : trimmedValue;
            }
            break;
          case "发货地":
            if (trimmedValue === "-1") {
              result.shippedplace = "";
            } else {
              // 转换发货地为英文
              result.shippedplace = convertShippedPlaceToEnglish(trimmedValue);
            }
            break;
          case "要求ETD":
            if (trimmedValue !== "-1") {
              result.isvalidity = true;
              // 解析日期并设置到shipmentdate字段
              result.shipmentdate = dayjs(trimmedValue).format("YYYY-MM-DD");
            } else {
              result.isvalidity = false;
            }
            break;
          case "保舱":
            result.ensurecabin =
              trimmedValue !== "-1" &&
              trimmedValue !== "无" &&
              trimmedValue !== "否";
            break;
        }
      }
    });

    // 如果货物体积为-1且有计算尺寸，则使用计算尺寸计算体积
    if (result.goodsvolume === undefined && result.calculatesizes) {
      const calculatedVolume = calculateVolumeFromSizes(result.calculatesizes);
      if (calculatedVolume > 0) {
        result.goodsvolume = calculatedVolume;
        console.log(`使用计算尺寸计算出体积: ${calculatedVolume}m³`);
      }
    }

    return result;
  } catch (error) {
    console.error("解析港口转换结果失败:", error);
    throw error;
  }
};
