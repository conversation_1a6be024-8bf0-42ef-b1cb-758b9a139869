import React, { useState } from "react";
import { createPortal } from "react-dom";
import { Button, Modal, Input, message, Space, Progress } from "antd";
import { RobotOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import "./index.less";
import { chatCompletions } from "@/services/secondaryService";

const { TextArea } = Input;

interface AiCabinExtractorProps {
  onExtractSuccess: (extractedData: any[]) => void;
  buttonText?: string;
  buttonType?: "primary" | "default" | "dashed" | "link" | "text";
  buttonSize?: "small" | "middle" | "large";
  disabled?: boolean;
}

const AiCabinExtractor: React.FC<AiCabinExtractorProps> = ({
  onExtractSuccess,
  buttonText,
  buttonType = "dashed",
  buttonSize = "middle",
  disabled = false,
}) => {
  const { t } = useTranslation();
  const [visible, setVisible] = useState(false);
  const [inputText, setInputText] = useState("");
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(0);

  const EXTRACT_SYSTEM_PROMPT = `Current date: ${dayjs().format("YYYY-MM-DD")}
一、请你从我输入的信息中提取舱报信息，严格按照以下次序进行提取：
日期->单询->重量->重量术语->体积->价格变化->特价找货->整体密度（下限和上限）->限散件
注：
先提取日期和单询，再从日期后的文字中依次提取其他信息，提取的信息需与日期对应。
同个日期有多条记录，且其对应的其他信息有冲突时，则拆分为多条记录；对应信息不冲突，则合并为一条记录
价格变化：形式为表价变化+表价密度（数值、比例、货型），如"表-2收1:300以上"，-2为表价变化，1:300为表价密度；如"平货表价-2/KG"，-2为表价变化，平货为表价密度。表价密度仅影响pricechanges字段
特价找货：形式为特价+特价密度（数值、比例、货型），如"特价11元收泡货"，11元为特价，泡货为特价密度。如"特价收重货"，重货为特价密度。特价密度仅影响specialprice字段
整体密度（下限和上限）：形式为密度数值、比例或货型描述。需从独立密度描述（即非表价密度，且非特价密度）中提取。表价密度、特价密度、和整体密度均为独立字段，需分别提取。
整体密度（货型）与体积、重量为独立字段，需分别提取。
未提取到任何有效信息时，按格式输出（缺失内容保持为空或默认值）。


二、请对每个日期及其对应信息进行格式调整。调整时请严格遵循以下规则，逐条核对并按格式输出：
1.始发日期date：输出格式：x月x日。
1.1每个日期都需按照以下规则进行调整。
1.1.1无月份时，需根据当前日期补充月份为该月或下一月份，补充后的日期需为未来日期。
1.1.2即使只有日期，无其他任何有效信息也需记录为 x月x日，按格式输出（缺失内容保持为空或默认值）。日期必须为具体日期（非范围）。
1.1.3D后的数值为星期数，不记录。

1.2特殊情况处理（始发日期）
1.2.1对于日期1起，则记录日期1后需标注"起"。
1.2.2对于日期1/日期2起，则只记录"/"之前的日期1，日期2和"起"不记录。如"26/3起"，则只记录"x月26日"。
1.2.3对于日期1/日期2，则需拆分成两条记录，分别记录日期1和日期2。如"26/4，缺泡"，需拆分成两条记录："26号缺泡"，"4号缺泡"。
1.2.4对于某日期出现"收满"、"封舱（封仓）"或"不收货"，且无其他有效信息（或有其他信息但要候补订位或加价优先运输），则需删去此日期记录。有其他有效信息（且不用候补订位）时，仍需记录该日期。

1.3中转日期transferdate：输出格式：x月x日。
1.3.1通常情况下，日期均为始发日期。中转日期可以没有，默认为空。
1.3.2对于日期1接日期2（起），表示一种航班中转情况。需记录日期1为始发日期，记录日期2为中转日期。均不记录起。如"1接2号起"，则记录始发日期为"x月1日"，记录中转日期为"x月2日"；
1.3.3对于日期1/日期2接日期3，表示多日期始发的航班中转。需拆分成两条，再分别记录始发日期和中转日期。如"25/27接3号"，先拆分成2条记录："25接3号"，"27接3号"，按规则记录第一条：始发日期为"x月25日"，中转日期为"x月3日"；并记录第二条：始发日期为"x月27日"，中转日期为"x月3日"。
1.3.4对于"头程"和"二程"，则"头程"后的日期记录为始发日期，"二程"后的日期需记录为中转日期。


2.单询singleinquiry：默认为false
仅当出现明确单询信息（如单问、可问、可议、可谈等），则记录为true。
没有日期只有单询时，日期记录为空，单询记录为true。
若日期和单询均没有，则按格式输出（缺失内容保持为空或默认值）。
随订、随定、可订、可定、畅收等信息指可以预定舱位，不受任何限制，不表示单询，记录为false。

3.重量weightlimit（kg）：默认为99999，需换算单位为kg（T或t表示吨）。
对于重量范围，则记录最大重量。

4.重量术语weightterminology：默认为空。
重量术语优先级："M/N" < "Q45" /"小货"< "Q100" < "Q300" < "Q500" < "Q1000"（数值越大优先级越高）。
同日期出现多术语时仅记录最高级术语。如"Q45/Q100/Q300"，记录重量为"Q300"，不可省略"Q"。
当最高级术语为"小货"时，记录"Q45"。
"小货"是专有名词，只有以"小货"形式出现才记录；"小泡"、"小泡货"等复合词，不表示小货，只表示泡货，不记录"Q45"。

5.体积volumelimit（cbm）：默认99999。 
其他情况：如“392/5”可表示重量392kg，体积5cbm。

6.价格变化pricechanges： 输出格式：[表价密度下限,下一区间的下限]:变化值。如"-4收1:500以上，-6收1:1000以上，表价备装其他货型特货"，则记录为"[0,500]:0,[500,1000]:-4,[1000:99999]:-6"。
只有表价密度，没有表价变化值，则表价记录为0。如"表价收重货"，则记录为"[500,800]:0"。
只有表价变化，没有表价密度（数值、比例、货型）时，该表价密度区间记录为默认上下限[0,99999]。
表价密度仅记录在pricechanges字段中，不影响整体密度。

7.特价找货specialprice：输出格式：[特价密度下限,下一区间的下限]:具体价格。
只有特价密度，没有具体价格，则记录特价价格为-1。如"特价收泡货"，则记录为"[0,100]:-1"。
只有特价价格，没有特价密度时，该特价密度区间记录为默认上下限[0,99999]。
特价密度仅记录在specialprice字段中，不影响整体密度。

8.整体密度（规则1和规则2必须均满足才记录密度上限upperdensity和下限lowerdensity）：
8.1必须满足的两个规则
规则1：需为独立密度描述：即非价格变化中的表价密度，且非特价找货中的特价密度）。如"收小泡"，并非表价密度和特价密度，即"泡"为独立密度描述。
规则2：
规则2-1：只要密度描述（包括数值、比例、任何货型描述）被“优先”、“主收”等非绝对词修饰，就不记录密度上下限（保持默认 [0,99999]）。
示例：
"主要收1>300以上泡货"：表示"主要收"同时修饰"1>300"和"泡货"，则两者均不记录，密度上下限保持[0,99999]；
"1>300散件优先"：表示"优先"同时修饰"1>300"和"散件"，则两者均不记录，密度上下限保持 [0,99999]。
规则2-2：当同时存在非绝对词和绝对词（如"只收"、"限"等）修饰密度描述时，则忽略非绝对词的影响，只记录绝对词修饰的对应密度（数值、比例、货型）。（仍需满足规则1的独立性要求）

8.2仅对于满足以上两个规则的独立密度描述（无优先/主收等非绝对词修饰），才根据以下逻辑（8.2.1~8.2.4）进行记录：
8.2.1若同时存在独立的密度数值（比例）和货型描述，且均无优先/主收等非绝对词修饰，则优先记录密度数值或比例（如1:500 记录下限500）；给出多个密度数值时记录密度下限为最小数值（其他数值不记录），如1：500/800，则记录[500,99999]
比例为1:下限，1>下限等类似形式，符号差异不影响下限数值记录。
8.2.2若密度数值（比例）和货型描述中仅一方有优先/主收等修饰词，则采用无修饰词的描述记录密度。
8.2.3若无密度数值或比例，则按货型描述匹配预设密度范围（如"缺重货" 记录 [500,800]）；
8.2.4若两者均无，则保持默认值 [0,99999]。
货型描述如下：
平重（货）：不表示平货或重货，记录密度为[0,99999]
泡（货）：记录密度下限为0，密度上限为100
重（货），记录密度下限为500，密度上限为800
极重/超重（货）：记录密度下限为1000，密度上限为99999
平抛（货）：记录密度下限为130，密度上限为200
平（货）：记录密度下限为167，密度上限为99999
平泡（货）：记录密度下限为130、密度上限为200

9.限散件onlyparts：默认为false。
9.1对于"只收"、"仅限"、"专收"等绝对限制词，且搭配"散件"、"散货"、"散箱"、"纸箱"等关键词时，记录为true，如"只收散件货"。
9.2对于无任何限制词修饰散件时，记录为true，如“散件”。
9.3其他情况（包括"散件优先"、"主收散货"等表述）均记录为false。
9.4特殊情况：如"主收散件只收重货"，此时需拆分成"主收散件，只收重货"。"主收"搭配"散件"，则限散件仍记录为false；"只收"搭配"重货"（其他货型描述），绝对限制+货型需记录为整体密度。


三、输出JSON格式（缺失信息保持为空），禁止输出除JSON外的任何内容
示例如下：
{
  "date": 6月27日,
  "transferdate": 6月28日，
  "weightlimit": 99999,
  "weightterminology": Q300,
  "volumelimit": 99999,
  "lowerdensity": 0,
  "upperdensity": 99999,
  "onlyparts": false,
  "pricechanges": [
    {
      "leftdensity": 300, "rightdensity": 500, "pchanges": -5
    },{...}
  ],
  "specialprice": [
    {
      "densitylowerlimit": 300, "densityupperlimit": 99999, "sprice": 10
    },{...}
  ],
  "singleinquiry": false,
},{...}`;

  // 模拟进度条函数
  const startProgressSimulation = () => {
    const interval = setInterval(() => {
      setProgress((prev) => {
        const rand = prev >= 90 ? 2 : prev >= 70 ? 4 : prev >= 30 ? 6 : 8;
        return Math.min(prev + Math.random() * rand, 99);
      });
    }, 500);
    return () => clearInterval(interval);
  };

  // 日期转换公共函数
  const convertDateField = (dateValue: any, serverTimestamp?: number) => {
    if (!dateValue) return { date: null, hasWarning: false };

    const currentYear = dayjs().year();
    const match = dateValue.toString().match(/(\d+)月(\d+)日/);

    let convertedDate;
    if (match) {
      const [, month, day] = match;
      convertedDate = dayjs(
        `${currentYear}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`
      );
    } else {
      convertedDate = dayjs(dateValue);
    }

    if (!convertedDate.isValid()) {
      console.warn(`无效日期: ${dateValue}`);
      return { date: dayjs(), hasWarning: false }; // fallback
    }

    // 日期范围验证：从第二天到半个月内
    let hasWarning = false;
    if (serverTimestamp) {
      const serverTime = dayjs(serverTimestamp);
      const tomorrow = serverTime.add(1, "day").startOf("day");
      const halfMonthLater = serverTime.add(15, "day").endOf("day");

      if (
        convertedDate.isBefore(tomorrow) ||
        convertedDate.isAfter(halfMonthLater)
      ) {
        console.warn(`日期超出有效范围: ${convertedDate.format("YYYY-MM-DD")}`);
        hasWarning = true;
        return { date: null, hasWarning }; // 清空无效日期
      }
    }

    return { date: convertedDate, hasWarning };
  };

  // 单个 item 格式转换逻辑
  const normalizeItem = (item: any, serverTimestamp?: number) => {
    let hasDateWarning = false;

    // 处理主日期
    if (item.date) {
      const result = convertDateField(item.date, serverTimestamp);
      if (result) {
        item.date = result.date;
        if (result.hasWarning) {
          hasDateWarning = true;
        }
      }
    }

    // 处理中转日期
    if (item.transferdate) {
      const result = convertDateField(item.transferdate, serverTimestamp);
      if (result) {
        item.transferdate = result.date;
        if (result.hasWarning) {
          hasDateWarning = true;
        }
      }
    }

    // 设置警告标记
    if (hasDateWarning) {
      item._dateWarning = true;
    }

    // 重量限制处理
    if (item.weightlimit === 99999 && item.weightterminology) {
      const map: Record<string, number> = {
        "M/N": 45,
        Q45: 100,
        Q100: 300,
        Q300: 500,
        Q500: 1000,
        Q1000: 99999,
      };
      item.weightlimit = map[item.weightterminology] ?? item.weightlimit;
    }

    // 过滤pricechanges中pchanges为0的项
    if (item.pricechanges && Array.isArray(item.pricechanges)) {
      item.pricechanges = item.pricechanges.filter(
        (pc: any) => pc.pchanges !== 0
      );
    }

    // 过滤specialprice中sprice为-1的项
    if (item.specialprice && Array.isArray(item.specialprice)) {
      item.specialprice = item.specialprice.filter(
        (sp: any) => sp.sprice !== -1
      );
    }

    return item;
  };

  // AI 响应解析函数
  const parseAiResponse = (content: string, serverTimestamp?: number) => {
    const jsonStr = content.substring(
      content.indexOf("{"),
      content.lastIndexOf("}") + 1
    );
    const fixedJson = `[${jsonStr}]`;

    let data: any[] = [];
    try {
      data = JSON.parse(fixedJson);
    } catch {
      throw new Error("没有解析出相关内容");
    }

    const valid = data.filter((item) => item.date && item.date !== "");

    if (
      !valid.length ||
      (valid.length === 1 && Object.keys(valid[0]).length === 0)
    ) {
      throw new Error("没有解析出相关内容");
    }

    console.log("有效数据:", valid);

    return valid.map((item) => normalizeItem(item, serverTimestamp));
  };

  const handleExtract = async () => {
    if (!inputText.trim()) {
      message.warning(t("aiCabinExtractor.messages.inputRequired"));
      return;
    }

    setLoading(true);
    setProgress(0);

    const stopProgress = startProgressSimulation();

    try {
      const AIMessages = [
        { role: "system", content: EXTRACT_SYSTEM_PROMPT },
        { role: "user", content: inputText },
      ];

      const res = await chatCompletions(AIMessages);
      const content = res?.data?.data?.body?.choices?.[0]?.message?.content;
      const serverTimestamp = (res?.data as any)?.systimestamp;
      console.log("AI返回内容:", content);

      if (!content) {
        throw new Error(t("aiCabinExtractor.errors.aiResponseError"));
      }

      const result = parseAiResponse(content, serverTimestamp);

      if (!result.length) {
        throw new Error("没有解析出相关内容");
      }

      // 检查是否有日期问题需要提示用户
      const hasDateWarning = result.some((item) => item._dateWarning);
      if (hasDateWarning) {
        message.warning(
          "提取到的部分日期超出有效范围（明天到半个月内），请手动输入正确日期",
          5
        );
      }

      stopProgress();
      setProgress(100);
      await new Promise((resolve) => setTimeout(resolve, 300));

      onExtractSuccess(result);
      message.success(t("aiCabinExtractor.messages.extractSuccess"));
      setVisible(false);
      setInputText("");
    } catch (error) {
      console.error("AI提取失败:", error);

      const msg =
        error instanceof Error && error.message === "没有解析出相关内容"
          ? "没有解析出相关内容，请检查输入的文本是否包含有效的舱报信息"
          : t("aiCabinExtractor.messages.extractFailed");

      message.error(msg);
    } finally {
      setLoading(false);
      setProgress(0);
    }
  };
  const handleCancel = () => {
    setVisible(false);
    setInputText("");
    setProgress(0);
  };

  return (
    <>
      <Button
        type={buttonType}
        size={buttonSize}
        icon={<RobotOutlined />}
        onClick={() => setVisible(true)}
        disabled={disabled}
        className="ai-cabin-extractor-button"
      >
        {buttonText || t("aiCabinExtractor.buttonText")}
      </Button>

      {/* 进度遮罩 */}
      {loading &&
        createPortal(
          <div className="ai-extraction-overlay">
            <div className="ai-extraction-content">
              <div className="extraction-icon">
                <RobotOutlined />
              </div>
              <div className="extraction-title">正在提取舱报信息</div>
              <div className="extraction-subtitle">
                正在分析您的文本内容，请稍候...
              </div>
              <Progress
                percent={Math.round(progress)}
                status="active"
                strokeColor="#1797e1"
                showInfo={true}
                strokeWidth={8}
                className="extraction-progress"
              />
            </div>
          </div>,
          document.body
        )}

      <Modal
        title={t("aiCabinExtractor.modalTitle")}
        open={visible}
        onCancel={handleCancel}
        width={600}
        centered
        className="ai-cabin-extractor-modal"
        footer={
          <Space>
            <Button onClick={handleCancel}>{t("common.cancel")}</Button>
            <Button
              type="primary"
              loading={loading}
              onClick={handleExtract}
              icon={<RobotOutlined />}
            >
              {t("aiCabinExtractor.extractButton")}
            </Button>
          </Space>
        }
        destroyOnClose
      >
        <div className="ai-cabin-extractor-content">
          <div className="input-section">
            <div className="input-label">
              {t("aiCabinExtractor.inputLabel")}
            </div>
            <TextArea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder={t("aiCabinExtractor.inputPlaceholder")}
              rows={8}
              autoSize={{ minRows: 8, maxRows: 10 }}
              maxLength={2000}
              showCount
              className="input-textarea"
              disabled={loading}
            />
          </div>

          <div className="tips-section">
            <div className="tips-title">{t("aiCabinExtractor.tipsTitle")}</div>
            <ul className="tips-list">
              <li>{t("aiCabinExtractor.tips.tip1")}</li>
              <li>{t("aiCabinExtractor.tips.tip2")}</li>
              <li>{t("aiCabinExtractor.tips.tip3")}</li>
              <li>{t("aiCabinExtractor.tips.tip4")}</li>
            </ul>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default AiCabinExtractor;
