import { post } from "@/utils/request";
import dayjs from "dayjs";

// 检查DeepSeek API认证状态
// const ensureDeepSeekAuth = async () => {
//   if (!hasDeepSeekApiKey()) {
//     throw new Error("DeepSeek API Key未设置，请先设置API Key");
//   }
// };

// DeepSeek API 相关配置
const DEEPSEEK_DEFAULT_CONFIG = {
  model: "deepseek-chat",
  // frequency_penalty: 0,
  // max_tokens: 2048,
  // presence_penalty: 0,
  // response_format: {
  //   type: "text",
  // },
  stream: false,
  temperature: 0.25,
  top_p: 1,
};

// 系统提示词
const SYSTEM_PROMPT = `
Current date: ${dayjs().format("YYYY-MM-DD")}
你是一个货运信息填报助手，需要从我给出的回答中提取有效的货运信息。请遵循以下规则：
任务：
1.引导我分享与货运有关的信息，若我第一次回答未提供任何有效货运信息，则给出填写范例，引导我给出货运信息，禁止按格式输出。
2.判断我输入的文字中是否含有以下有效信息：
提货城市：与提货/发货/货物地址相关的才被认为是提货城市，仅获取城市名称。
经由城市：默认为“无”，若出现经由/出口到某一中国城市/机场（包括香港，澳门，台湾和其他中国城市及机场），则记录对应城市/机场。
目的城市：城市名和机场名均可，可多个，不能是中国城市及机场，如香港、澳门、台湾和中国的其他城市，无需考虑托运信息。
货物件数：直接记录。
货物总毛重、货物总体积、货物尺寸：货物总毛重、货物总体积、货物尺寸这三个信息在只有数字无单位时需要添加默认单位并提示我进行核实。
包装形式：可以根据件数后的包装来判断：纸箱、木箱、布卷、麻袋、桶、托盘，"boxes"和"packages"均记录为纸箱。
货物品牌："no brand"和"brands"均判定为有效。
特殊货物：仅6类：液体、锂电、带电、航材、温控、生鲜活体，可记录多类。只有明确标注"general cargo"时记录为无，其他所有情况均记录为“暂无”。若我提到笼统的包含特殊货物的概念词，你必须进一步问清楚具体类别才能记录，在没获取到具体类别时均记录为“暂无”。
要求ETD：无需为具体日期；当我输入具体日期，核对是否过期，当日或已过期均判定为无效，记录为暂无；无要求或需提供预计时间也判定无效，记录为暂无；有正确时间或时间范围，则记录具体日期或范围。提货时间也可作为ETD；时间紧急记录为尽快；相关缩写也判定有效并翻译记录。
是否保舱：直接记录是/否。
注：信息收集时，若我提到除以上信息外的其他信息，提示我该信息不影响货运报价，费用相关则提示可生成报价后再讨论。
3.其他规则：多运单时，有多件货物则计算总件数，有多个毛重则求和计算总毛重，有多个体积则求和计算总体积，其他信息若有多个，则均记录。禁止通过尺寸计算总体积。
4.收集到有效信息后，禁止按格式机械输出已收集的信息，需以自然语言总结信息输出。
5.提示我对输出的信息进行核对，告知我如果有需要还可以进行修改或补充（但无需给出具体示例，无需细化补充的内容），直至我表示没有其他补充和修改，再进入最后的交付步骤。
交付步骤：
当我确认无补充后，回复：
“Collection completed”并按统一格式输出（所有信息默认"暂无"，仅修改有效信息）：
<提货城市：，经由城市：，目的城市：，货物件数：，货物总毛重：，货物总体积：，货物尺寸：，包装形式：，货物品牌：，特殊货物：，要求ETD：，保舱：>
输出规范：毛重 → 数字+kg；体积 → 数字+cbm；尺寸 → 长x宽x高+单位，

交互规范：
开场白：你好，我是你的货运助手。开场白后用一句话进行简单引导
全程使用中文和我进行交流。语气亲切自然，禁止机械化、生硬的表达
单个回复仅给出一个提问。
使用自然亲切的语气，主动引导我一次性提供所知信息。
实时验证信息逻辑，检测矛盾，回答错误或矛盾需要给出提示。


限制
首次对话仅输出开场白，禁止输出其他内容。
禁止举例设想我的后续行为，禁止给出你的处理逻辑，集中精力模拟真实对话。
仅处理货运信息相关对话，拒绝无关内容。
禁止输出：流程预设、预告操作、想法解释、思考、等待、感谢、杜撰信息、假设信息。
禁止针对某一条具体信息提问。
禁止杜撰任何信息，所有信息均参考我的回答。`;

// 定义消息内容类型
type MessageContent = string | Array<{ type: string; text: string }>;

// DeepSeek聊天完成接口
export const chatCompletions = async (
  messages: Array<{ role: string; content: MessageContent }>
) => {
  // await ensureDeepSeekAuth();

  const requestData = {
    ...DEEPSEEK_DEFAULT_CONFIG,
    messages,
  };

  return post("/chat/completions", requestData);
};

// 初始化AI助手对话（获取欢迎信息）
export const initializeAIAssistant = async () => {
  const initialMessages = [
    {
      role: "user",
      content: SYSTEM_PROMPT,
    },
  ];

  return chatCompletions(initialMessages);
};

// 发送用户消息并获取AI回复
export const sendMessageToAI = async (
  userMessage: string,
  conversationHistory: Array<{ role: string; content: MessageContent }> = [],
  lastRequestMessages?: Array<{ role: string; content: MessageContent }>,
  lastAIResponse?: string
) => {
  // 判断是否为第一次用户对话
  // 只有当没有lastRequestMessages且conversationHistory中没有用户消息时，才是第一次对话
  const userMessages = conversationHistory.filter((msg) => msg.role === "user");
  const isFirstUserMessage = !lastRequestMessages && userMessages.length === 0;

  let messages;

  if (isFirstUserMessage) {
    // 从对话历史中获取AI的初始化消息
    const aiWelcomeMessage = conversationHistory.find(
      (msg) => msg.role === "assistant"
    );
    const welcomeText =
      aiWelcomeMessage && typeof aiWelcomeMessage.content === "string"
        ? aiWelcomeMessage.content
        : "你好，我是你的货运助手。可以告诉我您的货物需要从哪里运到哪里呢？";

    messages = [
      {
        role: "system",
        content: `Current date: ${dayjs().format("YYYY-MM-DD")}\n${SYSTEM_PROMPT}`,
      },
      {
        role: "user",
        content: [
          {
            type: "text",
            text: `> ${welcomeText}\n`,
          },
          {
            type: "text",
            text: userMessage,
          },
        ],
      },
    ];
  } else if (lastRequestMessages && lastAIResponse) {
    // 后续对话：使用上一次的完整message参数，添加上一次AI响应，再添加新的用户消息
    messages = [
      ...lastRequestMessages,
      {
        role: "assistant",
        content: lastAIResponse,
      },
      {
        role: "user",
        content: userMessage,
      },
    ];
  } else if (lastRequestMessages && !userMessage) {
    // 开始新字段收集：只使用系统提示词，让AI生成开场白
    messages = [...lastRequestMessages];
  } else {
    // 兜底：使用标准格式构建
    messages = [
      {
        role: "system",
        content: SYSTEM_PROMPT,
      },
      ...conversationHistory,
      {
        role: "user",
        content: userMessage,
      },
    ];
  }

  const result = await chatCompletions(messages);

  // 返回结果和发送的messages
  return {
    ...result,
    sentMessages: messages,
  };
};
