import React, { useState, useEffect } from "react";
import { Select, Button, Space } from "antd";
import type { SelectProps } from "antd";

interface SelectFilterProps {
  setSelectedKeys: (selectedKeys: React.Key[]) => void;
  selectedKeys: React.Key[];
  confirm: () => void;
  clearFilters: () => void;
  options: SelectProps["options"];
  placeholder?: string;
  paramName?: string; // 保留参数名，用于后端查询，但不再用于添加前缀
  mode?: "multiple" | "tags" | undefined;
  showSearch?: boolean;
}

/**
 * 选择框筛选组件
 * @param setSelectedKeys 设置选中的键
 * @param selectedKeys 当前选中的键
 * @param confirm 确认筛选
 * @param clearFilters 清除筛选
 * @param options 选项列表
 * @param placeholder 占位文本
 * @param paramName 参数名称，用于后端查询，但不再用于添加前缀
 * @param mode 选择模式，支持多选和标签
 * @param showSearch 是否显示搜索框
 */
const SelectFilter: React.FC<SelectFilterProps> = ({
  setSelectedKeys,
  selectedKeys,
  confirm,
  clearFilters,
  options,
  placeholder = "请选择",
  paramName = "", // 保留参数名，但不再用于添加前缀
  mode = "multiple", // 默认支持多选
  showSearch = true,
}) => {
  const [value, setValue] = useState<React.Key[]>(selectedKeys);

  useEffect(() => {
    setValue(selectedKeys);
  }, [selectedKeys]);

  const handleChange = (newValue: React.Key[]) => {
    setValue(newValue);

    // 直接设置选中的值，不做任何处理
    setSelectedKeys(Array.isArray(newValue) ? newValue : [newValue]);
  };

  const handleConfirm = () => {
    confirm();
  };

  const handleClear = () => {
    setValue([]);
    setSelectedKeys([]);
    clearFilters();
    // 点击重置后直接确认，不需要再点击确认按钮
    confirm();
  };

  return (
    <div style={{ padding: 8, minWidth: 280 }}>
      <Space style={{ width: "100%" }}>
        <Select
          mode={mode}
          allowClear
          showSearch={showSearch}
          style={{ width: 180 }}
          placeholder={placeholder}
          value={value}
          onChange={handleChange}
          options={options}
          placement="bottomLeft"
          dropdownStyle={{ zIndex: 1050 }}
          filterOption={(input, option) =>
            (option?.label as string)
              ?.toLowerCase()
              .includes(input.toLowerCase())
          }
        />
        <Space size={4}>
          <Button type="primary" onClick={handleConfirm} size="small">
            确定
          </Button>
          <Button onClick={handleClear} size="small">
            重置
          </Button>
        </Space>
      </Space>
    </div>
  );
};

export default SelectFilter;
