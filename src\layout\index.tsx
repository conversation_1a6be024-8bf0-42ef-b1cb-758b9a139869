import { Layout } from "antd";
import { Outlet, useLocation } from "react-router-dom";
import { useState, useEffect } from "react";
import Sidebar from "./components/Sidebar";
import Topbar from "./components/Topbar";
import QuickAccess from "./components/QuickAccess";
import { RouterAuth } from "@/router/routerAuth";
import { routerList } from "@/config/router";
import "./index.less";

const { Content } = Layout;

const AppLayout: React.FC = () => {
  const [menuPosition, setMenuPosition] = useState<"side" | "top">("top"); // 控制菜单位置
  const [checked, setChecked] = useState(true); // 控制开关的状态
  const [showQuickAccess, setShowQuickAccess] = useState(false);
  const location = useLocation();

  // 检查当前路径是否需要显示快速访问栏
  useEffect(() => {
    // 获取基础数据菜单及其子菜单
    const dataManagementMenu = routerList.find(
      (item) => item.key === "/data_management"
    );
    const dataSubMenuPaths =
      dataManagementMenu?.children?.map((item) => item.path) || [];

    // 获取价格管理菜单及其子菜单
    const priceManagementMenu = routerList.find(
      (item) => item.key === "/price_management"
    );
    const priceSubMenuPaths =
      priceManagementMenu?.children?.map((item) => item.path) || [];

    // 合并所有需要显示快速访问栏的路径
    const allSubMenuPaths = [...dataSubMenuPaths, ...priceSubMenuPaths];

    // 如果当前路径是基础数据或价格管理的子菜单路径之一，显示快速访问栏
    const shouldShowQuickAccess = allSubMenuPaths.some(
      (path) =>
        location.pathname === path || location.pathname.startsWith(path + "/")
    );

    setShowQuickAccess(shouldShowQuickAccess && menuPosition === "top");
  }, [location.pathname, menuPosition]);

  const handleSwitch = (checked: boolean) => {
    setMenuPosition(checked ? "side" : "top");
    setChecked(menuPosition === "top");
  };

  return (
    <RouterAuth>
      <Layout className="layout_container">
        {menuPosition === "side" && (
          <Sidebar onSwitch={handleSwitch} checked={checked} />
        )}
        <Layout>
          {menuPosition === "top" && <Topbar />}
          {menuPosition === "top" && showQuickAccess && <QuickAccess />}
          <Layout>
            <Content>
              <Outlet /> {/* 渲染子页面 */}
            </Content>
          </Layout>
        </Layout>
      </Layout>
    </RouterAuth>
  );
};

export default AppLayout;
