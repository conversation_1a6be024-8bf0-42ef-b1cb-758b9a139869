# 版本检测系统

## 📋 概述

本系统实现了基于Git提交哈希的版本检测功能，只有在代码真正变更时才会提示用户刷新页面，避免不必要的刷新提示。

## 🚀 特性

- ✅ **智能检测**: 基于Git提交哈希，只有代码变更时才提示更新
- ✅ **非侵入式**: 不修改现有代码结构，纯前端实现
- ✅ **国际化支持**: 支持中英文提示信息
- ✅ **灵活配置**: 可自定义检测间隔和启用条件
- ✅ **开发友好**: 开发环境默认不启用，避免干扰开发
- ✅ **用户友好**: 提供"立即刷新"和"稍后提醒"选项

## 📁 文件结构

```
├── scripts/
│   └── generate-version.js          # 版本文件生成脚本
├── src/
│   ├── hooks/
│   │   └── useVersionCheck.ts       # 版本检测Hook
│   ├── i18n/locales/
│   │   ├── zh-CN.json              # 中文翻译
│   │   └── en-US.json              # 英文翻译
│   └── App.tsx                     # 应用入口（已集成）
├── public/
│   └── version.json                # 版本信息文件（构建时生成）
└── package.json                    # 构建脚本配置
```

## 🔧 工作原理

### 1. 构建时生成版本信息
```bash
npm run build
```
执行构建时会自动：
1. 运行 `generate-version.js` 脚本
2. 获取当前Git提交哈希
3. 生成 `public/version.json` 文件

### 2. 运行时检测版本变化
- 应用启动时获取当前版本信息
- 定期（默认30秒）检查版本文件
- 发现版本变化时显示更新提示

### 3. 版本信息格式
```json
{
  "version": "a1b2c3d4",
  "gitHash": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
  "shortHash": "a1b2c3d4",
  "branch": "main",
  "isDirty": false,
  "lastCommitTime": "2024-01-04 10:00:00 +0800",
  "buildTime": "2024-01-04T02:00:00.000Z",
  "timestamp": 1704355200000
}
```

## ⚙️ 配置选项

### useVersionCheck Hook 参数

```typescript
useVersionCheck({
  checkInterval: 30000,        // 检查间隔（毫秒）
  enabled: true,               // 是否启用版本检查
  enableInDevelopment: false   // 是否在开发环境启用
});
```

### 默认配置
- **检查间隔**: 30秒
- **生产环境**: 启用
- **开发环境**: 不启用
- **页面获得焦点时**: 自动检查更新

## 🎯 使用场景

### 场景1: 代码更新部署
1. 开发者提交代码并推送到仓库
2. CI/CD 系统构建并部署新版本
3. 用户浏览器检测到版本变化
4. 显示更新提示，用户选择是否刷新

### 场景2: 用户切换回页面
1. 用户切换到其他标签页或应用
2. 期间系统发布了新版本
3. 用户切换回页面时自动检查更新
4. 发现新版本时提示刷新

## 🔍 调试信息

在浏览器控制台中可以看到以下调试信息：
```
✅ 版本文件生成成功: a1b2c3d4
📁 文件位置: /path/to/public/version.json
当前版本: a1b2c3d4
检测到新版本: { current: "a1b2c3d4", new: "e5f6g7h8" }
```

## 🚨 注意事项

1. **Git 仓库要求**: 需要在Git仓库中使用，否则会使用时间戳作为备用方案
2. **网络要求**: 需要能够访问 `/version.json` 文件
3. **缓存处理**: 请求版本文件时会添加时间戳参数避免缓存
4. **用户体验**: 用户可以选择"稍后提醒"来延迟刷新

## 🛠️ 自定义扩展

### 添加更多检测触发条件
```typescript
// 在特定操作后检查更新
const { manualCheck } = useVersionCheck();

const handleImportantAction = async () => {
  // 执行重要操作
  await doSomething();
  
  // 手动检查更新
  await manualCheck();
};
```

### 自定义提示样式
可以修改 `useVersionCheck.ts` 中的 Modal.confirm 配置来自定义提示框样式。

## 📊 性能影响

- **网络请求**: 每30秒一次轻量级请求（约1KB）
- **内存占用**: 极小，仅存储版本字符串
- **CPU占用**: 极小，仅在检查时短暂运行
- **用户体验**: 无感知，不影响正常使用
