import React from "react";
import { Modal, Typography, Row, Col, Card, Tag, Badge, Divider } from "antd";
import {
  InfoCircleOutlined,
  GlobalOutlined,
  DollarCircleOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import "./index.less";

const { Text } = Typography;

interface DetailModalProps {
  open: boolean;
  onClose: () => void;
  record: any;
}

const DetailModal: React.FC<DetailModalProps> = ({ open, onClose, record }) => {
  const { t } = useTranslation();

  if (!record) return null;

  // 格式化布尔值显示
  const formatBoolean = (value: boolean) => {
    return value ? (
      <Tag color="success" icon={<CheckCircleOutlined />}>
        {t("domesticPriceManagement.detailModal.booleanValues.yes")}
      </Tag>
    ) : (
      <Tag color="default" icon={<CloseCircleOutlined />}>
        {t("domesticPriceManagement.detailModal.booleanValues.no")}
      </Tag>
    );
  };

  return (
    <Modal
      title={
        <div>
          <span className="title-icon">
            <InfoCircleOutlined />
          </span>
          {t("domesticPriceManagement.detailModal.title")}
          {record && (
            <Tag color="blue" className="price-id-tag">
              ID: {record.domesticpriceid}
            </Tag>
          )}
        </div>
      }
      open={open}
      onCancel={onClose}
      footer={null}
      width={800}
      className="domestic-price-detail-modal"
      destroyOnClose
      centered
    >
      <div className="price-detail-content">
        <Card className="detail-card">
          <div className="section-title">
            <GlobalOutlined />{" "}
            {t("domesticPriceManagement.detailModal.sections.basicInfo")}
          </div>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "domesticPriceManagement.detailModal.fields.shippingPlace"
                  )}
                </Text>
                <div className="info-value">
                  <Tag color="green" icon={<EnvironmentOutlined />}>
                    {record.shippingplace || "-"}
                  </Tag>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">
                  {t(
                    "domesticPriceManagement.detailModal.fields.shippingProvince"
                  )}
                </Text>
                <div className="info-value">
                  {record.shippingprovince || "-"}
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">
                  {t("domesticPriceManagement.detailModal.fields.destination")}
                </Text>
                <div className="info-value">
                  <Tag color="orange" icon={<EnvironmentOutlined />}>
                    {record.destination || "-"}
                  </Tag>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">
                  {t("domesticPriceManagement.detailModal.fields.logistics")}
                </Text>
                <div className="info-value">
                  <Tag color="blue">{record.logistics || "-"}</Tag>
                </div>
              </div>
            </Col>
          </Row>
        </Card>

        <Card className="detail-card">
          <div className="section-title">
            <DollarCircleOutlined />{" "}
            {t("domesticPriceManagement.detailModal.sections.priceInfo")}
          </div>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">
                  {t("domesticPriceManagement.detailModal.fields.volumePrice")}
                </Text>
                <div className="info-value">
                  {record.volumeprice || "-"} CNY/m³
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">
                  {t("domesticPriceManagement.detailModal.fields.weightPrice")}
                </Text>
                <div className="info-value">
                  {record.weightprice || "-"} CNY/kg
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">
                  {t("domesticPriceManagement.detailModal.fields.minCharge")}
                </Text>
                <div className="info-value">{record.minicharge || "-"} CNY</div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">
                  {t("domesticPriceManagement.detailModal.fields.deliveryFee")}
                </Text>
                <div className="info-value">
                  {record.deliveryfee || "-"} CNY
                </div>
              </div>
            </Col>
          </Row>
        </Card>

        <Card className="detail-card">
          <div className="section-title">
            <ClockCircleOutlined />{" "}
            {t("domesticPriceManagement.detailModal.sections.agingInfo")}
          </div>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">
                  {t("domesticPriceManagement.detailModal.fields.fastestAging")}
                </Text>
                <div className="info-value">
                  <Badge
                    color="#52c41a"
                    text={`${record.fastestaging || "-"} ${t("domesticPriceManagement.detailModal.units.days")}`}
                  />
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">
                  {t("domesticPriceManagement.detailModal.fields.slowestAging")}
                </Text>
                <div className="info-value">
                  <Badge
                    color="#faad14"
                    text={`${record.slowestaging || "-"} ${t("domesticPriceManagement.detailModal.units.days")}`}
                  />
                </div>
              </div>
            </Col>
          </Row>
        </Card>
      </div>
    </Modal>
  );
};

export default DetailModal;
