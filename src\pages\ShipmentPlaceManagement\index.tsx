import React, { useState, useEffect } from "react";
import "./index.less";
import TableCom from "@/components/TableCom";
import { Space, Button, Form, Popconfirm, Tooltip, message } from "antd";
import type { TableProps } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { getAllShipmentPlace, delShipmentPlace } from "./services";
import ActionModal from "./components/ActionModal";

interface ShipmentPlace {
  placeid: number;
  shipmentplace: string;
  enplace: string;
  shipmentprovince: string;
  enprovince: string;
}

const ShipmentPlaceManagement: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [data, setData] = useState<ShipmentPlace[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [method, setMethod] = useState<string>("");

  const queryShipmentPlaceList = async () => {
    try {
      const res = await getAllShipmentPlace();
      const { data } = res;
      if (data?.resultCode === 200) {
        setData(data.data);
      } else {
        message.error(data.message);
      }
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    queryShipmentPlaceList();
  }, []);

  const columns: TableProps<ShipmentPlace>["columns"] = [
    // {
    //   title: "ID",
    //   dataIndex: "placeid",
    //   key: "placeid",
    // },
    {
      title: t("dataManagement.shipmentPlaceManagement.columns.shipmentPlace"),
      dataIndex: "shipmentplace",
      key: "shipmentplace",
    },
    {
      title: t("dataManagement.shipmentPlaceManagement.columns.enPlace"),
      dataIndex: "enplace",
      key: "enplace",
    },
    {
      title: t(
        "dataManagement.shipmentPlaceManagement.columns.shipmentProvince"
      ),
      dataIndex: "shipmentprovince",
      key: "shipmentprovince",
    },
    {
      title: t("dataManagement.shipmentPlaceManagement.columns.enProvince"),
      dataIndex: "enprovince",
      key: "enprovince",
    },
    {
      title: t("dataManagement.shipmentPlaceManagement.columns.actions"),
      key: "action",
      render: (_, record) => (
        <Space size="middle" className="operation-buttons">
          <Tooltip
            title={t("dataManagement.shipmentPlaceManagement.actions.edit")}
          >
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              className="edit-button"
              size="small"
            />
          </Tooltip>
          <Tooltip
            title={t("dataManagement.shipmentPlaceManagement.actions.delete")}
          >
            <Popconfirm
              title={t(
                "dataManagement.shipmentPlaceManagement.deleteConfirm.title"
              )}
              description={t(
                "dataManagement.shipmentPlaceManagement.deleteConfirm.description"
              )}
              onConfirm={() => handleDel(record)}
              onCancel={handleCancel}
              okText={t(
                "dataManagement.shipmentPlaceManagement.deleteConfirm.okText"
              )}
              cancelText={t(
                "dataManagement.shipmentPlaceManagement.deleteConfirm.cancelText"
              )}
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                className="delete-button"
                size="small"
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    showModal();
    setMethod("add");
    form.resetFields();
  };

  const handleEdit = (record: ShipmentPlace) => {
    setMethod("edit");
    showModal();
    form.setFieldsValue(record);
  };

  const handleDel = async (record: ShipmentPlace) => {
    const res = await delShipmentPlace({ placeid: record?.placeid });
    const { data } = res;
    if (data.resultCode === 200) {
      queryShipmentPlaceList();
      message.success(
        t("dataManagement.shipmentPlaceManagement.messages.deleteSuccess")
      );
    } else {
      message.error(data.message);
    }
  };

  const handleCancel = () => {
    // 取消删除操作
  };

  const showModal = () => {
    setIsModalOpen(true);
  };

  const filterRender = () => {
    return (
      <div className="filter_box">
        <div className="title">
          {t("dataManagement.shipmentPlaceManagement.title")}
        </div>
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            {t("dataManagement.shipmentPlaceManagement.newShipmentPlace")}
          </Button>
        </Space>
      </div>
    );
  };

  return (
    <div className="shipment-place-management">
      <TableCom<ShipmentPlace>
        columns={columns}
        data={data}
        filterRender={filterRender}
      />
      <ActionModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        method={method}
        form={form}
        queryShipmentPlaceList={queryShipmentPlaceList}
      />
    </div>
  );
};

export default ShipmentPlaceManagement;
