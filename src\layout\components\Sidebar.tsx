import React, { ReactElement } from "react";
import { Layout, Menu } from "antd";
import { useNavigate } from "react-router-dom";
import { routerList, MenuItem } from "@/config/router";
import * as Icon from "@ant-design/icons";
import { FileOutlined } from "@ant-design/icons";
import "./index.less";
import { useAppSelector } from "@/store/hooks";

const { Sider } = Layout;

// 定义 props 类型
interface SidebarProps {
  onSwitch: (checked: boolean) => void;
  checked: boolean; // 控制开关的状态
}

// 定义 IconType，确保 icon 只能是合法的 Ant Design 图标名称
type IconType = keyof typeof Icon;
const Sidebar: React.FC<SidebarProps> = () => {
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.user);

  // 动态获取 icon
  const iconToElement = (name: IconType): ReactElement | null => {
    const IconComponent = Icon[name] as React.ComponentType<any>; // 确保是 React 组件
    return IconComponent ? <IconComponent /> : null;
  };

  // 菜单数据处理
  const getItems = (items: MenuItem[]): any[] => {
    // 根据用户身份过滤菜单
    if (user?.useridentity === 0) {
      // 用户身份为0的用户只能访问AI智能报价、智能报价、智能报价2.0、3D智能报价和供应价格筛选页面
      const filteredItems = items.filter(
        (item) =>
          item.key === "/ai_quotation" ||
          item.key === "/supply_price" ||
          item.key === "/smart_quotation"
      );

      // 处理过滤后的菜单项
      return filteredItems.map((item) => {
        return {
          key: item.path,
          ...(item.icon && { icon: iconToElement(item.icon) }),
          label: item.label,
          ...(item.children && { children: getItems(item.children) }),
        };
      });
    } else if (user?.useridentity === 5) {
      // 超级管理员只能访问组织管理和基础数据管理
      const filteredItems = items.filter(
        (item) =>
          item.key === "/organization_manage" || item.key === "/data_management"
      );

      // 处理过滤后的菜单项
      return filteredItems.map((item) => {
        // 如果是基础数据管理，需要过滤子菜单
        if (item.key === "/data_management" && item.children) {
          // 只保留允许的五个子菜单
          const allowedChildren = [
            "/airline_management",
            "/harbor_management",
            "/package_type_management",
            "/special_items_management",
            "/shipment_place_management",
          ];

          // 过滤子菜单
          const filteredChildren = item.children.filter((child) =>
            allowedChildren.includes(child.key)
          );

          // 返回带有过滤后子菜单的菜单项
          return {
            key: item.path,
            ...(item.icon && { icon: iconToElement(item.icon) }),
            label: item.label,
            children: filteredChildren.map((child) => ({
              key: child.path,
              ...(child.icon && { icon: iconToElement(child.icon) }),
              label: child.label,
            })),
          };
        } else {
          // 其他菜单项直接返回
          return {
            key: item.path,
            ...(item.icon && { icon: iconToElement(item.icon) }),
            label: item.label,
            ...(item.children && { children: getItems(item.children) }),
          };
        }
      });
    } else if (user?.useridentity === 1) {
      // 询价业务员只能访问询价管理、供应价格筛选、国内价格管理与国际价格管理
      const filteredItems = items.filter(
        (item) =>
          item.key === "/quotation" ||
          item.key === "/supply_price" ||
          item.key === "/domestic_price" ||
          item.key === "/international_price"
      );

      // 处理过滤后的菜单项
      return filteredItems.map((item) => {
        return {
          key: item.path,
          ...(item.icon && { icon: iconToElement(item.icon) }),
          label: item.label,
          ...(item.children && { children: getItems(item.children) }),
        };
      });
    } else if (user?.useridentity === 2) {
      // 用户身份为2的用户可以访问国内价格管理、国际价格管理页面、特殊报价管理页面以及基础数据管理
      const filteredItems = items.filter(
        (item) =>
          item.key === "/domestic_price" ||
          item.key === "/international_price" ||
          item.key === "/manual_quotation" ||
          item.key === "/data_management"
      );

      // 处理过滤后的菜单项
      return filteredItems.map((item) => {
        // 如果是基础数据管理，需要过滤子菜单
        if (item.key === "/data_management" && item.children) {
          // 只保留允许的五个子菜单
          const allowedChildren = [
            "/airline_management",
            "/harbor_management",
            "/package_type_management",
            "/special_items_management",
            "/shipment_place_management",
          ];

          // 过滤子菜单
          const filteredChildren = item.children.filter((child) =>
            allowedChildren.includes(child.key)
          );

          // 返回带有过滤后子菜单的菜单项
          return {
            key: item.path,
            ...(item.icon && { icon: iconToElement(item.icon) }),
            label: item.label,
            children: filteredChildren.map((child) => ({
              key: child.path,
              ...(child.icon && { icon: iconToElement(child.icon) }),
              label: child.label,
            })),
          };
        } else {
          return {
            key: item.path,
            ...(item.icon && { icon: iconToElement(item.icon) }),
            label: item.label,
            ...(item.children && { children: getItems(item.children) }),
          };
        }
      });
    } else if (user?.useridentity === 3) {
      // 用户身份为3的用户只能访问询价管理、国内价格管理、国际价格管理和供应价格筛选页面
      const filteredItems = items.filter(
        (item) =>
          item.key === "/quotation" ||
          item.key === "/domestic_price" ||
          item.key === "/international_price" ||
          item.key === "/supply_price"
      );

      // 处理过滤后的菜单项
      return filteredItems.map((item) => {
        return {
          key: item.path,
          ...(item.icon && { icon: iconToElement(item.icon) }),
          label: item.label,
          ...(item.children && { children: getItems(item.children) }),
        };
      });
    } else if (user?.useridentity === 4) {
      // 用户身份为4的用户可以访问国内价格管理、国际价格管理页面、特殊报价管理页面以及基础数据管理
      const filteredItems = items.filter(
        (item) =>
          item.key === "/domestic_price" ||
          item.key === "/international_price" ||
          item.key === "/manual_quotation" ||
          item.key === "/data_management"
      );

      // 处理过滤后的菜单项
      return filteredItems.map((item) => {
        // 如果是基础数据管理，需要过滤子菜单
        if (item.key === "/data_management" && item.children) {
          // 只保留允许的五个子菜单
          const allowedChildren = [
            "/airline_management",
            "/harbor_management",
            "/package_type_management",
            "/special_items_management",
            "/shipment_place_management",
          ];

          // 过滤子菜单
          const filteredChildren = item.children.filter((child) =>
            allowedChildren.includes(child.key)
          );

          // 返回带有过滤后子菜单的菜单项
          return {
            key: item.path,
            ...(item.icon && { icon: iconToElement(item.icon) }),
            label: item.label,
            children: filteredChildren.map((child) => ({
              key: child.path,
              ...(child.icon && { icon: iconToElement(child.icon) }),
              label: child.label,
            })),
          };
        } else {
          return {
            key: item.path,
            ...(item.icon && { icon: iconToElement(item.icon) }),
            label: item.label,
            ...(item.children && { children: getItems(item.children) }),
          };
        }
      });
    } else {
      // 其他用户可以访问所有菜单
      return items.map((item) => ({
        key: item.path,
        ...(item.icon && { icon: iconToElement(item.icon) }),
        label: item.label,
        ...(item.children && { children: getItems(item.children) }),
      }));
    }
  };

  return (
    <Sider theme="light" className="layout_sidebar">
      <div className="demo-logo">
        <FileOutlined className="logo-icon" />
        <span className="logo-text">报价管理系统</span>
      </div>
      <Menu
        theme="light"
        mode="inline"
        defaultSelectedKeys={["/home"]}
        selectedKeys={[window?.location?.pathname]}
        onClick={({ key }) => navigate(key)}
        items={getItems(routerList)}
      />
      {/* 菜单切换按钮 */}
      {/* <LayoutSwitch onSwitch={onSwitch} checked={checked} /> */}
    </Sider>
  );
};

export default Sidebar;
