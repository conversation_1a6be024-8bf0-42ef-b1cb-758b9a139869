import React, { ReactElement, useState, useEffect } from "react";
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  MenuProps,
  Modal,
  message,
  Form,
  Input,
  Button,
} from "antd";
import { useNavigate } from "react-router-dom";
import { routerList, MenuItem } from "@/config/router";
import "./index.less";
import {
  UserOutlined,
  LogoutOutlined,
  FileOutlined,
  ExclamationCircleOutlined,
  SettingOutlined,
  KeyOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import ProfileModal from "@/components/ProfileModal";
import PasswordModal from "@/components/PasswordModal";
import LanguageSwitcher from "@/components/LanguageSwitcher";
import * as Icon from "@ant-design/icons";
import { useDispatch } from "react-redux";
import { removeUserInfo } from "@/store/slices/userSlice";
import { useAppSelector } from "@/store/hooks";
import { delAccount, getCode } from "@/pages/Login/services";
import { useTranslation } from "react-i18next";

const { Header } = Layout;

// 定义 props 类型
interface TopbarProps {
  onSwitch?: (checked: boolean) => void; // 假设 onSwitch 是一个函数
  checked?: boolean; // 控制开关的状态
}

// 定义 IconType，确保 icon 只能是合法的 Ant Design 图标名称
type IconType = keyof typeof Icon;

// interface MenuItem {
//   key: string;
//   path: string;
//   icon?: IconType;
//   label: string;
//   authenticate?: boolean;
//   children?: MenuItem[];
// }

// interface User {
//   userLevel: number;
// }

const Topbar: React.FC<TopbarProps> = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { confirm } = Modal;
  const { t } = useTranslation();
  const { user } = useAppSelector((state) => state.user);
  const [profileModalVisible, setProfileModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);

  // 打开个人信息管理模态框
  const handleOpenProfileModal = () => {
    setProfileModalVisible(true);
  };

  // 关闭个人信息管理模态框
  const handleCloseProfileModal = () => {
    setProfileModalVisible(false);
  };

  // 打开密码修改模态框
  const handleOpenPasswordModal = () => {
    setPasswordModalVisible(true);
  };

  // 关闭密码修改模态框
  const handleClosePasswordModal = () => {
    setPasswordModalVisible(false);
  };

  // 处理退出登录
  const handleLogout = () => {
    confirm({
      title: t("layout.logoutConfirm"),
      icon: <ExclamationCircleOutlined />,
      content: t("layout.logoutContent"),
      okText: t("common.confirm"),
      cancelText: t("common.cancel"),
      onOk() {
        // 清除用户信息和token
        dispatch(removeUserInfo());
        message.success(t("layout.logoutSuccess"));
        // 跳转到登录页
        navigate("/login");
      },
    });
  };

  // 处理注销账号
  const handleDeactivateAccount = () => {
    confirm({
      title: t("layout.deactivateConfirm"),
      icon: <ExclamationCircleOutlined />,
      content: t("layout.deactivateContent"),
      okText: t("layout.confirmDeactivate"),
      cancelText: t("common.cancel"),
      okButtonProps: { danger: true },
      onOk: async () => {
        try {
          // 再次确认
          return new Promise((resolve, reject) => {
            // 验证码状态变量
            let countdown = 0;
            let sendingCode = false;
            let countdownTimer: NodeJS.Timeout | null = null;
            let verificationCode = "";

            // 创建验证码输入框
            const VerificationInput = () => {
              const [code, setCode] = useState("");

              // 更新验证码
              const handleCodeChange = (
                e: React.ChangeEvent<HTMLInputElement>
              ) => {
                setCode(e.target.value);
                verificationCode = e.target.value;
              };

              // 发送验证码函数
              const sendVerificationCode = async () => {
                if (countdown > 0 || sendingCode) return;

                try {
                  sendingCode = true;
                  const codeBtn = document.querySelector(
                    ".verification-code-btn"
                  );
                  if (codeBtn) {
                    codeBtn.textContent = "发送中...";
                    codeBtn.setAttribute("disabled", "true");
                  }

                  // 调用发送验证码接口
                  const response = await getCode({
                    email: user?.email as string,
                  });

                  if (response.data?.resultCode === 200) {
                    message.success(t("verification.codeSent"));
                    // 开始倒计时
                    countdown = 60;

                    const updateButtonText = () => {
                      const btn = document.querySelector(
                        ".verification-code-btn"
                      );
                      if (btn) {
                        btn.textContent = t("verification.resendAfter", {
                          seconds: countdown,
                        });
                        btn.setAttribute("disabled", "true");
                      }
                    };

                    updateButtonText();

                    countdownTimer = setInterval(() => {
                      countdown -= 1;

                      if (countdown <= 0) {
                        if (countdownTimer) clearInterval(countdownTimer);
                        sendingCode = false;
                        const btn = document.querySelector(
                          ".verification-code-btn"
                        );
                        if (btn) {
                          btn.textContent = t("verification.sendCode");
                          btn.removeAttribute("disabled");
                        }
                      } else {
                        updateButtonText();
                      }
                    }, 1000);
                  } else {
                    message.error(
                      response.data?.message || t("verification.codeSendFailed")
                    );
                    sendingCode = false;
                    const btn = document.querySelector(
                      ".verification-code-btn"
                    );
                    if (btn) {
                      btn.textContent = t("verification.sendCode");
                      btn.removeAttribute("disabled");
                    }
                  }
                } catch (error) {
                  console.error("发送验证码出错:", error);
                  message.error(t("verification.codeSendFailed"));
                  sendingCode = false;
                  const btn = document.querySelector(".verification-code-btn");
                  if (btn) {
                    btn.textContent = t("verification.sendCode");
                    btn.removeAttribute("disabled");
                  }
                }
              };

              // 组件挂载后自动发送验证码
              useEffect(() => {
                sendVerificationCode();
              }, []);

              return (
                <div>
                  <p>{t("verification.emailHint", { email: user?.email })}</p>
                  <Input
                    placeholder={t("verification.inputCode")}
                    maxLength={6}
                    value={code}
                    onChange={handleCodeChange}
                    suffix={
                      <Button
                        type="link"
                        size="small"
                        className="verification-code-btn"
                        onClick={sendVerificationCode}
                      >
                        {t("verification.sendCode")}
                      </Button>
                    }
                  />
                </div>
              );
            };

            // 显示验证码确认对话框
            Modal.confirm({
              title: t("verification.title"),
              icon: <ExclamationCircleOutlined />,
              content: <VerificationInput />,
              okText: t("layout.confirmDeactivate"),
              cancelText: t("common.cancel"),
              okButtonProps: { danger: true },
              onOk: async () => {
                try {
                  // 验证验证码
                  if (!verificationCode || verificationCode.length !== 6) {
                    message.error(t("verification.codeRequired"));
                    return Promise.reject();
                  }

                  // 调用注销账号接口，传入验证码
                  const response = await delAccount({
                    email: user?.email as string,
                    verifycode: verificationCode,
                  });

                  if (response.data?.resultCode === 200) {
                    message.success(t("layout.deactivateSuccess"));
                    // 清除用户信息和token
                    dispatch(removeUserInfo());
                    // 跳转到登录页
                    navigate("/login");
                    resolve(null);
                  } else {
                    message.error(response.data?.message || t("common.error"));
                    // reject();
                  }
                } catch (error) {
                  console.error("注销账号出错:", error);
                  message.error(t("verification.codeInvalid"));
                  return Promise.reject(error);
                }
              },
              onCancel: () => {
                reject();
              },
              afterClose: () => {
                // 清理倒计时
                if (countdownTimer) {
                  clearInterval(countdownTimer);
                }
              },
            });
          });
        } catch (error) {
          return Promise.reject(error);
        }
      },
    });
  };

  // 动态获取 icon
  const iconToElement = (name: IconType): ReactElement | null => {
    const IconComponent = Icon[name] as React.ComponentType<any>; // 确保是 React 组件
    return IconComponent ? <IconComponent /> : null;
  };

  // 菜单数据处理
  const getItems = (items: MenuItem[]): any[] => {
    // 定义不同用户身份可访问的路径
    const allowedPathsMap: Record<number, string[]> = {
      0: ["/personal_quotation", "/ai_quotation", "/smart_quotation"],
      1: [
        "/quotation",
        "/supply_price",
        "/domestic_price",
        "/international_price",
        "/intelligenty_quotation",
      ],
      2: [
        "/domestic_price",
        "/international_price",
        "/manual_quotation",
        "/data_management",
        "/airline_management",
        "/harbor_management",
        "/package_type_management",
        "/special_items_management",
        "/shipment_place_management",
        "/supplier_management",
      ],
      3: [
        "/quotation",
        "/domestic_price",
        "/international_price",
        "/supply_price",
        "/intelligenty_quotation",
      ],
      4: [
        "/domestic_price",
        "/international_price",
        "/manual_quotation",
        "/data_management",
        "/airline_management",
        "/harbor_management",
        "/package_type_management",
        "/special_items_management",
        "/shipment_place_management",
        "/supplier_management",
      ],
      5: [
        "/data_management",
        "/organization_manage",
        "/airline_management",
        "/harbor_management",
        "/package_type_management",
        "/special_items_management",
        "/shipment_place_management",
        "/supplier_management",
        // "/intelligenty_quotation",
        // "/organization_manage",
        // "/quotation",
        // "/supply_price",
        // "/domestic_price",
        // "/international_price",
        // "/domestic_price",
        // "/manual_quotation",
      ],
    };

    // 获取当前用户身份的允许路径
    const userIdentity = user?.useridentity;
    const allowedPaths =
      userIdentity !== undefined ? allowedPathsMap[userIdentity] || [] : [];

    // 通用的菜单项转换函数
    const transformMenuItem = (item: MenuItem): any => {
      // 基本菜单项结构
      const menuItem = {
        key: item.path,
        ...(item.icon && { icon: iconToElement(item.icon) }),
        label: item.labelKey ? t(item.labelKey) : item.label,
      };

      // 特殊处理基础数据管理子菜单（对用户角色2、4和5）
      if (
        (userIdentity === 2 || userIdentity === 4 || userIdentity === 5) &&
        item.key === "/data_management" &&
        item.children
      ) {
        const allowedChildren = [
          "/airline_management",
          "/harbor_management",
          "/package_type_management",
          "/special_items_management",
          "/shipment_place_management",
          "/supplier_management",
        ];

        const filteredChildren = item.children.filter((child) =>
          allowedChildren.includes(child.key)
        );

        return {
          ...menuItem,
          children: filteredChildren.map((child) => ({
            key: child.path,
            ...(child.icon && { icon: iconToElement(child.icon) }),
            label: child.labelKey ? t(child.labelKey) : child.label,
          })),
        };
      }

      // 处理普通子菜单
      if (item.children) {
        return {
          ...menuItem,
          children: getItems(item.children),
        };
      }

      return menuItem;
    };

    // 如果是已定义的用户身份，过滤菜单项
    if (userIdentity !== undefined && allowedPaths.length > 0) {
      const filteredItems = items.filter((item) =>
        allowedPaths.includes(item.key)
      );
      return filteredItems.map(transformMenuItem);
    }

    // 默认情况：其他用户可以访问所有菜单
    return items.map(transformMenuItem);
  };

  // // 菜单数据处理
  // const getItems = (items: MenuItem[], user?: User) => {
  //   return items.reduce((acc: any[], item) => {
  //     if (user?.userLevel !== 0 || !item.authenticate) {
  //       acc.push({
  //         key: item.path,
  //         ...(item.icon && { icon: iconToElement(item.icon) }),
  //         label: item.label,
  //         ...(item.children && { children: getItems(item.children, user) }),
  //       });
  //     }
  //     return acc;
  //   }, []);
  // };

  // 用户菜单项
  const userMenuItems: MenuProps["items"] = [
    {
      key: "profile",
      label: t("layout.profile"),
      icon: <UserOutlined />,
      onClick: handleOpenProfileModal,
    },
    {
      key: "password",
      label: t("layout.changePassword"),
      icon: <KeyOutlined />,
      onClick: handleOpenPasswordModal,
    },
    {
      type: "divider",
    },
    {
      key: "logout",
      label: t("layout.logout"),
      icon: <LogoutOutlined />,
      onClick: handleLogout,
    },
  ];

  // 只有用户身份为0的用户才显示注销账号选项
  if (user?.useridentity === 0) {
    // 在退出登录前插入注销账号选项
    userMenuItems.splice(
      userMenuItems.length - 1,
      0,
      {
        key: "deactivate",
        label: t("layout.deactivateAccount"),
        icon: <DeleteOutlined />,
        onClick: handleDeactivateAccount,
        danger: true,
      },
      {
        type: "divider",
      }
    );
  }

  return (
    <>
      <Header className="header_container">
        <div className="topbar">
          <div className="demo-logo">
            <FileOutlined className="logo-icon" />
            <span className="logo-text">{t("layout.systemTitle")}</span>
          </div>
          <Menu
            mode="horizontal"
            selectedKeys={[window?.location?.pathname]}
            onClick={({ key }) => navigate(key)}
            items={getItems(routerList)}
            disabledOverflow={false} // 中文时禁用溢出隐藏，英文时启用溢出隐藏
          />
        </div>
        <div className="header-actions">
          {/* <Tooltip title="帮助中心">
            <div className="action-item">
              <QuestionCircleOutlined />
            </div>
          </Tooltip>
          <Tooltip title="消息通知">
            <div className="action-item">
              <Badge count={5} size="small">
                <BellOutlined />
              </Badge>
            </div>
          </Tooltip> */}
          <LanguageSwitcher />
          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            trigger={["click"]}
          >
            <div className="avatar">
              <Avatar icon={<UserOutlined />} />
              <p>{user?.email}</p>
              {/* <p>Teresa</p> */}
            </div>
          </Dropdown>
        </div>
      </Header>

      {/* 个人信息管理模态框 */}
      <ProfileModal
        visible={profileModalVisible}
        onClose={handleCloseProfileModal}
      />

      {/* 密码修改模态框 */}
      <PasswordModal
        visible={passwordModalVisible}
        onClose={handleClosePasswordModal}
      />
    </>
  );
};

export default Topbar;
