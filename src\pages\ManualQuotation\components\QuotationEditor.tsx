import React, { useState, useEffect } from "react";
import {
  Modal,
  Form,
  Input,
  Button,
  Divider,
  Typography,
  Card,
  Space,
  message,
  Tooltip,
  Row,
  Col,
  Select,
  InputNumber,
  DatePicker,
} from "antd";
import {
  SaveOutlined,
  PrinterOutlined,
  DownloadOutlined,
  EditOutlined,
  CloseOutlined,
  CopyOutlined,
  InfoCircleOutlined,
  FileTextOutlined,
  DollarOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import "./index.less";
// import { saveManualQuotation, updateInquiryStatus } from "../services";
import dayjs from "dayjs";
import { formatDateToTimestamp, copyToClipboard } from "@/utils/util";

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface QuotationEditorProps {
  visible: boolean;
  onClose: () => void;
  record: any;
  onSaveSuccess: () => void;
}

const QuotationEditor: React.FC<QuotationEditorProps> = ({
  visible,
  onClose,
  record,
  onSaveSuccess,
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [editMode, setEditMode] = useState(true);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible && record) {
      // 初始化表单数据
      const initialValues = {
        inquiryid: record.inquiryid,
        freightmethod: record.freightmethod,
        goodstype: record.goodstype,
        tradeterms: record.tradeterms,
        companyname: record.companyname,
        inquirer: record.inquirer,
        shippedplace: record.shippedplace,
        originport: record.originport,
        unloadingport: record.unloadingport,
        grossweight: record.grossweight,
        goodsvolume: record.goodsvolume,
        isbrand: record.isbrand,
        ensurecabin: record.ensurecabin,
        isvalidity: record.isvalidity,
        packagetype: record.packagetype,
        specialcargo: record.specialcargo,
        airline: "",
        route: `${record.originport}-${record.unloadingport}`,
        transitTime: "1-3",
        schedule: "********",
        airFreight: "",
        chargeWeight: "",
        terminalCharge: "",
        handleFee: "",
        customs: "",
        pickupFee: "",
        airDocCharge: "",
        ensCharge: "",
        otherCharges: "",
        remarks: "",
        validityDate: dayjs().add(30, "day"),
        quotationContent: generateDefaultContent(record),
      };
      form.setFieldsValue(initialValues);
    }
  }, [visible, record, form]);

  // 生成默认的报价单内容
  const generateDefaultContent = (record: any) => {
    if (!record) return "";

    return `Airline（航空公司）          :CA
Route（路线）                :PEK-SVO1
T/T（时效）                  :1day
Schedule（班次频率）         :D126
A/F (运费单价)               :33CNY/KG(+45kgs;stand by service)
CW（计费重）                 :59KG

额外费用
Terminal charge（终端费用）  :0.1USD/KG;Min 10USD(Charge by CW)
Handle fee（处理费）         :50USD/shpt
Customs（报关费）            :50USD/shpt (up to 6 items only, thereafter USD15 per item)
Pick up fee（提货费）        :50USD
Air doc charge（文件费）     : 10USD/shpt
ENS_charge（申报费）         :10USD/shpt
Brand_cost（品牌处理费）      : 800CNY
`;
  };

  // 更新报价单内容
  const updateQuotationContent = () => {
    const values = form.getFieldsValue();
    const content = `Airline（航空公司）          :CA
Route（路线）                :PEK-SVO1
T/T（时效）                  :1day
Schedule（班次频率）         :D126
A/F (运费单价)               :33CNY/KG(+45kgs;stand by service)
CW（计费重）                 :59KG

额外费用
Terminal charge（终端费用）  :0.1USD/KG;Min 10USD(Charge by CW)
Handle fee（处理费）         :50USD/shpt
Customs（报关费）            :50USD/shpt (up to 6 items only, thereafter USD15 per item)
Pick up fee（提货费）        :50USD
Air doc charge（文件费）     : 10USD/shpt
ENS_charge（申报费）         :10USD/shpt
Brand_cost（品牌处理费）      : 800CNY`;

    form.setFieldsValue({ quotationContent: content });
  };

  // 从报价单内容中提取字段值
  const extractFieldsFromContent = () => {
    const content = form.getFieldValue("quotationContent");
    if (!content) return;

    try {
      // 提取航空公司
      const airlineMatch = content.match(/Airline\s*:\s*([^\n]*)/);
      if (airlineMatch && airlineMatch[1].trim()) {
        form.setFieldValue("airline", airlineMatch[1].trim());
      }

      // 提取路线
      const routeMatch = content.match(/Route\s*:\s*([^\n]*)/);
      if (routeMatch && routeMatch[1].trim()) {
        form.setFieldValue("route", routeMatch[1].trim());
      }

      // 提取运输时间
      const ttMatch = content.match(/T\/T\s*:\s*([^\n]*)/);
      if (ttMatch && ttMatch[1].trim()) {
        const ttValue = ttMatch[1].trim().replace(/\s*days/, "");
        form.setFieldValue("transitTime", ttValue);
      }

      // 提取航班计划
      const scheduleMatch = content.match(/Schedule\s*:\s*([^\n]*)/);
      if (scheduleMatch && scheduleMatch[1].trim()) {
        form.setFieldValue("schedule", scheduleMatch[1].trim());
      }

      // 提取空运费
      const afMatch = content.match(/A\/F\s*:\s*([^\n]*)/);
      if (afMatch && afMatch[1].trim()) {
        const afValue = afMatch[1].trim().replace(/\s*CNY\/KG.*/, "");
        form.setFieldValue("airFreight", afValue);
      }

      // 提取计费重量
      const cwMatch = content.match(/CW\s*:\s*([^\n]*)/);
      if (cwMatch && cwMatch[1].trim()) {
        const cwValue = cwMatch[1].trim().replace(/\s*KG.*/, "");
        form.setFieldValue("chargeWeight", cwValue);
      }

      // 提取其他费用...
    } catch (error) {
      console.error("提取字段失败:", error);
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      await form.validateFields(); // 验证表单字段

      // 构建保存数据（仅用于注释的API调用）
      // const values = form.getFieldsValue();
      // const saveData = {
      //   inquiryid: record.inquiryid,
      //   quotationContent: values.quotationContent,
      //   status: "quoted", // 更新状态为已报价
      //   validityDate: formatDateToTimestamp(values.validityDate),
      //   // 其他需要保存的字段
      // };

      // 模拟API调用延迟
      setTimeout(() => {
        message.success(
          t("manualQuotation.quotationEditor.messages.saveSuccess")
        );
        setEditMode(false);
        onSaveSuccess(); // 刷新列表
        setLoading(false);
      }, 1000);

      // 实际API调用（暂时注释）
      /*
      // 保存报价单
      const res = await saveManualQuotation(saveData);
      if (res.data?.resultCode === 200) {
        // 更新询价状态
        await updateInquiryStatus({
          inquiryid: record.inquiryid,
          status: "quoted",
        });

        message.success(t("manualQuotation.quotationEditor.messages.saveSuccess"));
        setEditMode(false);
        onSaveSuccess(); // 刷新列表
      } else {
        message.error(res.data?.message || t("manualQuotation.quotationEditor.messages.saveFailed"));
      }
      */
    } catch (error) {
      console.error("表单验证失败:", error);
      message.error(
        t("manualQuotation.quotationEditor.validation.formValidationFailed")
      );
      setLoading(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExport = () => {
    message.success(
      t("manualQuotation.quotationEditor.messages.exportSuccess")
    );
  };

  const handleCopy = async () => {
    const content = form.getFieldValue("quotationContent");
    if (content) {
      const success = await copyToClipboard(content);
      if (success) {
        message.success(
          t("manualQuotation.quotationEditor.messages.copySuccess")
        );
      } else {
        message.error(t("manualQuotation.quotationEditor.messages.copyFailed"));
      }
    }
  };

  const handleGenerateQuotation = () => {
    updateQuotationContent();
  };

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={onClose}
      footer={null}
      width="90%"
      className="manual-quotation-editor-modal"
      centered
      destroyOnClose
      maskClosable={false}
    >
      <div className="quotation-editor">
        <div className="editor-header">
          <div className="header-left">
            <Title level={3}>
              <FileTextOutlined /> {t("manualQuotation.quotationEditor.title")}
            </Title>
            <Text type="secondary">
              {t("manualQuotation.quotationEditor.inquiryNumber")}:{" "}
              {record?.inquiryid}
            </Text>
          </div>
          <div className="header-right">
            <Space size="middle">
              {editMode ? (
                <>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={handleSave}
                    loading={loading}
                  >
                    {t("manualQuotation.quotationEditor.buttons.saveQuotation")}
                  </Button>
                  <Button
                    type="default"
                    icon={<DollarOutlined />}
                    onClick={handleGenerateQuotation}
                  >
                    {t(
                      "manualQuotation.quotationEditor.buttons.generateQuotation"
                    )}
                  </Button>
                  <Button
                    danger
                    icon={<CloseOutlined />}
                    onClick={() => setEditMode(false)}
                  >
                    {t("manualQuotation.quotationEditor.buttons.cancelEdit")}
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    type="primary"
                    ghost
                    icon={<EditOutlined />}
                    onClick={() => setEditMode(true)}
                  >
                    {t("manualQuotation.quotationEditor.buttons.editContent")}
                  </Button>
                  <Button
                    icon={<PrinterOutlined />}
                    onClick={handlePrint}
                    title={t("manualQuotation.quotationEditor.tooltips.print")}
                  >
                    {t("manualQuotation.quotationEditor.buttons.print")}
                  </Button>
                  <Button
                    type="default"
                    icon={<DownloadOutlined />}
                    onClick={handleExport}
                    title={t("manualQuotation.quotationEditor.tooltips.export")}
                  >
                    {t("manualQuotation.quotationEditor.buttons.export")}
                  </Button>
                  <Button
                    type="default"
                    icon={<CopyOutlined />}
                    onClick={handleCopy}
                    title={t("manualQuotation.quotationEditor.tooltips.copy")}
                  >
                    {t("manualQuotation.quotationEditor.buttons.copy")}
                  </Button>
                </>
              )}
            </Space>
          </div>
        </div>

        {/* <Divider /> */}

        <Form
          form={form}
          layout="vertical"
          className="quotation-form"
          disabled={!editMode}
        >
          <Row gutter={24}>
            <Col span={16}>
              <Card
                className="quotation-card"
                title={t(
                  "manualQuotation.quotationEditor.sections.quotationDetails"
                )}
                extra={
                  <Tooltip
                    title={t(
                      "manualQuotation.quotationEditor.tooltips.alignmentInfo"
                    )}
                  >
                    <Button
                      type="text"
                      icon={<InfoCircleOutlined />}
                      size="small"
                    />
                  </Tooltip>
                }
              >
                <Form.Item
                  name="quotationContent"
                  rules={[
                    {
                      required: true,
                      message: t(
                        "manualQuotation.quotationEditor.validation.quotationContentRequired"
                      ),
                    },
                  ]}
                >
                  <TextArea
                    rows={16}
                    className="quotation-textarea"
                    placeholder={t(
                      "manualQuotation.quotationEditor.placeholders.quotationContent"
                    )}
                    style={{
                      whiteSpace: "pre-wrap",
                      fontFamily: "monospace",
                    }}
                  />
                </Form.Item>
              </Card>
            </Col>
            <Col span={8}>
              <Card
                title={t(
                  "manualQuotation.quotationEditor.sections.quotationInfo"
                )}
                className="quotation-info-card"
              >
                <Form.Item
                  label={t(
                    "manualQuotation.quotationEditor.fields.quotationContent"
                  )}
                  name="inquiryid"
                >
                  <Input disabled />
                </Form.Item>

                <Form.Item
                  label={t(
                    "manualQuotation.quotationEditor.fields.validityDate"
                  )}
                  name="validityDate"
                  rules={[
                    {
                      required: true,
                      message: t(
                        "manualQuotation.quotationEditor.validation.validityDateRequired"
                      ),
                    },
                  ]}
                >
                  <DatePicker style={{ width: "100%" }} />
                </Form.Item>

                <Divider orientation="left">
                  {t("manualQuotation.quotationEditor.sections.basicInfo")}
                </Divider>

                <Form.Item
                  label={t("manualQuotation.quotationEditor.fields.airline")}
                  name="airline"
                >
                  <Input
                    placeholder={t(
                      "manualQuotation.quotationEditor.placeholders.airline"
                    )}
                  />
                </Form.Item>

                <Form.Item
                  label={t("manualQuotation.quotationEditor.fields.route")}
                  name="route"
                >
                  <Input
                    placeholder={t(
                      "manualQuotation.quotationEditor.placeholders.route"
                    )}
                  />
                </Form.Item>

                <Form.Item
                  label={t(
                    "manualQuotation.quotationEditor.fields.transitTime"
                  )}
                  name="transitTime"
                >
                  <Input
                    placeholder={t(
                      "manualQuotation.quotationEditor.placeholders.transitTime"
                    )}
                  />
                </Form.Item>

                <Form.Item
                  label={t("manualQuotation.quotationEditor.fields.schedule")}
                  name="schedule"
                >
                  <Input
                    placeholder={t(
                      "manualQuotation.quotationEditor.placeholders.schedule"
                    )}
                  />
                </Form.Item>

                <Divider orientation="left">
                  {t("manualQuotation.quotationEditor.sections.priceInfo")}
                </Divider>

                <Form.Item
                  label={t("manualQuotation.quotationEditor.fields.airFreight")}
                  name="airFreight"
                >
                  <Input
                    placeholder={t(
                      "manualQuotation.quotationEditor.placeholders.airFreight"
                    )}
                  />
                </Form.Item>

                <Form.Item
                  label={t(
                    "manualQuotation.quotationEditor.fields.chargeWeight"
                  )}
                  name="chargeWeight"
                >
                  <Input
                    placeholder={t(
                      "manualQuotation.quotationEditor.placeholders.chargeWeight"
                    )}
                  />
                </Form.Item>

                <Form.Item
                  label={t(
                    "manualQuotation.quotationEditor.fields.terminalCharge"
                  )}
                  name="terminalCharge"
                >
                  <Input
                    placeholder={t(
                      "manualQuotation.quotationEditor.placeholders.terminalCharge"
                    )}
                  />
                </Form.Item>

                <Form.Item
                  label={t(
                    "manualQuotation.quotationEditor.fields.terminalChargeMin"
                  )}
                  name="terminalChargeMin"
                >
                  <Input
                    placeholder={t(
                      "manualQuotation.quotationEditor.placeholders.terminalChargeMin"
                    )}
                  />
                </Form.Item>

                <Form.Item
                  label={t("manualQuotation.quotationEditor.fields.handleFee")}
                  name="handleFee"
                >
                  <Input
                    placeholder={t(
                      "manualQuotation.quotationEditor.placeholders.handleFee"
                    )}
                  />
                </Form.Item>

                <Form.Item
                  label={t("manualQuotation.quotationEditor.fields.customs")}
                  name="customs"
                >
                  <Input
                    placeholder={t(
                      "manualQuotation.quotationEditor.placeholders.customs"
                    )}
                  />
                </Form.Item>

                <Form.Item
                  label={t("manualQuotation.quotationEditor.fields.pickupFee")}
                  name="pickupFee"
                >
                  <Input
                    placeholder={t(
                      "manualQuotation.quotationEditor.placeholders.pickupFee"
                    )}
                  />
                </Form.Item>

                <Form.Item
                  label={t(
                    "manualQuotation.quotationEditor.fields.airDocCharge"
                  )}
                  name="airDocCharge"
                >
                  <Input
                    placeholder={t(
                      "manualQuotation.quotationEditor.placeholders.airDocCharge"
                    )}
                  />
                </Form.Item>

                <Form.Item
                  label={t("manualQuotation.quotationEditor.fields.ensCharge")}
                  name="ensCharge"
                >
                  <Input
                    placeholder={t(
                      "manualQuotation.quotationEditor.placeholders.ensCharge"
                    )}
                  />
                </Form.Item>

                <Form.Item
                  label={t(
                    "manualQuotation.quotationEditor.fields.otherCharges"
                  )}
                  name="otherCharges"
                >
                  <Input
                    placeholder={t(
                      "manualQuotation.quotationEditor.placeholders.otherCharges"
                    )}
                  />
                </Form.Item>

                <Form.Item
                  label={t("manualQuotation.quotationEditor.fields.remarks")}
                  name="remarks"
                >
                  <TextArea
                    rows={3}
                    placeholder={t(
                      "manualQuotation.quotationEditor.placeholders.remarks"
                    )}
                  />
                </Form.Item>
              </Card>
            </Col>
          </Row>
        </Form>
      </div>
    </Modal>
  );
};

export default QuotationEditor;
