import React, {
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import { Table, message } from "antd";
import type { TableProps, PaginationProps, TablePaginationConfig } from "antd";
import type { FilterValue, SorterResult } from "antd/es/table/interface";
import "./index.less";

// 定义API请求参数接口
export interface ApiParams {
  current?: number;
  pageSize?: number;
  sortField?: string;
  sortOrder?: string;
  [key: string]: any; // 允许其他任意参数
}

// 定义API响应接口
export interface ApiResponse<T> {
  resultCode: number;
  message?: string;
  data: {
    list: T[];
    total: number;
    [key: string]: any;
  };
}

// 定义表格参数接口
export interface TableParams {
  pagination?: TablePaginationConfig;
  sortField?: string;
  sortOrder?: string;
  filters?: Record<string, FilterValue | null>;
}

// 定义表格组件引用类型
export interface TableComRef {
  refresh: () => void; // 刷新表格数据
  reset: () => void; // 重置表格参数并刷新数据
  getParams: () => TableParams; // 获取当前表格参数
  setParams: (params: TableParams) => void; // 设置表格参数并刷新数据
  getData: () => any[]; // 获取当前数据
}

// 使用泛型 T，使组件支持任意数据结构
export interface TableComProps<T> {
  columns: TableProps<T>["columns"];
  data?: T[]; // 前端分页时需要传入数据
  pagination?: false | PaginationProps;
  rowKey?: TableProps<T>["rowKey"];
  filterRender?: () => React.ReactNode;

  // 分页相关属性
  serverPagination?: boolean; // 是否使用后端分页
  size?: TableProps<any>["size"];

  // 后端分页时需要的属性
  api?: (params: ApiParams) => Promise<ApiResponse<T>>; // API请求函数
  extraParams?: Record<string, any>; // 额外的API参数
  transformData?: (data: any) => T[]; // 数据转换函数
  onDataLoaded?: (data: T[], total: number) => void; // 数据加载完成回调
}

// 内部组件实现
function TableComInner<T extends object>(
  props: TableComProps<T>,
  ref: React.ForwardedRef<TableComRef>
) {
  const {
    columns,
    data: externalData = [],
    pagination = { pageSize: 10 },
    rowKey = "key",
    filterRender = () => null,
    serverPagination = false, // 默认使用前端分页
    size = "middle",

    // 后端分页相关属性
    api,
    extraParams = {},
    transformData,
    onDataLoaded,
  } = props;

  const [internalData, setInternalData] = useState<T[]>([]);
  const [internalTotal, setInternalTotal] = useState<number>(0);
  const [internalLoading, setInternalLoading] = useState<boolean>(false);
  const [tableHeight, setTableHeight] = useState(0);

  // 表格参数状态
  const [tableParams, setTableParams] = useState<TableParams>({
    pagination: {
      current: 1,
      pageSize: 10,
      ...(typeof pagination === "object" ? pagination : {}),
    },
    filters: {},
  });

  // 确定实际使用的数据、加载状态和总数
  const data = serverPagination ? internalData : externalData;
  const loading = serverPagination ? internalLoading : false;
  const total = serverPagination ? internalTotal : externalData.length;

  // 内部数据获取函数
  const fetchTableData = async (params: TableParams = {}) => {
    if (!api) return;

    setInternalLoading(true);

    try {
      // 合并当前表格参数和新参数
      const newParams = {
        // 基础属性直接从 params 获取，如果不存在则使用 tableParams 中的值
        sortField: params.sortField || tableParams.sortField,
        sortOrder: params.sortOrder || tableParams.sortOrder,

        // 深度合并 pagination 对象
        pagination: {
          ...tableParams.pagination,
          ...params.pagination,
        },

        // 深度合并 filters 对象
        filters: {
          ...tableParams.filters,
          ...params.filters,
        },
      };

      // 更新表格参数状态
      setTableParams(newParams);

      // 构建API查询参数
      const apiParams: ApiParams = {
        pageindex: newParams.pagination?.current || 1,
        pagesize: newParams.pagination?.pageSize || 10,
        sortField: newParams.sortField,
        sortOrder: newParams.sortOrder,
        ...extraParams, // 添加额外参数
      };

      // 处理过滤条件
      if (newParams.filters) {
        // 获取搜索文本
        const searchText = newParams.filters.searchText
          ? newParams.filters.searchText[0]
          : undefined;

        if (searchText) {
          apiParams.searchText = searchText;
        }

        // 添加其他过滤条件
        Object.entries(newParams.filters)
          .filter(([key]) => key !== "searchText")
          .forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
              apiParams[key] = value;
            }
          });
      }

      // 调用API获取数据
      const response = await api(apiParams);
      const { data } = response;

      if (data.resultCode === 200) {
        // 获取数据列表和总数
        let list = data.data || [];
        const total = data.totalNum || 0;

        // 如果提供了数据转换函数，则使用它转换数据
        if (transformData) {
          list = transformData(list);
        }

        // 更新内部状态
        setInternalData(list);
        setInternalTotal(total);

        // 调用数据加载完成回调
        if (onDataLoaded) {
          onDataLoaded(list, total);
        }
      } else {
        message.error(response.message || "获取数据失败");
      }
    } catch (error) {
      console.error("获取数据出错:", error);
      message.error("获取数据出错");
    } finally {
      setInternalLoading(false);
    }
  };

  // 首次加载时获取数据
  useEffect(() => {
    if (serverPagination && api) {
      fetchTableData();
    }
  }, []);

  // 处理表格变化事件
  const handleTableChange = (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<T> | SorterResult<T>[]
  ) => {
    // 更新表格参数
    const newParams: TableParams = {
      pagination,
      filters,
      ...((sorter as SorterResult<T>).field
        ? {
            sortField: (sorter as SorterResult<T>).field as string,
            sortOrder: (sorter as SorterResult<T>).order as string,
          }
        : {}),
    };

    // 如果使用后端分页，则调用获取数据的函数
    if (serverPagination && api) {
      fetchTableData(newParams);
    } else {
      setTableParams(newParams);
    }
  };

  // 构建分页配置
  const paginationConfig = serverPagination
    ? {
        // 默认分页配置
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total: number) => `共 ${total} 条记录`,
        ...tableParams.pagination,
        // 外部传入的配置（优先级最高）
        ...(typeof pagination === "object" ? pagination : {}),
        // 总数必须设置
        total: total || 0,
      }
    : pagination;

  // 暴露给外部的方法
  useImperativeHandle(
    ref,
    () => ({
      // 刷新表格数据
      refresh: () => {
        if (serverPagination && api) {
          fetchTableData(tableParams);
        }
      },
      // 重置表格参数并刷新数据
      reset: () => {
        const resetParams = {
          pagination: {
            current: 1,
            pageSize:
              (typeof pagination === "object" ? pagination.pageSize : 10) || 10,
          },
          filters: {},
        };

        if (serverPagination && api) {
          fetchTableData(resetParams);
        } else {
          setTableParams(resetParams);
        }
      },
      // 获取当前表格参数
      getParams: () => tableParams,
      // 设置表格参数并刷新数据
      setParams: (params: TableParams) => {
        if (serverPagination && api) {
          fetchTableData(params);
        } else {
          setTableParams(params);
        }
      },
      // 获取当前数据
      getData: () => data,
    }),
    [tableParams, data, serverPagination, api]
  );

  return (
    <div className="table_com">
      {filterRender()}
      <Table<T>
        className={`standard-table scroll-table ${data.length === 0 ? "empty-table" : ""}`}
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={paginationConfig}
        rowKey={rowKey}
        size={size}
        onChange={handleTableChange}
        scroll={{
          // x: supplyPriceData.length > 0 ? 1300 : undefined, // 增加表格宽度
          y: 495,
        }}
      />
    </div>
  );
}

// 使用 forwardRef 包装组件，使其可以接收 ref
const TableCom = forwardRef(TableComInner) as <T extends object>(
  props: TableComProps<T> & { ref?: React.ForwardedRef<TableComRef> }
) => ReturnType<typeof React.createElement>;

export default TableCom;
