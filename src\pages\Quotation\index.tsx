import { useEffect, useState } from "react";
import {
  getInquiryByCondition,
  delInquiry,
  getUsersByDepartment,
} from "./services";
import { Table, Space, Button, Form, message, Badge, Select, Tabs } from "antd";
import "./index.less";
import type { TableProps, GetProp } from "antd";
import {
  PlusOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  InboxOutlined,
  MailOutlined,
} from "@ant-design/icons";
import AddQuotation from "./components/AddQuotation";
import EmailExtractModal from "./components/EmailExtractModal";
import { useNavigate, useLocation } from "react-router-dom";
import { useAppSelector } from "@/store/hooks";
import dayjs from "dayjs";
import { createColumns } from "./columns";
import useBaseData from "@/hooks/useBaseData";
import { useTranslation } from "react-i18next";

type TablePaginationConfig = Exclude<
  GetProp<TableProps, "pagination">,
  boolean
>;

interface DataType {
  inquiryid: string;
  freightmethod: string; // 货运方式
  goodstype: string; // 货物类型
  tradeterms: string; // 贸易术语
  companyname: string; // 公司名
  inquirer: string; // 询价人
  inquirytime: string; // 询价时间
  shippedplace: string; // 发货地
  originport: string; // POL: 起始港
  unloadingport: string; // POD: 目的港
  grossweight: number; // 货物毛重
  goodsvolume: number; // 货物体积
  singlemaxweight?: number | null; // 单件货最大重量
  isbrand: boolean; // 是否品牌货
  ensurecabin: boolean; // 保舱
  isvalidity: boolean; // 要求ETD(发货日期)
  shipmentdate?: string; // 发货日期
  packagetype: string; // 包装形式
  cargolength?: number; // 货物长度
  cargowidth?: number; // 货物宽
  cargoheight?: number; // 货物高
  specialcargo?: string; // 特殊货物
  inquirycreatetime: string; // 记录生成时间
  inquirystate: number; // 询价状态
  userid: number; // 用户ID
  departmentid: number; // 所在部门id
  status?: "quoted" | "unquoted" | "deal" | "nodeal"; // 状态：已报价、未报价、已成交、未成交
  [key: string]: any;
}

const Quotation: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  const [quotationData, setQuotationData] = useState<DataType[]>([]);
  const [open, setOpen] = useState<boolean>(false);
  const [method, setMethod] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("all");
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const { t } = useTranslation();
  const [loading, setLoading] = useState<boolean>(false);
  const [departmentUsers, setDepartmentUsers] = useState<any[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [emailExtractModalOpen, setEmailExtractModalOpen] =
    useState<boolean>(false);
  const [currentFilters, setCurrentFilters] = useState<any>({});
  const [tableFilters, setTableFilters] = useState<any>({});
  const [tableKey, setTableKey] = useState<number>(0);
  const { user } = useAppSelector((state) => state.user);
  const {
    packageTypeOptions,
    portOptions,
    loadAll,
    shipmentPlaceOptions,
    specialItemOptions,
  } = useBaseData();
  const setQuotationList = async (
    options: {
      userId?: number | null;
      pageParams?: any;
      tabKey?: string;
      filterParams?: any;
      resetFilters?: boolean;
    } = {}
  ) => {
    const { userId, pageParams, tabKey, filterParams, resetFilters } = options;
    try {
      setLoading(true);

      if (userId) {
        const currentTab = tabKey || activeTab;
        let inquirystate: number | undefined;
        switch (currentTab) {
          case "unquoted":
            inquirystate = 0; // 未报价
            break;
          case "nodeal":
            inquirystate = 1; // 未成交
            break;
          case "deal":
            inquirystate = 2; // 已成交
            break;
          case "all":
          default:
            inquirystate = undefined; // 全部时不传
            break;
        }

        // 如果不重置筛选且没有传入新的筛选参数，则使用当前保存的筛选状态
        const finalFilterParams = resetFilters
          ? {}
          : filterParams || currentFilters;

        // 如果重置筛选，清空保存的筛选状态
        if (resetFilters) {
          setCurrentFilters({});
          setTableFilters({});
          setTableKey((prev) => prev + 1);
        }

        const params = {
          userid: userId,
          departmentid: user?.departmentid,
          pageindex: pageParams?.current || pagination.current || 1,
          pagesize: pageParams?.pageSize || pagination.pageSize || 10,
          ...(inquirystate !== undefined && { inquirystate }), // 只有当inquirystate有值时才添加到参数中
          ...finalFilterParams,
        };

        const res = await getInquiryByCondition(params);
        const { data } = res;
        if (data?.resultCode === 200) {
          setQuotationData(data.data || []);
          setPagination({
            ...pagination,
            current: params.pageindex,
            pageSize: params.pagesize,
            total: data.totalNum || 0,
          });
        } else {
          setQuotationData([]);
          message.error(t("quotationTable.messages.getListFailed"));
        }
        setLoading(false);
      }
    } catch (e) {
      setLoading(false);
    }
  };

  const handleUserChange = (userId: number | null) => {
    if (userId) {
      setSelectedUserId(userId);
      setQuotationList({ userId, resetFilters: true });
    } else {
      setSelectedUserId(null);
      setQuotationList({ userId: user?.userid, resetFilters: true });
    }
  };

  // 获取部门下的用户列表
  const fetchDepartmentUsers = async () => {
    try {
      if (user?.departmentid) {
        const res = await getUsersByDepartment({
          departmentid: user.departmentid,
        });

        if (res.data?.resultCode === 200) {
          // 过滤掉当前用户自己
          const users = res.data.data.filter(
            (u: any) => u.userid !== user.userid
          );
          setDepartmentUsers(users);
        } else {
          setDepartmentUsers([]);
        }
      }
    } catch (error) {
      setDepartmentUsers([]);
    }
  };

  useEffect(() => {
    setQuotationList({ userId: user?.userid, resetFilters: true });
    // 如果是询价部门主管，获取部门下的用户列表
    if (user?.useridentity === 3) {
      fetchDepartmentUsers();
    }
    loadAll();

    // 检查是否有预填数据传入
    const state = location.state as any;
    if (state?.action === "create" && state?.prefilledData) {
      setMethod("add");
      setOpen(true);
      // 延迟填充表单，确保抽屉已经打开
      setTimeout(() => {
        form.setFieldsValue(state.prefilledData);
      }, 100);
    }
  }, [location.state]);

  const handleQueryPrice = (record: DataType) => {
    navigate("/supply_price", { state: record });
  };

  const handleManualQuote = (_record: DataType) => {
    message.success(t("quotationTable.messages.manualQuoteSuccess"));
  };

  const handleDel = async (record: DataType) => {
    const res = await delInquiry({
      inquiryid: record.inquiryid,
    });
    const { data } = res;
    if (data.resultCode === 200) {
      setQuotationList({ userId: selectedUserId || user?.userid });
      message.success(t("quotationTable.messages.deleteSuccess"));
    } else {
      message.error(data.message || t("quotationTable.messages.deleteFailed"));
    }
  };

  const handleCancel = () => {
    // 取消操作的处理逻辑
  };

  const showDrawer = (method: string) => {
    setMethod(method);
    setOpen(true);
  };

  const handleEdit = (record: DataType) => {
    showDrawer("edit");
    const newItem = {
      ...record,
      inquirytime: dayjs(record?.inquirytime),
      shipmentdate: record?.shipmentdate
        ? dayjs(record?.shipmentdate)
        : undefined,
      inquirycreatetime: record?.inquirycreatetime
        ? dayjs(record?.inquirycreatetime)
        : undefined,
      specialcargo: record?.specialcargo?.split(","),
      originport: record?.originport?.split("/"),
    };
    form.setFieldsValue(newItem);
  };

  const handleAdd = () => {
    showDrawer("add");
    form.resetFields();
    setCurrentFilters({});
    setTableFilters({});
    setTableKey((prev) => prev + 1);
  };

  const handleEmailExtract = () => {
    setEmailExtractModalOpen(true);
  };

  const handleEmailExtractConfirm = () => {
    setQuotationList({ userId: user?.userid, resetFilters: true });
  };

  const columns = createColumns({
    handleEdit,
    handleDel,
    handleCancel,
    handleQueryPrice,
    handleManualQuote,
    user,
    selectedUserId,
    packageTypeOptions,
    portOptions,
    shipmentPlaceOptions,
    specialItemOptions,
  });

  const columnsWithFilters = columns?.map((column: any) => {
    if (column.key && tableFilters[column.key]) {
      return {
        ...column,
        filteredValue: tableFilters[column.key],
      };
    }
    return column;
  });

  const handleTableChange: TableProps<DataType>["onChange"] = (
    pagination,
    filters,
    _sorter
  ) => {
    const filterParams: any = {};

    if (filters?.inquirytime && filters.inquirytime.length > 0) {
      const dateFilters = filters.inquirytime as string[];

      dateFilters.forEach((filter) => {
        if (filter.startsWith("leftinquirytime=")) {
          filterParams.leftinquirytime = parseInt(
            filter.replace("leftinquirytime=", "")
          );
        } else if (filter.startsWith("rightinquirytime=")) {
          filterParams.rightinquirytime = parseInt(
            filter.replace("rightinquirytime=", "")
          );
        }
      });
    }

    // 处理发货地筛选
    if (filters?.shippedplace && filters.shippedplace.length > 0) {
      filterParams.shippedplace = filters.shippedplace.join(",");
    }

    // 处理起始港筛选
    if (filters?.originport && filters.originport.length > 0) {
      filterParams.originport = filters.originport.join(",");
    }

    // 处理目的港筛选
    if (filters?.unloadingport && filters.unloadingport.length > 0) {
      filterParams.unloadingport = filters.unloadingport.join(",");
    }

    // 处理货物类型筛选
    if (filters?.goodstype && filters.goodstype.length > 0) {
      filterParams.goodstype = filters.goodstype.join(",");
    }

    // 处理包装形式筛选
    if (filters?.packagetype && filters.packagetype.length > 0) {
      filterParams.packagetype = filters.packagetype.join(",");
    }

    // 处理特殊货物筛选
    if (filters?.specialcargo && filters.specialcargo.length > 0) {
      filterParams.specialcargo = filters.specialcargo.join(",");
    }

    // 处理是否品牌货
    if (filters.isbrand && filters.isbrand.length > 0) {
      filterParams.isbrand = filters.isbrand[0];
    }

    // 处理是否保舱
    if (filters.ensurecabin && filters.ensurecabin.length > 0) {
      filterParams.ensurecabin = filters.ensurecabin[0];
    }

    // 处理是否要求ETD
    if (filters.isvalidity && filters.isvalidity.length > 0) {
      filterParams.isvalidity = filters.isvalidity[0];
    }

    // 处理公司名筛选
    if (filters.companyname && filters.companyname.length > 0) {
      filterParams.companyname = filters.companyname[0];
    }

    // 处理询价人筛选
    if (filters.inquirer && filters.inquirer.length > 0) {
      filterParams.inquirer = filters.inquirer[0];
    }

    // 保存当前筛选状态
    setCurrentFilters(filterParams);
    // 保存表格筛选UI状态（用于UI显示），创建副本避免意外修改
    setTableFilters({ ...filters });

    // 调用查询接口，传递筛选参数
    setQuotationList({
      userId: selectedUserId || user?.userid,
      pageParams: pagination,
      filterParams,
    });
  };

  const handleChangeTabs = (activeKey: string) => {
    setActiveTab(activeKey);
    const resetPagination = { ...pagination, current: 1 };
    setPagination(resetPagination);
    // 切换标签页时保持筛选状态
    setQuotationList({
      userId: selectedUserId || user?.userid,
      pageParams: resetPagination,
      tabKey: activeKey,
    });
  };

  return (
    <>
      {!open ? (
        <div className="quotation_container">
          <div className="filter_box">
            <div className="title">{t("menu.quotation")}</div>
            <Space>
              {user?.useridentity === 3 && departmentUsers.length > 0 && (
                <Select
                  placeholder={t(
                    "quotationTable.placeholders.selectDepartmentMember"
                  )}
                  style={{ width: 200 }}
                  allowClear
                  onChange={handleUserChange}
                  options={[
                    ...departmentUsers.map((user: any) => ({
                      label:
                        user.email ||
                        user.fullname ||
                        `${t("common.user")}${user.userid}`,
                      value: user.userid,
                    })),
                  ]}
                />
              )}
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
              >
                {t("common.createinquiry")}
              </Button>
              <Button
                type="default"
                icon={<MailOutlined />}
                onClick={handleEmailExtract}
              >
                {t("common.quickinquiry")}
              </Button>
            </Space>
          </div>

          {/* 标签页区域 */}
          <div className="tabs-container">
            <Tabs
              activeKey={activeTab}
              onChange={handleChangeTabs}
              type="card"
              className="custom-tabs"
              items={[
                {
                  key: "all",
                  label: (
                    <span className="tab-label">
                      <InboxOutlined />
                      {t("common.all")}
                      {activeTab === "all" && (
                        <Badge
                          count={pagination?.total}
                          className="tab-badge"
                        />
                      )}
                    </span>
                  ),
                },
                {
                  key: "unquoted",
                  label: (
                    <span className="tab-label">
                      <ClockCircleOutlined />
                      {t("common.notquoted")}
                      {activeTab === "unquoted" && (
                        <Badge
                          count={pagination?.total}
                          className="tab-badge"
                        />
                      )}
                    </span>
                  ),
                },
                {
                  key: "deal",
                  label: (
                    <span className="tab-label">
                      <DollarOutlined />
                      {t("common.closed")}
                      {activeTab === "deal" && (
                        <Badge
                          count={pagination?.total}
                          className="tab-badge"
                        />
                      )}
                    </span>
                  ),
                },
                {
                  key: "nodeal",
                  label: (
                    <span className="tab-label">
                      <CloseCircleOutlined />
                      {t("common.notclosed")}
                      {activeTab === "nodeal" && (
                        <Badge
                          count={pagination?.total}
                          className="tab-badge"
                        />
                      )}
                    </span>
                  ),
                },
              ]}
            />
          </div>

          {/* 表格区域 */}
          <div className="table-container">
            <Table<DataType>
              key={`table-${tableKey}`}
              className={"scroll-table"}
              columns={columnsWithFilters}
              dataSource={quotationData}
              pagination={{
                ...pagination,
                showTotal: (total) => t("common.total", { count: total }),
                showQuickJumper: true,
                showSizeChanger: true,
                size: "small",
              }}
              loading={loading}
              onChange={handleTableChange}
              size="middle"
              rowKey="inquiryid"
              tableLayout="fixed"
              scroll={{
                y: "57vh",
                x: 2500,
              }}
            />
          </div>
        </div>
      ) : (
        <AddQuotation
          open={open}
          setOpen={setOpen}
          method={method}
          form={form}
          setQuotationList={setQuotationList}
          prefilledData={(location.state as any)?.prefilledData}
          inquiryRecords={(location.state as any)?.inquiryRecords}
          isMultipleRecords={(location.state as any)?.isMultipleRecords}
        />
      )}

      {/* 快捷提取询价模态框 */}
      <EmailExtractModal
        open={emailExtractModalOpen}
        onClose={() => setEmailExtractModalOpen(false)}
        onConfirm={handleEmailExtractConfirm}
      />
    </>
  );
};

export default Quotation;
