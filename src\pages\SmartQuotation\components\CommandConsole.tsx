import React, { useRef, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Button,
  Select,
  Input,
  DatePicker,
  InputNumber,
  Space,
} from "antd";
import {
  RocketOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import { Canvas } from "@react-three/fiber";
import { OrbitControls } from "@react-three/drei";
import SpaceshipBackground from "./SpaceshipBackground";
import DataVisualization from "./DataVisualization";
import { InteractionState, InteractionStep } from "../index";

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface CommandConsoleProps {
  interactionState: InteractionState;
  interactionSteps: InteractionStep[];
  onUserInput: (key: string, value: any) => void;
  onNextStep: () => void;
  onPrevStep: () => void;
  onGenerateQuotation: () => void;
}

const CommandConsole: React.FC<CommandConsoleProps> = ({
  interactionState,
  interactionSteps,
  onUserInput,
  onNextStep,
  onPrevStep,
  onGenerateQuotation,
}) => {
  const { currentStep, completedSteps, userInputs } = interactionState;
  const currentStepData = interactionSteps[currentStep];
  const isLastStep = currentStep === interactionSteps.length - 1;
  const isFirstStep = currentStep === 0;

  // 渲染步骤指示器
  const renderStepIndicator = () => {
    return (
      <div className="step-indicator">
        {interactionSteps.map((step, index) => (
          <div
            key={step.id}
            className={`step-item ${index === currentStep ? "active" : ""} ${
              completedSteps.includes(index) ? "completed" : ""
            }`}
          >
            <div className="step-number">
              {completedSteps.includes(index) ? <CheckOutlined /> : index + 1}
            </div>
            <div className="step-label">{step.title}</div>
          </div>
        ))}
      </div>
    );
  };

  // 渲染输入区域
  const renderInputArea = () => {
    const { inputType, options, key } = currentStepData;

    switch (inputType) {
      case "select":
        return (
          <Select
            style={{ width: "100%" }}
            placeholder="请选择"
            value={userInputs[key]}
            onChange={(value) => onUserInput(key, value)}
            size="large"
          >
            {options?.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );

      case "input":
        return (
          <Input
            placeholder="请输入"
            value={userInputs[key]}
            onChange={(e) => onUserInput(key, e.target.value)}
            size="large"
          />
        );

      case "dateRange":
        return (
          <RangePicker
            style={{ width: "100%" }}
            value={userInputs[key]}
            onChange={(dates) => onUserInput(key, dates)}
            size="large"
          />
        );

      case "numberInput":
        return (
          <Space direction="vertical" style={{ width: "100%" }}>
            <InputNumber
              style={{ width: "100%" }}
              placeholder="货物重量 (kg)"
              value={userInputs[key]?.weight}
              onChange={(value) =>
                onUserInput(key, { ...userInputs[key], weight: value })
              }
              size="large"
              addonAfter="kg"
            />
            <InputNumber
              style={{ width: "100%" }}
              placeholder="货物体积 (cbm)"
              value={userInputs[key]?.volume}
              onChange={(value) =>
                onUserInput(key, { ...userInputs[key], volume: value })
              }
              size="large"
              addonAfter="cbm"
            />
          </Space>
        );

      case "multiSelect":
        return (
          <Select
            mode="multiple"
            style={{ width: "100%" }}
            placeholder="请选择"
            value={userInputs[key]}
            onChange={(value) => onUserInput(key, value)}
            size="large"
          >
            {options?.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );

      case "visualization":
        return (
          <div className="visualization-area">
            <DataVisualization
              type={currentStepData.visualizationType || "flowchart"}
              data={userInputs}
              onDataChange={(key, value) => onUserInput(key, value)}
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="command-console-container">
      {/* 3D背景 */}
      <div className="canvas-container">
        <Canvas
          camera={{ position: [0, 0, 10], fov: 40 }}
          style={{ width: "100%", height: "100%" }}
          gl={{ antialias: true, alpha: true }}
        >
          <color attach="background" args={["#f0f7ff"]} />
          <fog attach="fog" args={["#f0f7ff", 15, 40]} />
          <SpaceshipBackground />
          <OrbitControls
            enableZoom={false}
            enablePan={false}
            enableRotate={true}
            autoRotate={true}
            autoRotateSpeed={0.5}
          />
        </Canvas>
      </div>

      {/* 控制台头部 */}
      <div className="console-header">
        <div className="panel-title">
          <RocketOutlined className="panel-icon" />
          <Title level={4}>智能报价控制台</Title>
        </div>
        <Text className="panel-subtitle">
          通过交互式控制台，AI将引导您获取最佳报价
        </Text>
      </div>

      {/* 控制台内容区 */}
      <div className="console-content">
        {/* 步骤指示器 */}
        {renderStepIndicator()}

        {/* 交互区域 */}
        <div className="interaction-area">
          <div className="step-title">{currentStepData.title}</div>
          <div className="step-description">{currentStepData.description}</div>

          <div className="input-area">{renderInputArea()}</div>

          <div className="action-buttons">
            <Button
              type="default"
              onClick={onPrevStep}
              disabled={isFirstStep}
              icon={<ArrowLeftOutlined />}
            >
              上一步
            </Button>
            {isLastStep ? (
              <Button
                type="primary"
                onClick={onGenerateQuotation}
                icon={<RocketOutlined />}
              >
                生成报价
              </Button>
            ) : (
              <Button
                type="primary"
                onClick={onNextStep}
                icon={<ArrowRightOutlined />}
              >
                下一步
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CommandConsole;
