import React, { useState } from "react";
import {
  List,
  Card,
  Typography,
  Tag,
  Button,
  Empty,
  <PERSON>lt<PERSON>,
  Modal,
  Badge,
} from "antd";
import {
  EnvironmentOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  StarOutlined,
  StarFilled,
} from "@ant-design/icons";
import "./SavedQuotations.less";

const { Text, Title, Paragraph } = Typography;

interface SavedQuotationsProps {
  quotations: any[];
  onViewDetail: (quotation: any) => void;
  onDeleteQuotation: (id: string) => void;
  onExportQuotation: (quotation: any) => void;
}

const SavedQuotations: React.FC<SavedQuotationsProps> = ({
  quotations,
  onViewDetail,
  onDeleteQuotation,
  onExportQuotation,
}) => {
  const [selectedQuotation, setSelectedQuotation] = useState<any>(null);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);

  // 处理查看详情
  const handleViewDetail = (quotation: any) => {
    onViewDetail(quotation);
  };

  // 处理删除确认
  const handleDeleteConfirm = (quotation: any) => {
    setSelectedQuotation(quotation);
    setDeleteModalVisible(true);
  };

  // 处理删除
  const handleDelete = () => {
    if (selectedQuotation) {
      onDeleteQuotation(selectedQuotation.id);
      setDeleteModalVisible(false);
      setSelectedQuotation(null);
    }
  };

  // 处理导出
  const handleExport = (quotation: any) => {
    onExportQuotation(quotation);
  };

  // 如果没有保存的报价，显示空状态
  if (quotations.length === 0) {
    return (
      <div className="saved-quotations-empty">
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无保存的报价"
        />
        <Paragraph className="empty-tip">
          使用智能报价系统获取报价后，可以将其保存在此处进行管理
        </Paragraph>
      </div>
    );
  }

  return (
    <div className="saved-quotations-container">
      <List
        dataSource={quotations}
        renderItem={(item) => (
          <List.Item key={item.id} className="quotation-list-item">
            <Card className="saved-quotation-card">
              <div className="card-header">
                <div className="airline-info">
                  <Text strong className="airline-name">
                    {item.airline}
                  </Text>
                  {item.recommended && (
                    <Badge
                      status="processing"
                      text="推荐"
                      className="recommendation-badge"
                    />
                  )}
                </div>
                <div className="favorite-action">
                  {item.favorite ? (
                    <StarFilled className="favorite-icon active" />
                  ) : (
                    <StarOutlined className="favorite-icon" />
                  )}
                </div>
              </div>

              <div className="route-info">
                <div className="route-points">
                  <Text className="origin">{item.route.origin}</Text>
                  <div className="route-arrow">→</div>
                  <Text className="destination">{item.route.destination}</Text>
                </div>
                <div className="price-tag">
                  <DollarOutlined />
                  <Text strong>{item.price}</Text>
                  <Text className="currency">{item.currency}/kg</Text>
                </div>
              </div>

              <div className="quotation-tags">
                <Tag icon={<ClockCircleOutlined />} color="blue">
                  {item.transitTime}
                </Tag>
                <Tag icon={<EnvironmentOutlined />} color="green">
                  {item.serviceLevel}
                </Tag>
              </div>

              <div className="validity-info">
                <Text type="secondary">有效期至: {item.validUntil}</Text>
              </div>

              <div className="card-actions">
                <Tooltip title="查看详情">
                  <Button
                    type="text"
                    icon={<EyeOutlined />}
                    onClick={() => handleViewDetail(item)}
                  />
                </Tooltip>
                <Tooltip title="导出报价">
                  <Button
                    type="text"
                    icon={<ExportOutlined />}
                    onClick={() => handleExport(item)}
                  />
                </Tooltip>
                <Tooltip title="删除">
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleDeleteConfirm(item)}
                  />
                </Tooltip>
              </div>
            </Card>
          </List.Item>
        )}
      />

      {/* 删除确认对话框 */}
      <Modal
        title="删除确认"
        open={deleteModalVisible}
        onOk={handleDelete}
        onCancel={() => setDeleteModalVisible(false)}
        okText="确认删除"
        cancelText="取消"
      >
        <p>确定要删除这条报价记录吗？此操作无法撤销。</p>
        {selectedQuotation && (
          <div className="delete-quotation-info">
            <Text strong>
              {selectedQuotation.route?.origin} →{" "}
              {selectedQuotation.route?.destination}
            </Text>
            <Text>
              航空公司: {selectedQuotation.airline}, 价格:{" "}
              {selectedQuotation.price} {selectedQuotation.currency}/kg
            </Text>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default SavedQuotations;
