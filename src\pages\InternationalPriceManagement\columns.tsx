import React from "react";
import {
  Button,
  Form,
  Input,
  Popconfirm,
  Space,
  Tag,
  Tooltip,
  InputNumber,
} from "antd";
import { TableProps } from "antd";
import {
  CheckOutlined,
  CloseOutlined,
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { InternationalPrice, CabinReport } from "./types";
import {
  DateRangeFilter,
  DensityRangeFilter,
  SelectFilter,
  CustomTableFilter,
} from "@/components/filters";

// ==================== 筛选选项配置区域 ====================
// 开发注意：
// 1. 这里定义了常用的筛选选项配置，避免在列定义中重复编写
// 2. 要修改选项内容，请直接修改对应的配置函数
// 3. 要添加新的选项配置，请参考现有模式创建新的配置函数
// ========================================================

// 星期选项配置 - 用于航班班期筛选
const createWeekdayOptions = (t: (key: string) => string) => [
  { text: t("internationalPriceManagement.schedules.monday"), value: "1" },
  { text: t("internationalPriceManagement.schedules.tuesday"), value: "2" },
  { text: t("internationalPriceManagement.schedules.wednesday"), value: "3" },
  { text: t("internationalPriceManagement.schedules.thursday"), value: "4" },
  { text: t("internationalPriceManagement.schedules.friday"), value: "5" },
  { text: t("internationalPriceManagement.schedules.saturday"), value: "6" },
  { text: t("internationalPriceManagement.schedules.sunday"), value: "7" },
];

// 是否中转选项配置 - 用于中转港筛选
const createTransferStatusOptions = (t: (key: string) => string) => [
  { text: t("internationalPriceManagement.transfer.yes"), value: true },
  { text: t("internationalPriceManagement.transfer.no"), value: false },
];

// ==================== 价格列配置区域 ====================
// 开发注意：
// 1. 要添加新的价格列，请在下面的 PRICE_COLUMNS 数组中添加配置
// 2. 要修改现有价格列，请直接修改对应的配置项
// 3. 所有价格列都使用统一的编辑渲染逻辑，无需重复编写
// 4. 列的顺序就是在表格中显示的顺序
// ========================================================

interface PriceColumnConfig {
  key: string;
  dataIndex: string;
  titleKey: string;
  width: number;
}

const PRICE_COLUMNS: PriceColumnConfig[] = [
  {
    key: "mPrice",
    dataIndex: "mprice",
    titleKey: "mPrice",
    width: 80,
  },
  {
    key: "nPrice",
    dataIndex: "nprice",
    titleKey: "nPrice",
    width: 80,
  },
  {
    key: "q45Price",
    dataIndex: "q45price",
    titleKey: "q45Price",
    width: 80,
  },
  {
    key: "subQ45Price",
    dataIndex: "subq45price",
    titleKey: "subQ45Price",
    width: 85,
  },
  {
    key: "subQ100Price",
    dataIndex: "subq100price",
    titleKey: "subQ100Price",
    width: 90,
  },
];

// 创建表格列定义函数参数接口
interface CreateColumnsParams {
  // 编辑相关函数
  editHandlers: {
    isEditing: (record: InternationalPrice) => boolean;
    hasEditPermission: (record?: InternationalPrice) => boolean;
    edit: (record: InternationalPrice) => void;
    save: (key: string) => void;
    cancel: () => void;
  };
  // 操作相关函数
  actionHandlers: {
    handleViewDetail: (record: InternationalPrice) => void;
    handleCopy: (record: InternationalPrice) => void;
    handleEdit: (record: InternationalPrice) => void;
    handleDel: (record: InternationalPrice) => void;
    handleCancel: () => void;
    handlePriceIncrease: (record: InternationalPrice) => void;
    handlePriceDecrease: (record: InternationalPrice) => void;
    onEditCabinReport?: (record: InternationalPrice) => void;
  };
  // 选项数据
  options: {
    airlineOptions: { label: string; value: string }[];
    portOptions: { label: string; value: string }[];
    supplierOptions: { label: string; value: string }[];
  };
  // 配置参数
  config: {
    t: (key: string, options?: any) => string;
    currentLanguage: string;
    tableFilters?: Record<string, any>;
    batchMode?: boolean;
    userIdentity?: number;
  };
}

// 创建表格列定义函数
export const createColumns = ({
  editHandlers,
  actionHandlers,
  options,
  config,
}: CreateColumnsParams): TableProps<InternationalPrice>["columns"] => {
  const { isEditing, hasEditPermission, edit, save, cancel } = editHandlers;
  const {
    handleViewDetail,
    handleCopy,
    handleEdit,
    handleDel,
    handleCancel,
    onEditCabinReport,
  } = actionHandlers;
  const { airlineOptions, portOptions, supplierOptions } = options;
  const { t, currentLanguage, tableFilters, batchMode, userIdentity } = config;
  const isEnglish = currentLanguage === "en-US";

  // 通用筛选配置
  const createFilterConfig = {
    // 固定选项筛选
    fixedOptions: (
      options: { text: string; value: any }[],
      multiple: boolean = true,
      filterKey?: string
    ) => ({
      filters: options,
      filterMultiple: multiple,
      filteredValue: tableFilters?.[filterKey || ""] || null,
    }),

    // 密度范围筛选
    densityRange: (filterKey?: string) => ({
      filterDropdown: ({ setSelectedKeys, confirm, clearFilters }: any) => (
        <DensityRangeFilter
          setSelectedKeys={setSelectedKeys}
          confirm={confirm}
          clearFilters={clearFilters!}
        />
      ),
      filteredValue: tableFilters?.[filterKey || ""] || null,
    }),

    // 文本输入筛选
    textInput: (placeholder: string, filterKey?: string) => ({
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }: any) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder={placeholder}
            value={selectedKeys[0]}
            onChange={(e) =>
              setSelectedKeys(e.target.value ? [e.target.value] : [])
            }
            onPressEnter={() => confirm()}
            style={{ width: 188, marginBottom: 8, display: "block" }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => confirm()}
              size="small"
              style={{ width: 90 }}
            >
              {t("common.confirm")}
            </Button>
            <Button
              onClick={() => {
                clearFilters && clearFilters();
                confirm();
              }}
              size="small"
              style={{ width: 90 }}
            >
              {t("common.reset")}
            </Button>
          </Space>
        </div>
      ),
      filterIcon: (filtered: boolean) => (
        <span style={{ color: filtered ? "#1890ff" : undefined }}>🔍</span>
      ),
      filteredValue: tableFilters?.[filterKey || ""] || null,
    }),

    // 下拉选择筛选
    select: (
      options: { label: string; value: string }[],
      placeholder: string,
      paramName: string,
      filterKey?: string
    ) => ({
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }: any) => (
        <SelectFilter
          setSelectedKeys={setSelectedKeys}
          selectedKeys={selectedKeys}
          confirm={confirm}
          clearFilters={clearFilters!}
          options={options}
          placeholder={placeholder}
          paramName={paramName}
          showSearch={true}
        />
      ),
      filterIcon: (filtered: boolean) => (
        <span style={{ color: filtered ? "#1890ff" : undefined }}>🔍</span>
      ),
      filteredValue: tableFilters?.[filterKey || ""] || null,
    }),

    // 日期范围筛选
    dateRange: (filterKey?: string) => ({
      filterDropdown: ({ setSelectedKeys, confirm, clearFilters }: any) => (
        <DateRangeFilter
          setSelectedKeys={setSelectedKeys}
          confirm={confirm}
          clearFilters={clearFilters!}
        />
      ),
      filteredValue: tableFilters?.[filterKey || ""] || null,
    }),

    // 自定义表格筛选 - 支持多选和单选，点击重置直接重置筛选状态
    customTableFilter: (
      options: { text: string; value: any }[],
      multiple: boolean = true,
      filterKey?: string
    ) => ({
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }: any) => (
        <CustomTableFilter
          setSelectedKeys={setSelectedKeys}
          selectedKeys={selectedKeys}
          confirm={confirm}
          clearFilters={clearFilters!}
          options={options}
          multiple={multiple}
        />
      ),
      filteredValue: tableFilters?.[filterKey || ""] || null,
    }),
  };

  // 可编辑单元格渲染
  const renderEditableCell = (
    value: any,
    record: InternationalPrice,
    fieldName: string,
    isEditing: (record: InternationalPrice) => boolean,
    hasEditPermission: (record?: InternationalPrice) => boolean,
    edit: (record: InternationalPrice) => void,
    save: (key: string) => void,
    batchMode?: boolean
  ) => {
    const editable = isEditing(record) && hasEditPermission(record);
    return editable ? (
      <Form.Item name={fieldName} style={{ margin: 0 }} initialValue={value}>
        <InputNumber
          min={0}
          precision={3}
          style={{ width: "100%" }}
          onPressEnter={() => save(record.priceid.toString())}
        />
      </Form.Item>
    ) : (
      <div
        className={`editable-cell-value-wrap ${batchMode ? "batch-mode-disabled" : ""}`}
        onClick={() => !batchMode && hasEditPermission(record) && edit(record)}
      >
        {value === null ? "-" : value}
      </div>
    );
  };

  // 验证班次格式
  const validateScheduleFormat = (
    value: string
  ): { isValid: boolean; message?: string } => {
    if (!value) return { isValid: true };

    // 检查是否以D开头
    if (!value.startsWith("D")) {
      return {
        isValid: false,
        message: t(
          "internationalPriceManagement.validation.scheduleStartWithD"
        ),
      };
    }

    // 获取D后面的数字部分
    const digits = value.slice(1);

    // 检查是否只包含1-7的数字
    const validDigits = /^[1-7]*$/.test(digits);
    if (!validDigits) {
      return {
        isValid: false,
        message: t(
          "internationalPriceManagement.validation.scheduleOnlyNumbers1to7"
        ),
      };
    }

    // 检查数字是否有重复
    const digitArray = digits.split("");
    const uniqueDigits = Array.from(new Set(digitArray));
    if (digitArray.length !== uniqueDigits.length) {
      return {
        isValid: false,
        message: t(
          "internationalPriceManagement.validation.scheduleNoDuplicateNumbers"
        ),
      };
    }

    return { isValid: true };
  };

  // 格式化班次字符串（数字按从小到大排序）
  const formatScheduleString = (value: string): string => {
    if (!value || !value.startsWith("D")) return value;

    const digits = value.slice(1);
    if (!digits) return value;

    // 将数字排序
    const sortedDigits = digits.split("").sort().join("");
    return `D${sortedDigits}`;
  };

  // 可编辑文本单元格渲染
  const renderEditableTextCell = (
    value: any,
    record: InternationalPrice,
    fieldName: string,
    isEditing: (record: InternationalPrice) => boolean,
    hasEditPermission: (record?: InternationalPrice) => boolean,
    edit: (record: InternationalPrice) => void,
    save: (key: string) => void,
    batchMode?: boolean
  ) => {
    const editable = isEditing(record) && hasEditPermission(record);

    // 判断是否为班次字段
    const isScheduleField =
      fieldName === "originschedules" || fieldName === "transferschedules";

    return editable ? (
      <Form.Item
        name={fieldName}
        style={{ margin: 0 }}
        initialValue={value}
        rules={
          isScheduleField
            ? [
                {
                  validator: (_, val) => {
                    if (!val) return Promise.resolve(); // 允许空值
                    const validation = validateScheduleFormat(val);
                    if (!validation.isValid) {
                      return Promise.reject(new Error(validation.message));
                    }
                    return Promise.resolve();
                  },
                },
              ]
            : []
        }
      >
        <Input
          style={{ width: "100%" }}
          placeholder={
            isScheduleField
              ? t("internationalPriceManagement.placeholders.scheduleFormat")
              : ""
          }
          onPressEnter={() => save(record.priceid.toString())}
          onBlur={
            isScheduleField
              ? (e) => {
                  const inputValue = e.target.value;
                  if (
                    inputValue &&
                    validateScheduleFormat(inputValue).isValid
                  ) {
                    const formatted = formatScheduleString(inputValue);
                    if (formatted !== inputValue) {
                      e.target.value = formatted;
                      const event = new Event("input", { bubbles: true });
                      e.target.dispatchEvent(event);
                    }
                  }
                }
              : undefined
          }
        />
      </Form.Item>
    ) : (
      <div
        className={`editable-cell-value-wrap ${batchMode ? "batch-mode-disabled" : ""}`}
        onClick={() => !batchMode && hasEditPermission(record) && edit(record)}
      >
        {value || "-"}
      </div>
    );
  };

  const renderCabinReports = (
    cabinReports: CabinReport,
    record: InternationalPrice,
    onEditCabinReport?: (record: InternationalPrice) => void
  ) => {
    if (
      !cabinReports ||
      !Array.isArray(cabinReports) ||
      cabinReports.length === 0
    ) {
      return (
        <div
          className="cabin-report-empty-cell"
          style={{
            cursor: hasEditPermission(record) ? "pointer" : "default",
            color: "#999",
            borderRadius: "4px",
            transition: "background-color 0.2s",
          }}
          onClick={() =>
            hasEditPermission(record) &&
            onEditCabinReport &&
            onEditCabinReport(record)
          }
          onMouseEnter={(e) => {
            if (hasEditPermission(record)) {
              e.currentTarget.style.backgroundColor = "#f5f5f5";
            }
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = "transparent";
          }}
        >
          -
        </div>
      );
    }

    const tooltipContent = (
      <div className="cabin-report-tooltip">
        <div className="tooltip-header">
          <span className="tooltip-title">
            {t("internationalPriceManagement.cabinReport.detailTitle")}
          </span>
          <span className="tooltip-count">
            {t("internationalPriceManagement.cabinReport.totalCount", {
              count: cabinReports.length,
            })}
          </span>
        </div>
        <div className="tooltip-content">
          {cabinReports.map((cabin, index) => {
            const date = cabin?.date
              ? dayjs(cabin.date).format("YYYY-MM-DD")
              : t("internationalPriceManagement.cabinReport.unknownDate");
            const transferDate = cabin?.transferdate
              ? dayjs(cabin.transferdate).format("YYYY-MM-DD")
              : null;
            const weightLimit =
              cabin?.weightlimit && cabin.weightlimit !== 99999
                ? cabin.weightlimit
                : null;
            const volumeLimit =
              cabin?.volumelimit && cabin.volumelimit !== 99999
                ? cabin.volumelimit
                : null;
            const densityRange = `${cabin?.lowerdensity || 0}-${cabin?.upperdensity || 99999}`;
            const priceChangesCount = cabin?.pricechanges?.length || 0;
            const specialPriceCount = cabin?.specialprice?.length || 0;

            return (
              <div key={index} className="cabin-item-detail">
                <div className="cabin-header">
                  <span className="cabin-index">#{index + 1}</span>
                  <span className="cabin-date">
                    {date}
                    {transferDate && (
                      <span className="transfer-date">
                        {" → " + transferDate}
                      </span>
                    )}
                  </span>
                  <div className="cabin-badges">
                    {(weightLimit || volumeLimit) && (
                      <span className="badge limit-badge">
                        {t(
                          "internationalPriceManagement.cabinReport.badges.limit"
                        )}
                      </span>
                    )}
                    {(priceChangesCount > 0 || specialPriceCount > 0) && (
                      <span className="badge special-badge">
                        {t(
                          "internationalPriceManagement.cabinReport.badges.special"
                        )}
                      </span>
                    )}
                    {cabin?.onlyparts && (
                      <span className="badge parts-badge">
                        {t(
                          "internationalPriceManagement.cabinReport.badges.parts"
                        )}
                      </span>
                    )}
                    {cabin?.singleinquiry && (
                      <span className="badge inquiry-badge">
                        {t(
                          "internationalPriceManagement.cabinReport.badges.inquiry"
                        )}
                      </span>
                    )}
                  </div>
                </div>
                <div className="cabin-details">
                  <div className="detail-row">
                    <span className="detail-label">
                      {t(
                        "internationalPriceManagement.cabinReport.labels.densityRange"
                      )}
                      :
                    </span>
                    <span className="detail-value">{densityRange} kg/m³</span>
                  </div>
                  {(weightLimit || volumeLimit) && (
                    <div className="detail-row">
                      <span className="detail-label">
                        {t(
                          "internationalPriceManagement.cabinReport.labels.limitations"
                        )}
                        :
                      </span>
                      <span className="detail-value">
                        {weightLimit &&
                          `${t("internationalPriceManagement.cabinReport.labels.weight")}≤${weightLimit}kg`}
                        {weightLimit && volumeLimit && " | "}
                        {volumeLimit &&
                          `${t("internationalPriceManagement.cabinReport.labels.volume")}≤${volumeLimit}m³`}
                      </span>
                    </div>
                  )}
                  {/* 价格变化详细信息 */}
                  {cabin?.pricechanges && cabin.pricechanges.length > 0 && (
                    <div className="detail-row">
                      <span className="detail-label">
                        {t(
                          "internationalPriceManagement.cabinReport.labels.priceChanges"
                        )}
                        :
                      </span>
                      <div className="detail-value">
                        {cabin.pricechanges.map(
                          (change: any, changeIndex: number) => (
                            <div
                              key={changeIndex}
                              className="price-change-item"
                            >
                              {t(
                                "internationalPriceManagement.cabinReport.labels.density"
                              )}{" "}
                              {change.leftdensity}-{change.rightdensity} kg/m³:{" "}
                              {change.pchanges > 0 ? "+" : ""}
                              {change.pchanges}
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}
                  {/* 特价找货详细信息 */}
                  {cabin?.specialprice && cabin.specialprice.length > 0 && (
                    <div className="detail-row">
                      <span className="detail-label">
                        {t(
                          "internationalPriceManagement.cabinReport.labels.specialPrice"
                        )}
                        :
                      </span>
                      <div className="detail-value">
                        {cabin.specialprice.map(
                          (special: any, specialIndex: number) => (
                            <div
                              key={specialIndex}
                              className="special-price-item"
                            >
                              {t(
                                "internationalPriceManagement.cabinReport.labels.density"
                              )}{" "}
                              {special.densitylowerlimit}-
                              {special.densityupperlimit} kg/m³: ¥
                              {special.sprice}
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );

    const displayContent = (
      <div className="cabin-report-cell">
        <span className="cabin-icon">📋</span>
        <span className="cabin-separator">|</span>
        {cabinReports.slice(0, 3).map((cabin, index) => {
          const date = cabin?.date
            ? dayjs(cabin.date).format("MM-DD")
            : t("internationalPriceManagement.cabinReport.unknown");
          const transferDate = cabin?.transferdate
            ? dayjs(cabin.transferdate).format("MM-DD")
            : null;
          const hasLimits =
            (cabin?.weightlimit && cabin.weightlimit !== 99999) ||
            (cabin?.volumelimit && cabin.volumelimit !== 99999);
          const hasSpecialItems =
            cabin?.pricechanges?.length > 0 || cabin?.specialprice?.length > 0;

          return (
            <span key={index} className="cabin-item">
              <span className="cabin-date">
                {date}
                {transferDate && (
                  <span className="transfer-date-inline">→{transferDate}</span>
                )}
              </span>
              {hasLimits && (
                <span className="cabin-tag limit-tag">
                  {t("internationalPriceManagement.cabinReport.tags.limit")}
                </span>
              )}
              {hasSpecialItems && (
                <span className="cabin-tag special-tag">
                  {t("internationalPriceManagement.cabinReport.tags.special")}
                </span>
              )}
              {index < Math.min(cabinReports.length - 1, 2) && (
                <span className="cabin-divider">,</span>
              )}
            </span>
          );
        })}
        {cabinReports.length > 3 && (
          <span className="cabin-more">+{cabinReports.length - 3}</span>
        )}
      </div>
    );

    return (
      <Tooltip
        title={tooltipContent}
        placement="left"
        overlayStyle={{ maxWidth: 800 }}
        overlayClassName="custom-tooltip"
      >
        <div
          className="cabin-report-tooltip-trigger"
          style={{
            cursor: hasEditPermission(record) ? "pointer" : "default",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            maxWidth: "100%",
          }}
          onClick={() =>
            hasEditPermission(record) &&
            onEditCabinReport &&
            onEditCabinReport(record)
          }
        >
          {displayContent}
        </div>
      </Tooltip>
    );
  };

  // 收费项目列表渲染 - 用于包装费用和特殊物品收费
  const renderChargesList = (
    chargesList: any[],
    itemKey: string,
    valueKey: string = "value"
  ) => {
    if (!chargesList || !chargesList.length) return "-";

    const formattedText = chargesList
      .map((item: any) => `${item[itemKey]}+${item[valueKey]}`)
      .join(", ");

    return <Tooltip title={formattedText}>{formattedText}</Tooltip>;
  };

  // 创建价格列的工厂函数
  const createPriceColumn = (
    config: PriceColumnConfig,
    t: (key: string) => string,
    isEditing: (record: InternationalPrice) => boolean,
    hasEditPermission: (record?: InternationalPrice) => boolean,
    edit: (record: InternationalPrice) => void,
    save: (key: string) => void,
    batchMode?: boolean
  ) => ({
    title: t(`internationalPriceManagement.columns.${config.titleKey}`),
    dataIndex: config.dataIndex,
    key: config.key,
    width: config.width,
    sorter: (a: any, b: any) => a[config.dataIndex] - b[config.dataIndex],
    render: (value: any, record: InternationalPrice) =>
      renderEditableCell(
        value,
        record,
        config.dataIndex,
        isEditing,
        hasEditPermission,
        edit,
        save,
        batchMode
      ),
  });

  const getColumnWidth = (zhWidth: number, enWidth: number) => {
    return isEnglish ? enWidth : zhWidth;
  };
  // 基础列定义
  const baseColumns: TableProps<InternationalPrice>["columns"] = [
    {
      title: t("internationalPriceManagement.columns.updateDate"),
      dataIndex: "priceupdatetime",
      key: "priceupdatetime",
      width: getColumnWidth(90, 140),
      render: (text) => (text ? dayjs(text).format("YYYY-MM-DD") : "-"),
      ...createFilterConfig.dateRange("priceupdatetime"),
    },
    {
      title: t("internationalPriceManagement.columns.id"),
      dataIndex: "priceid",
      key: "priceid",
      width: 50,
      render: (text: string) => <Tooltip title={text}>{text}</Tooltip>,
    },
    // 供应商列：用户角色1、3不显示，其他角色显示
    ...(userIdentity === 1 || userIdentity === 3
      ? []
      : [
          {
            title: t("internationalPriceManagement.columns.supplier"),
            dataIndex: "suppliername",
            key: "suppliername",
            width: getColumnWidth(88, 100),
            render: (text: string) => (
              <Tooltip title={text ? text : "-"}>{text ? text : "-"}</Tooltip>
            ),
            ...createFilterConfig.select(
              supplierOptions,
              t("internationalPriceManagement.filters.selectSupplier"),
              "suppliername",
              "suppliername"
            ),
          },
        ]),
    {
      title: t("internationalPriceManagement.columns.airlineName"),
      dataIndex: "airlinename",
      key: "airlinename",
      width: getColumnWidth(78, 140),
      ...createFilterConfig.select(
        airlineOptions,
        t("internationalPriceManagement.filters.selectAirline"),
        "airlinename",
        "airlinename"
      ),
    },
    {
      title: t("internationalPriceManagement.columns.originPort"),
      dataIndex: "originport",
      key: "originport",
      width: getColumnWidth(78, 120),
      ...createFilterConfig.select(
        portOptions,
        t("internationalPriceManagement.filters.selectOriginPort"),
        "originport",
        "originport"
      ),
    },
    {
      title: t("internationalPriceManagement.columns.destinationPort"),
      dataIndex: "unloadingport",
      key: "unloadingport",
      width: getColumnWidth(90, 180),
      render: (text) => (
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      ),
      ...createFilterConfig.select(
        portOptions,
        t("internationalPriceManagement.filters.selectDestinationPort"),
        "unloadingport",
        "unloadingport"
      ),
    },
    // === 价格列区域开始 ===
    ...PRICE_COLUMNS.map((config) =>
      createPriceColumn(
        config,
        t,
        isEditing,
        hasEditPermission,
        edit,
        save,
        batchMode // 传递批量模式参数
      )
    ),
    // === 价格列区域结束 ===
    {
      title: t("internationalPriceManagement.columns.cabinReport"),
      dataIndex: "cabinreport",
      key: "cabinreport",
      width: 200,
      render: (cabinReports: CabinReport, record: InternationalPrice) =>
        renderCabinReports(cabinReports, record, onEditCabinReport),
    },
    {
      title: t("internationalPriceManagement.columns.originSchedules"),
      dataIndex: "originschedules",
      key: "originschedules",
      width: 140,
      render: (value: any, record: InternationalPrice) =>
        renderEditableTextCell(
          value,
          record,
          "originschedules",
          isEditing,
          hasEditPermission,
          edit,
          save,
          batchMode
        ),
      ...createFilterConfig.customTableFilter(
        createWeekdayOptions(t),
        true,
        "originschedules"
      ),
    },
    {
      title: t("internationalPriceManagement.columns.transferPort"),
      dataIndex: "transfer",
      key: "transfer",
      width: getColumnWidth(100, 130),
      render: (text) => {
        return text ? text : "-";
      },
      ...createFilterConfig.customTableFilter(
        createTransferStatusOptions(t),
        false,
        "transfer"
      ),
    },
    {
      title: t("internationalPriceManagement.columns.transferSchedules"),
      dataIndex: "transferschedules",
      key: "transferschedules",
      width: 140,
      render: (value: any, record: InternationalPrice) =>
        renderEditableTextCell(
          value,
          record,
          "transferschedules",
          isEditing,
          hasEditPermission,
          edit,
          save,
          batchMode
        ),
    },
    {
      title: t("internationalPriceManagement.columns.lengthLimit"),
      dataIndex: "lengthlimit",
      key: "lengthlimit",
      width: getColumnWidth(90, 110),
      sorter: (a, b) => a.lengthlimit - b.lengthlimit,
    },
    {
      title: t("internationalPriceManagement.columns.widthLimit"),
      dataIndex: "widthlimit",
      key: "widthlimit",
      width: getColumnWidth(90, 110),
      sorter: (a, b) => a.widthlimit - b.widthlimit,
    },
    {
      title: t("internationalPriceManagement.columns.heightLimit"),
      dataIndex: "heightlimit",
      key: "heightlimit",
      width: getColumnWidth(90, 110),
      sorter: (a, b) => a.heightlimit - b.heightlimit,
    },
    {
      title: t("internationalPriceManagement.columns.packageCharges"),
      dataIndex: "packagecharges",
      key: "packagecharges",
      width: getColumnWidth(180, 160),
      render: (chargesList) => renderChargesList(chargesList, "packageItem"),
    },
    {
      title: t("internationalPriceManagement.columns.specialCharges"),
      dataIndex: "specialcharges",
      key: "specialcharges",
      width: getColumnWidth(180, 170),
      render: (chargesList) => renderChargesList(chargesList, "specialItem"),
    },
    {
      title: t("internationalPriceManagement.columns.effectiveDate"),
      dataIndex: "effectivetime",
      key: "effectivetime",
      width: getColumnWidth(120, 140),
      render: (text) => {
        return text ? dayjs(text).format("YYYY-MM-DD") : "-";
      },
    },
  ];

  // 操作按钮配置
  const actionButtons = {
    view: (record: InternationalPrice) => (
      <Tooltip title={t("internationalPriceManagement.actions.viewDetail")}>
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => handleViewDetail(record)}
          className="view-button"
          size="small"
        />
      </Tooltip>
    ),
    copy: (record: InternationalPrice) => (
      <Tooltip title={t("internationalPriceManagement.actions.copy")}>
        <Button
          type="text"
          icon={<CopyOutlined />}
          onClick={() => handleCopy(record)}
          className="copy-button"
          size="small"
        />
      </Tooltip>
    ),
    edit: (record: InternationalPrice) => (
      <Tooltip title={t("internationalPriceManagement.actions.edit")}>
        <Button
          type="text"
          icon={<EditOutlined />}
          onClick={() => handleEdit(record)}
          className="edit-button"
          size="small"
        />
      </Tooltip>
    ),
    delete: (record: InternationalPrice) => (
      <Tooltip title={t("internationalPriceManagement.actions.delete")}>
        <Popconfirm
          title={t("internationalPriceManagement.confirmDelete.title")}
          description={t(
            "internationalPriceManagement.confirmDelete.description"
          )}
          onConfirm={() => handleDel(record)}
          onCancel={handleCancel}
          okText={t("internationalPriceManagement.confirmDelete.okText")}
          cancelText={t(
            "internationalPriceManagement.confirmDelete.cancelText"
          )}
        >
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            className="delete-button"
            size="small"
          />
        </Popconfirm>
      </Tooltip>
    ),
    save: (record: InternationalPrice) => (
      <Tooltip title={t("internationalPriceManagement.actions.save")}>
        <Button
          type="text"
          icon={<CheckOutlined />}
          onClick={() => save(record.priceid.toString())}
          className="save-button"
          size="small"
        />
      </Tooltip>
    ),
    cancel: () => (
      <Tooltip title={t("internationalPriceManagement.actions.cancel")}>
        <Button
          type="text"
          icon={<CloseOutlined />}
          onClick={cancel}
          className="cancel-button"
          size="small"
        />
      </Tooltip>
    ),
  };

  // 添加操作列
  baseColumns.push({
    title: t("internationalPriceManagement.columns.actions"),
    key: "action",
    fixed: "right" as const,
    width: getColumnWidth(180, 200),
    render: (_, record) => {
      const canEditAndDelete = hasEditPermission(record);
      const editable = isEditing(record);

      return (
        <Space size="middle" className="operation-buttons">
          {editable ? (
            <>
              {actionButtons.save(record)}
              {actionButtons.cancel()}
            </>
          ) : (
            <>
              {actionButtons.view(record)}
              {canEditAndDelete && (
                <>
                  {actionButtons.copy(record)}
                  {actionButtons.edit(record)}
                  {actionButtons.delete(record)}
                </>
              )}
            </>
          )}
        </Space>
      );
    },
  });

  return baseColumns;
};
