import React, { useState } from "react";
import {
  Form,
  Input,
  Button,
  message,
  Spin,
  Modal,
  Tabs,
  Row,
  Col,
  Divider,
} from "antd";
import {
  UserOutlined,
  LockOutlined,
  FileOutlined,
  MailOutlined,
  KeyOutlined,
  PhoneOutlined,
} from "@ant-design/icons";
import "./index.less";
import { LoginApi, getCode, registerAccount } from "./services";
import { useNavigate, Navigate } from "react-router-dom";
import { SHA256 } from "crypto-js";
import { setUserInfo } from "@/store/slices/userSlice";
import { useDispatch } from "react-redux";
import preloadBaseData from "@/utils/preloadBaseData";
import { useTranslation } from "react-i18next";
import LanguageSwitcher from "@/components/LanguageSwitcher";

const Login: React.FC = () => {
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [registerModalVisible, setRegisterModalVisible] = useState(false);
  const [registerForm] = Form.useForm();
  const [sendingCode, setSendingCode] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [registerLoading, setRegisterLoading] = useState(false);

  //在登陆状态下，需要跳转到home页面
  if (localStorage.getItem("token")) {
    return <Navigate to="/" />;
  }

  // 打开注册模态框
  const showRegisterModal = () => {
    setRegisterModalVisible(true);
    registerForm.resetFields();
  };

  // 关闭注册模态框
  const handleRegisterCancel = () => {
    setRegisterModalVisible(false);
    registerForm.resetFields();
  };

  // 发送验证码
  const handleSendVerificationCode = async () => {
    try {
      // 验证邮箱字段
      await registerForm.validateFields(["email"]);
      const email = registerForm.getFieldValue("email");

      setSendingCode(true);
      try {
        const res = await getCode({ email });
        if (res.data?.resultCode === 200) {
          message.success(t("verification.codeSent"));
          // 开始倒计时
          setCountdown(60);
          const timer = setInterval(() => {
            setCountdown((prev) => {
              if (prev <= 1) {
                clearInterval(timer);
                return 0;
              }
              return prev - 1;
            });
          }, 1000);
        } else {
          message.error(res.data?.message || t("verification.codeSendFailed"));
        }
      } catch (error) {
        console.error("发送验证码错误:", error);
        message.error(t("verification.emailContent"));
      } finally {
        setSendingCode(false);
      }
    } catch (error) {
      message.error(t("verification.emailContent"));
    }
  };

  // 处理注册提交
  const handleRegister = async () => {
    try {
      const values = await registerForm.validateFields();
      setRegisterLoading(true);

      try {
        const res = await registerAccount({
          ...values,
          password: SHA256(values.password).toString(),
          confirmPassword: undefined,
        });

        if (res.data?.resultCode === 200) {
          message.success(t("login.RegistrationSuccess"));
          setRegisterModalVisible(false);
          registerForm.resetFields();
        } else {
          message.error(res.data?.message || t(".RegistrationFailed"));
        }
      } catch (error) {
        console.error("注册错误:", error);
        message.error(t(".RegistrationFailed"));
      } finally {
        setRegisterLoading(false);
      }
    } catch (error) {
      console.error("表单验证错误:", error);
    }
  };

  const handleSubmit = async (val: { password: string; email: string }) => {
    // 检查是否有值
    if (!val.email || !val.password) {
      message.warning(t("login.inputRequired"));
      return;
    }

    setLoading(true);
    try {
      const res = await LoginApi({
        email: val.email,
        password: SHA256(val.password).toString(),
      });
      const { data } = res;
      if (data?.resultCode === 200) {
        // 先更新Redux状态
        dispatch(setUserInfo({ user: data?.data, token: data?.data?.token }));
        message.success(t("login.loginSuccess"));
        // 预加载基础数据
        preloadBaseData();
        navigator("/");
      } else {
        message.error(data.message || t("login.loginFailed"));
      }
    } catch (e) {
      console.log("登录错误:", e);
      message.error(t("login.loginRequestFailed"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      {/* 语言切换器 */}
      <div className="language-switcher-wrapper">
        <LanguageSwitcher />
      </div>

      {/* 动态光晕元素 */}
      <div className="light-orb light-orb-1"></div>
      <div className="light-orb light-orb-2"></div>
      <div className="light-orb light-orb-3"></div>

      {/* 浮动粒子 */}
      <div className="particles">
        <div className="particle particle-1"></div>
        <div className="particle particle-2"></div>
        <div className="particle particle-3"></div>
        <div className="particle particle-4"></div>
        <div className="particle particle-5"></div>
        <div className="particle particle-6"></div>
        <div className="particle particle-7"></div>
        <div className="particle particle-8"></div>
        <div className="particle particle-9"></div>
        <div className="particle particle-10"></div>
      </div>

      {/* 3D装饰元素 */}
      <div className="decoration-cube cube-1"></div>
      <div className="decoration-cube cube-2"></div>
      <div className="decoration-cube cube-3"></div>
      <div className="decoration-sphere sphere-1"></div>
      <div className="decoration-sphere sphere-2"></div>

      <Form
        className="login-box"
        onFinish={handleSubmit}
        validateTrigger="onSubmit"
      >
        <div className="title">
          <h1>{t("layout.systemTitle")}</h1>
          <p>{t("login.title")}</p>
        </div>

        <Form.Item
          label={t("login.account")}
          name="email"
          rules={[{ required: true, message: t("login.accountPlaceholder") }]}
        >
          <Input
            prefix={<UserOutlined style={{ color: "#9ca3af" }} />}
            placeholder={t("login.accountPlaceholder")}
            size="large"
            autoComplete="email"
          />
        </Form.Item>

        <Form.Item
          label={t("login.password")}
          name="password"
          rules={[{ required: true, message: t("login.passwordRequired") }]}
        >
          <Input.Password
            prefix={<LockOutlined style={{ color: "#9ca3af" }} />}
            placeholder={t("login.passwordRequired")}
            size="large"
            autoComplete="current-password"
          />
        </Form.Item>

        <Form.Item className="login-button">
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            size="large"
          >
            {loading ? t("login.LoginIn") : t("login.login")}
          </Button>
        </Form.Item>

        <div className="register-link">
          <span>{t("login.noAccount")}</span>
          <Button type="link" onClick={showRegisterModal}>
            {t("login.RegisterNow")}
          </Button>
        </div>
      </Form>

      {/* 注册模态框 */}
      <Modal
        title={t("register.title")}
        open={registerModalVisible}
        onCancel={handleRegisterCancel}
        footer={null}
        width={500}
        className="register-modal"
        centered
      >
        <Form form={registerForm} layout="vertical" onFinish={handleRegister}>
          {/* <Form.Item
            name="email"
            label="账号"
            rules={[
              { required: true, message: "请输入账号" },
              // { min: 4, message: "账号长度不能少于4个字符" },
              { max: 20, message: "账号长度不能超过20个字符" },
            ]}
          >
            <Input
              prefix={<UserOutlined style={{ color: "#9ca3af" }} />}
              placeholder="请输入账号"
            />
          </Form.Item> */}

          <Form.Item
            name="email"
            label={t("register.emailLabel")}
            rules={[
              { required: true, message: t("register.emailRequired") },
              { type: "email", message: t("register.emailInvalid") },
            ]}
          >
            <Input
              prefix={<MailOutlined style={{ color: "#9ca3af" }} />}
              placeholder={t("register.emailRequired")}
            />
          </Form.Item>

          <Form.Item
            name="fullname"
            label={t("register.fullnameLabel")}
            rules={[
              // { required: true, message: "请输入姓名" },
              { max: 50, message: t("register.fullnameMaxLength") },
            ]}
          >
            <Input
              prefix={<UserOutlined style={{ color: "#9ca3af" }} />}
              placeholder={t("register.fullnamePlaceholder")}
            />
          </Form.Item>

          <Form.Item
            name="telephone"
            label={t("register.telephoneLabel")}
            rules={[
              // { required: true, message: "请输入电话" },
              {
                pattern: /^1[3-9]\d{9}$/,
                message: t("register.telephoneInvalid"),
              },
            ]}
          >
            <Input
              prefix={<PhoneOutlined style={{ color: "#9ca3af" }} />}
              placeholder={t("register.telephonePlaceholder")}
            />
          </Form.Item>

          <Form.Item
            name="password"
            label={t("register.passwordLabel")}
            rules={[
              { required: true, message: t("register.passwordRequired") },
              // { min: 6, message: "密码长度不能少于6个字符" },
              { max: 20, message: t("register.passwordMaxLength") },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined style={{ color: "#9ca3af" }} />}
              placeholder={t("register.passwordRequired")}
            />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label={t("register.confirmPasswordLabel")}
            dependencies={["password"]}
            rules={[
              {
                required: true,
                message: t("register.confirmPasswordRequired"),
              },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("password") === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(
                    new Error(t("register.confirmPasswordMismatch"))
                  );
                },
              }),
            ]}
          >
            <Input.Password
              prefix={<LockOutlined style={{ color: "#9ca3af" }} />}
              placeholder={t("register.confirmPasswordRequired")}
            />
          </Form.Item>

          <Form.Item label={t("register.verifyCodeLabel")}>
            <Row gutter={8}>
              <Col span={16}>
                <Form.Item
                  name="verifycode"
                  noStyle
                  rules={[
                    {
                      required: true,
                      message: t("register.verifyCodeRequired"),
                    },
                    { len: 6, message: t("register.verifyCodeLength") },
                  ]}
                >
                  <Input
                    prefix={<KeyOutlined style={{ color: "#9ca3af" }} />}
                    placeholder={t("register.verifyCodeRequired")}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Button
                  onClick={handleSendVerificationCode}
                  loading={sendingCode}
                  disabled={countdown > 0}
                  block
                >
                  {countdown > 0
                    ? t("register.sendCodeCountdown", { countdown: countdown })
                    : t("register.sendCode")}
                </Button>
              </Col>
            </Row>
          </Form.Item>

          <Form.Item className="register-button">
            <Button
              type="primary"
              htmlType="submit"
              loading={registerLoading}
              block
            >
              {t("login.register")}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Login;
