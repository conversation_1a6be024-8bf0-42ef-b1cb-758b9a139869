import { ConfigProvider } from "antd";
import zhCN from "antd/es/locale/zh_CN";
import enUS from "antd/es/locale/en_US";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import "dayjs/locale/en";
import router from "./router";
import { RouterProvider } from "react-router-dom";
import { useAppSelector } from "@/store/hooks";
import { useVersionCheck } from "@/hooks/useVersionCheck";
import "./i18n"; // 导入i18n配置

// 设置 Day.js 语言的函数
const setDayjsLocale = (language: string) => {
  if (language === "zh-CN") {
    dayjs.locale("zh-cn");
  } else {
    dayjs.locale("en");
  }
};

function AppContent() {
  const { currentLanguage } = useAppSelector((state) => state.language);

  // // 启用版本检查（仅在生产环境）
  // useVersionCheck({
  //   checkInterval: 30000, // 30秒检查一次
  //   enabled: true,
  //   enableInDevelopment: false, // 开发环境不启用
  // });

  // 根据当前语言设置Antd和dayjs的语言
  const antdLocale = currentLanguage === "zh-CN" ? zhCN : enUS;
  setDayjsLocale(currentLanguage);

  return (
    <ConfigProvider
      locale={antdLocale}
      theme={{
        token: {
          colorPrimary: "#1797e1", // 主题主色
          borderRadius: 5, // 全局圆角
          fontSize: 14, // 全局字体大小
        },
      }}
    >
      <RouterProvider router={router} />
    </ConfigProvider>
  );
}

function App() {
  return <AppContent />;
}

export default App;
