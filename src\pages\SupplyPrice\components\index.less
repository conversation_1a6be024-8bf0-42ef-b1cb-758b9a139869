@bg-color: #f9fafb;
@card-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
@primary-text: #1f2937;
@secondary-text: #4b5563;
@border-color: #e5e7eb;
@rounded-button: 6px;
@transition-time: 0.3s;
@section-border-radius: 8px;

.ant-input-number-input {
  display: flex !important;
  align-items: center !important;
  line-height: 1 !important;
}

.filter_container {
  overflow-y: auto;
  height: calc(100% - 60px);
  padding-right: 20px;

  scrollbar-width: thin;
  scrollbar-color: #eaeaea transparent;
  scrollbar-gutter: stable;

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #eaeaea;
    border-radius: 4px;
    border: 2px solid transparent;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #d0d0d0;
  }

  .filter_title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #1f2937;
    position: relative;
    padding-left: 12px;
    display: flex;
    align-items: center;
    gap: 12px;

    &:before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 18px;
      background-color: @primary-color;
      border-radius: 2px;
    }
  }

  .filter_top {
    margin-bottom: 16px;

    .ant-alert {
      border-radius: 10px;
      border: 1px solid rgba(24, 144, 255, 0.1);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
      transition: all 0.3s ease;
      overflow: hidden;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.9),
          rgba(240, 248, 255, 0.5)
        );
        pointer-events: none;
        z-index: -1;
      }

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 10px;
        padding: 1px;
        background: linear-gradient(
          135deg,
          rgba(0, 120, 255, 0.2),
          rgba(0, 60, 255, 0.1),
          rgba(0, 120, 255, 0.2)
        );
        mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        -webkit-mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        pointer-events: none;
        z-index: 1;
      }

      &.ant-alert-info {
        background-color: rgba(240, 247, 255, 0.7);
        backdrop-filter: blur(5px);

        .ant-alert-message {
          color: #1f2937;
          font-weight: 500;
        }

        .ant-alert-description {
          color: #4b5563;
        }

        .anticon {
          color: #1890ff;
        }
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 120, 255, 0.08);
        border-color: rgba(24, 144, 255, 0.2);
      }
    }
  }

  .filter_content {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    padding: 16px;
    border: 1px solid rgba(0, 120, 255, 0.1);
    margin-top: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    backdrop-filter: blur(5px);
    position: relative;
    transition: all 0.3s ease;
    margin-bottom: 10px;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 10px;
      padding: 1px;
      background: linear-gradient(
        135deg,
        rgba(0, 120, 255, 0.15),
        rgba(0, 60, 255, 0.05),
        rgba(0, 120, 255, 0.15)
      );
      mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      pointer-events: none;
    }

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      border-color: rgba(0, 120, 255, 0.2);

      &::before {
        background: linear-gradient(
          135deg,
          rgba(0, 120, 255, 0.25),
          rgba(0, 60, 255, 0.1),
          rgba(0, 120, 255, 0.25)
        );
      }
    }

    .ant-form-item {
      margin-bottom: 16px;

      .ant-form-item-label {
        padding-bottom: 4px;

        label {
          color: @primary-text;
          font-weight: 500;
        }
      }

      .ant-input,
      .ant-input-number,
      .ant-select .ant-select-selector,
      .ant-picker {
        border-radius: 6px;
        transition: all 0.3s;
        border-color: rgba(0, 120, 255, 0.1);
        height: 32px;
        font-size: 13px;
        width: 100%;
        background-color: rgba(255, 255, 255, 0.9);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);

        &:hover {
          border-color: rgba(0, 120, 255, 0.3);
          box-shadow: 0 2px 5px rgba(0, 120, 255, 0.05);
          transform: translateY(-1px);
        }

        &:focus {
          border-color: @primary-color;
          box-shadow: 0 0 0 2px fade(@primary-color, 15%);
          background-color: #fff;
        }
      }

      .ant-input {
        line-height: 32px;
        padding: 0 11px;
      }

      .ant-input-number {
        width: 100%;

        .ant-input-number-input {
          height: 30px;
          line-height: 30px;
          padding: 0 11px;
          display: flex;
          align-items: center;
        }
      }

      .ant-select .ant-select-selector {
        height: 32px;
        padding: 0 11px;
        display: flex;
        align-items: center;
      }

      .ant-select-multiple .ant-select-selector {
        height: auto;
        min-height: 32px;
        padding: 2px 4px;

        .ant-select-selection-item {
          height: 22px;
          line-height: 20px;
          margin-top: 2px;
          margin-bottom: 2px;
          padding: 0 6px 0 8px;

          .ant-select-selection-item-remove {
            margin-left: 3px;
          }
        }

        .ant-select-selection-search {
          margin-inline-start: 0;
          margin-inline-end: 0;

          .ant-select-selection-search-input {
            height: 22px;
            margin-top: 2px;
            margin-bottom: 2px;
          }
        }
      }

      .ant-select-multiple.ant-select-show-search {
        .ant-select-selector {
          flex-wrap: wrap;
        }
      }
    }

    .advanced-filter-toggle {
      color: @primary-color;
      cursor: pointer;
      padding: 8px 12px;
      text-align: center;
      transition: all 0.3s;
      margin-top: 12px;
      border-radius: 6px;
      background-color: rgba(24, 144, 255, 0.05);
      border: 1px dashed rgba(24, 144, 255, 0.15);
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 500;
      font-size: 13px;

      .anticon {
        margin-right: 6px;
        transition: transform 0.3s;
      }

      &:hover {
        background-color: rgba(24, 144, 255, 0.1);
        border-color: rgba(24, 144, 255, 0.25);
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(24, 144, 255, 0.1);
      }
    }

    .filter-section {
      margin-bottom: 20px;
      background-color: rgba(240, 248, 255, 0.5);
      border-radius: 8px;
      padding: 14px;
      border: 1px solid rgba(0, 120, 255, 0.05);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:hover {
        background-color: rgba(240, 248, 255, 0.7);
        border-color: rgba(0, 120, 255, 0.1);
        box-shadow: 0 2px 8px rgba(0, 120, 255, 0.05);
      }

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.7),
          rgba(240, 248, 255, 0.3)
        );
        pointer-events: none;
        z-index: -1;
      }

      .section-title {
        font-weight: 500;
        margin-bottom: 16px;
        color: #1f2937;
        font-size: 14px;
        position: relative;
        padding-left: 10px;
        border-bottom: 1px dashed rgba(0, 120, 255, 0.15);
        padding-bottom: 8px;
        display: flex;
        align-items: center;

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 14px;
          background: linear-gradient(to bottom, #1890ff, #40a9ff);
          border-radius: 2px;
        }
      }

      .section-content {
        padding: 0 4px;
      }
    }

    .advanced-filters {
      margin-top: 16px;

      .filter-section {
        margin-bottom: 16px;
      }
    }
  }

  .ant-divider {
    margin: 16px 0;
    border-top-color: rgba(0, 120, 255, 0.1);

    &.ant-divider-with-text {
      &::before,
      &::after {
        border-top-color: rgba(0, 120, 255, 0.1);
      }
    }

    .ant-divider-inner-text {
      font-weight: 500;
      color: #1f2937;
      padding: 0 12px;
      font-size: 14px;

      .anticon {
        margin-right: 6px;
        color: #1890ff;
      }
    }
  }

  .filter-group {
    background-color: #f9fafb;
    border-radius: @section-border-radius;
    padding: 12px;
    margin-bottom: 16px;
    border: 1px solid @border-color;

    .group-title {
      font-weight: 500;
      margin-bottom: 8px;
      color: @primary-text;
      font-size: 14px;
    }
  }

  .quick-filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;

    .filter-tag {
      padding: 4px 10px;
      background-color: #f3f4f6;
      border-radius: 16px;
      font-size: 12px;
      color: @secondary-text;
      cursor: pointer;
      transition: all @transition-time;
      border: 1px solid transparent;

      &:hover {
        background-color: #e5e7eb;
      }

      &.active {
        background-color: fade(@primary-color, 10%);
        color: @primary-color;
        border-color: fade(@primary-color, 30%);
      }
    }
  }
}

.filter_footer {
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: rgba(255, 255, 255, 0.95);
  width: 100%;
  border-top: 1px solid rgba(0, 120, 255, 0.1);
  padding: 0 20px;
  backdrop-filter: blur(5px);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.03);
  z-index: 5;

  .ant-btn {
    min-width: 100px;
    border-radius: 6px;
    height: 36px;
    transition: all 0.3s;
    font-weight: 500;

    &:not(:last-child) {
      margin-right: 12px;
    }

    &.ant-btn-primary {
      background: linear-gradient(90deg, #1797e1, #40a9ff);
      border: none;
      box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);

      &:hover {
        background: linear-gradient(90deg, #40a9ff, #1797e1);
        box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
        transform: translateY(-2px);
      }
    }

    &:not(.ant-btn-primary) {
      border: 1px solid rgba(0, 120, 255, 0.15);
      color: #1890ff;
      background-color: rgba(24, 144, 255, 0.05);

      &:hover {
        background-color: rgba(24, 144, 255, 0.1);
        border-color: rgba(24, 144, 255, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 2px 6px rgba(24, 144, 255, 0.1);
      }
    }
  }
}

.add-container-supply-price {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  .fixed-title-box {
    position: fixed;
    top: 85px;
    left: 0;
    right: 0;
    z-index: 99;
    background: #fff;
    padding: 16px 16px 16px 0;
    margin: 0 15px;
    height: 56px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: none;
  }

  .content-container {
    margin-top: 63px;
    overflow-y: auto;
    background-color: #f9fafc;
    .form-container > .ant-card-body {
      height: calc(100vh - 220px);
    }
  }

  .fixed-title-box {
    .title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      position: relative;
      padding-left: 16px;
      display: flex;
      align-items: center;

      &:before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 22px;
        background: linear-gradient(to bottom, #1797e1, #40a9ff);
        border-radius: 2px;
      }
    }

    .go_back {
      display: flex;
      align-items: center;
      color: #1797e1;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      padding: 8px 16px;
      border-radius: 6px;
      transition: all 0.3s;
      border: 1px solid rgba(23, 151, 225, 0.2);
      background-color: rgba(23, 151, 225, 0.05);

      &:hover {
        background-color: rgba(23, 151, 225, 0.1);
        border-color: rgba(23, 151, 225, 0.3);
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(23, 151, 225, 0.15);
      }

      .anticon {
        margin-right: 8px;
      }
    }
  }

  .ant-card {
    background-color: #fff;
    overflow: hidden;

    .ant-card-head {
      border-bottom: 1px solid rgba(23, 151, 225, 0.1);
      background: linear-gradient(
        to right,
        rgba(23, 151, 225, 0.05),
        rgba(64, 169, 255, 0.02)
      );
      min-height: 56px;
      padding: 0 16px;
    }

    .ant-card-body {
      height: calc(100% - 56px);
      overflow-y: auto;
      padding: 20px;
      background-color: #fff;

      .form-block {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin-bottom: 0;
      }

      .form-card {
        border-radius: 8px;
        background-color: white;
        border: 1px solid #eaedf0;
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.02);
        padding: 16px;
        transition: all 0.3s ease;
        margin-bottom: 16px;
        position: relative;

        &:before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 2px;
          background: linear-gradient(to right, #1797e1, #40a9ff);
          border-radius: 8px 8px 0 0;
        }

        &:hover {
          box-shadow: 0 3px 10px rgba(0, 0, 0, 0.04);
          transform: translateY(-1px);
          border-color: #d9e8ff;
        }
      }

      .card-title {
        font-size: 15px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 14px;
        position: relative;
        padding-left: 10px;

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 14px;
          background: linear-gradient(to bottom, #1797e1, #40a9ff);
          border-radius: 2px;
        }
      }

      .form-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;

        @media (min-width: 1200px) {
          grid-template-columns: repeat(5, 1fr);
        }
      }

      .input-default {
        width: 100%;
        border-radius: 4px;

        &:hover,
        &:focus {
          border-color: #1797e1;
          box-shadow: 0 0 0 1px rgba(23, 151, 225, 0.1);
        }
      }

      .ant-form-item {
        margin-bottom: 12px;

        .ant-form-item-label {
          padding-bottom: 4px;

          > label {
            color: #374151;
            font-weight: 500;
            font-size: 13px;
          }
        }

        .ant-form-item-explain {
          font-size: 12px;
          min-height: 18px;
        }

        .ant-input,
        .ant-input-number,
        .ant-select .ant-select-selector,
        .ant-picker {
          border-radius: 4px;
          border-color: #e5e7eb;
          transition: all 0.3s;
          height: 32px;
          padding: 0 8px;
          font-size: 13px;
          box-shadow: 0 1px 1px rgba(0, 0, 0, 0.01);
          display: flex;
          align-items: center;

          &:hover {
            border-color: #1797e1;
            box-shadow: 0 0 0 1px rgba(23, 151, 225, 0.1);
          }

          &:focus {
            border-color: #1797e1;
            box-shadow: 0 0 0 2px rgba(23, 151, 225, 0.1);
            outline: none;
          }
        }

        .ant-input {
          line-height: 32px;
        }

        .ant-input-number {
          width: 100%;

          .ant-input-number-handler-wrap {
            border-radius: 0 4px 4px 0;
          }

          .ant-input-number-input {
            height: 30px;
            line-height: 30px;
            padding: 0 8px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            margin-top: 0;
            margin-bottom: 0;
          }

          input.ant-input-number-input {
            line-height: 1;
            padding-top: 0;
            padding-bottom: 0;
          }
        }

        .ant-switch {
          background-color: #d1d5db;
          height: 20px;
          min-width: 40px;

          .ant-switch-handle {
            width: 16px;
            height: 16px;
            top: 2px;
            left: 2px;
            transition: all 0.3s;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          }

          &.ant-switch-checked {
            background: linear-gradient(90deg, #1797e1, #40a9ff);

            .ant-switch-handle {
              left: calc(100% - 18px);
            }
          }

          &:hover:not(.ant-switch-disabled) {
            background-color: #b0b7c3;

            &.ant-switch-checked {
              background: linear-gradient(90deg, #40a9ff, #1797e1);
            }
          }
        }

        &[data-field="mprice"],
        &[data-field="nprice"],
        &[data-field="q45price"],
        &[data-field="q100price"],
        &[data-field="q300price"],
        &[data-field="q500price"],
        &[data-field="q1000price"],
        &[data-field="cabinprice"],
        &[data-field="smallcargofee"],
        &[data-field="subq45price"],
        &[data-field="subq100price"] {
          .ant-input-number-input {
            color: #1797e1;
            font-weight: 500;
            height: 30px;
            line-height: 30px;
            display: flex;
            align-items: center;
          }
        }
      }

      .sub-section {
        margin-top: 14px;
        padding-top: 12px;
        border-top: 1px dashed #e5e7eb;
      }

      .sub-title {
        font-size: 14px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 12px;
        position: relative;
        padding-left: 8px;

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 12px;
          background: linear-gradient(to bottom, #1797e1, #40a9ff);
          border-radius: 2px;
          opacity: 0.7;
        }
      }

      .dynamic-row {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 8px;
        background-color: #f5f7fa;
        padding: 8px;
        border-radius: 4px;
        border: 1px solid #e5e7eb;
        transition: all 0.3s;

        &:hover {
          background-color: #f0f9ff;
          border-color: #d9e8ff;
        }
      }

      .row-item {
        flex: 1;
        margin-bottom: 0;
      }

      .icon-remove {
        font-size: 14px;
        color: #ff4d4f;
        cursor: pointer;
        padding: 6px;
        border-radius: 50%;
        transition: all 0.3s;

        &:hover {
          background-color: #fff2f0;
          transform: scale(1.1);
        }
      }

      .btn-dashed {
        margin-top: 8px;
        border-style: dashed;
        border-color: #1797e1;
        color: #1797e1;
        border-radius: 4px;
        height: 32px;
        transition: all 0.3s;

        &:hover {
          border-color: #40a9ff;
          color: #40a9ff;
          background-color: rgba(23, 151, 225, 0.05);
        }
      }

      .branch-price-section {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-top: 12px;

        > div {
          background-color: #f5f7fa;
          padding: 12px;
          border-radius: 6px;
          border: 1px solid #e0e0e0;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
          transition: all 0.3s;

          &:hover {
            background-color: #f0f9ff;
            border-color: #d9e8ff;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
          }
        }
      }

      .ant-divider {
        margin: 16px 0 12px;

        &.ant-divider-with-text {
          &::before,
          &::after {
            border-top-color: #e5e7eb;
          }

          .ant-divider-inner-text {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            padding: 0 12px;
          }
        }
      }
    }
  }

  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    height: 56px;
    border-top: 1px solid #e6e8eb;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #fff;
    padding: 0 24px;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.03);
    z-index: 100;
    backdrop-filter: blur(5px);
    background-color: rgba(255, 255, 255, 0.95);

    .footer-button {
      .ant-btn {
        height: 32px;
        border-radius: 4px;
        padding: 0 16px;
        font-weight: 500;
        font-size: 14px;
        transition: all 0.3s;
        border: 1px solid #e6e8eb;

        &:not(:last-child) {
          margin-right: 12px;
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
        }

        &.ant-btn-primary {
          background: linear-gradient(90deg, #1797e1, #40a9ff);
          border: none;
          box-shadow: 0 1px 4px rgba(23, 151, 225, 0.2);
          color: white;

          &:hover {
            background: linear-gradient(90deg, #40a9ff, #1797e1);
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(23, 151, 225, 0.25);
          }
        }
      }
    }
  }
}

.quotation-editor-modal {
  .ant-modal-content {
    padding: 0;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.06);

    .ant-modal-body {
      padding: 0;
    }
  }

  .quotation-editor {
    max-height: calc(100vh - 40px);
    overflow: auto;
    padding: 38px 20px;

    .editor-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 0 12px;
      margin-bottom: 16px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);

      .header-left {
        h3.ant-typography {
          margin-bottom: 4px;
          font-size: 20px;
          color: #000;
          font-weight: 600;
          letter-spacing: 0.5px;
        }

        .ant-typography {
          color: #666;
          font-size: 14px;
        }
      }

      .header-right {
        .ant-btn {
          margin-left: 10px;
          border-radius: 6px;
          height: 36px;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
          transition: all 0.3s;
          border: 1px solid #d9d9d9;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: #40a9ff;
          }

          &.ant-btn-primary {
            background-color: #1890ff;
            border-color: #1890ff;
            color: #fff;

            &:hover {
              background-color: #40a9ff;
              border-color: #40a9ff;
            }
          }

          &.ant-btn-primary.ant-btn-ghost {
            background-color: transparent;
            border-color: #1890ff;
            color: #1890ff;

            &:hover {
              background-color: rgba(24, 144, 255, 0.1);
              border-color: #40a9ff;
              color: #40a9ff;
            }
          }

          &.ant-btn-danger {
            color: #ff4d4f;
            border-color: #ff4d4f;

            &:hover {
              background-color: #fff1f0;
              color: #ff7875;
              border-color: #ff7875;
            }
          }
        }
      }
    }

    .quotation-form {
      .quotation-card {
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
        overflow: hidden;
        transition: all 0.3s ease;
        background-color: #fff;

        &:hover {
          box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
          border-color: #e9e9e9;
        }

        .ant-card-head {
          padding: 0 16px;

          .ant-card-head-title {
            padding: 12px 0;
            font-size: 15px;
            font-weight: 600;
            color: #000;
          }
        }

        .ant-card-body {
          padding: 0 16px;
          background-color: #fff;
        }

        .quotation-textarea {
          font-family: "Courier New", Courier, monospace;
          font-size: 14px;
          line-height: 1.6;
          padding: 16px;
          border-radius: 6px;
          resize: vertical;
          min-height: 240px;
          background-color: #f9fbff;
          border: 1px solid #d9e8ff;
          color: #333;
          letter-spacing: 0.3px;
          transition: all 0.3s ease;
          box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);

          &:focus {
            background-color: #fff;
            border-color: #1890ff;
            box-shadow:
              0 0 0 2px rgba(24, 144, 255, 0.2),
              inset 0 2px 4px rgba(0, 0, 0, 0.05);
            outline: none;
          }

          &:hover {
            border-color: #40a9ff;
          }

          &::selection {
            background-color: #bae7ff;
          }
        }
      }
    }
  }
}

.quotation-editor {
  .editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e6f0fa;
    background: linear-gradient(to right, #f0f7ff, #f9fcff);

    .header-left {
      h3 {
        margin-bottom: 4px;
        display: flex;
        align-items: center;

        .anticon {
          margin-right: 8px;
          color: #1890ff;
        }
      }
    }
  }

  .quotation-form {
    padding: 24px;
    background-color: #f9fafc;

    .ant-form-item {
      margin-bottom: 0px;
    }

    .quotation-card {
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      background-color: #fff;
      border: 1px solid #e6e8eb;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }
    }

    .quotation-textarea {
      font-family: "Courier New", Courier, monospace;
      resize: none;
      border-radius: 4px;
    }

    .quotation-info-card {
      height: 100%;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      background-color: #fff;
      border: 1px solid #e6e8eb;

      .ant-card-head {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
      }

      .ant-card-body {
        padding: 16px;
        max-height: calc(100vh - 400px);
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 8px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }

        .ant-divider {
          margin: 16px 0;

          &.ant-divider-with-text-left {
            &::before {
              width: 5%;
            }
          }
        }

        .info-item {
          margin-bottom: 12px;

          .ant-typography {
            display: block;
            margin-bottom: 4px;
            font-size: 13px;
            color: rgba(0, 0, 0, 0.45);
          }

          .info-value {
            font-size: 14px;
            color: #1f2937;
            font-weight: 500;
            min-height: 22px;
            word-break: break-word;
          }
        }
      }

      .detail-card {
        margin-bottom: 16px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        background-color: #fff;
        border: 1px solid #e6e8eb;
        overflow: hidden;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .ant-card-body {
          padding: 16px;
          max-height: none;
          overflow-y: visible;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;

        .anticon {
          margin-right: 8px;
          color: #1890ff;
        }

        .price-id-tag {
          margin-left: auto;
          font-weight: normal;
          font-size: 12px;
        }
      }

      .price-tier {
        background-color: #f9fafb;
        border-radius: 6px;
        padding: 12px;
        border: 1px solid #eaedf0;
        height: 100%;
        margin-bottom: 16px;

        .tier-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px dashed #e5e7eb;

          .ant-badge {
            font-weight: 500;
          }
        }

        .tier-content {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .tier-item {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .ant-typography {
            margin-bottom: 0;
            font-size: 13px;
          }

          .tier-value {
            font-weight: 500;
            color: #1f2937;
          }
        }
      }
    }
  }
}

@media print {
  body * {
    visibility: hidden;
  }

  .quotation-editor,
  .quotation-editor *,
  .manual-quotation-editor-modal,
  .manual-quotation-editor-modal * {
    visibility: visible;
  }

  .quotation-editor {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    background-color: white !important;
    padding: 20px !important;

    .editor-header {
      margin-bottom: 30px !important;

      .header-left {
        h3.ant-typography {
          color: #000 !important;
          font-size: 24px !important;
        }
      }

      .header-right {
        display: none !important;
      }
    }

    .quotation-card {
      box-shadow: none !important;
      border: 1px solid #eee !important;
      margin-bottom: 0 !important;

      .ant-card-head {
        background: none !important;
      }

      .quotation-textarea {
        border: none !important;
        box-shadow: none !important;
        background: none !important;
        font-size: 14px !important;
        padding: 0 !important;
        color: #000 !important;
      }
    }

    .quotation-total-price-banner {
      background: none !important;
      border: 2px solid #000 !important;
      box-shadow: none !important;
      margin: 20px 0 !important;

      &::before {
        display: none !important;
      }

      .total-price-container {
        .total-price-label {
          color: #000 !important;

          .price-icon {
            color: #000 !important;
          }
        }

        .total-price-value {
          color: #000 !important;
          text-shadow: none !important;

          .currency-symbol,
          .price-amount {
            color: #000 !important;
          }
        }
      }
    }
  }

  .manual-quotation-editor-modal {
    .quotation-total-price-banner {
      background: none !important;
      border: 2px solid #000 !important;
      box-shadow: none !important;
      margin: 20px 0 !important;

      &::before {
        display: none !important;
      }

      .total-price-container {
        .total-price-label {
          color: #000 !important;

          .price-icon {
            color: #000 !important;
          }
        }

        .total-price-value {
          color: #000 !important;
          text-shadow: none !important;

          .currency-symbol,
          .price-amount {
            color: #000 !important;
          }
        }
      }
    }
  }
}

.price-detail-modal {
  .ant-modal-content {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.06);
  }

  .ant-modal-header {
    padding: 16px 24px;
    background: linear-gradient(to right, #f0f7ff, #f9fcff);
    border-bottom: 1px solid #e6f0fa;
  }

  .ant-modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
    background-color: #f9fafc;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }

  .price-detail-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;

    .title-icon {
      margin-right: 8px;
      color: #1890ff;
      font-size: 18px;
    }

    .price-id-tag {
      margin-left: 12px;
      font-weight: normal;
      font-size: 12px;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }

  .price-detail-content {
    .detail-card {
      margin-bottom: 16px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      background-color: #fff;
      border: 1px solid #e6e8eb;
      overflow: hidden;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }

      .ant-card-body {
        padding: 16px;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      align-items: center;

      .anticon {
        margin-right: 8px;
        color: #1890ff;
      }
    }

    .info-item {
      margin-bottom: 8px;

      .ant-typography {
        display: block;
        margin-bottom: 4px;
        font-size: 13px;
      }

      .info-value {
        font-size: 14px;
        color: #1f2937;
        font-weight: 500;
        min-height: 22px;
        word-break: break-word;
      }
    }

    .json-list {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 4px;
        font-size: 13px;
      }
    }

    .price-tier {
      background-color: #f9fafb;
      border-radius: 6px;
      padding: 12px;
      border: 1px solid #eaedf0;
      height: 100%;

      .tier-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px dashed #e5e7eb;

        .ant-badge {
          font-weight: 500;
        }
      }

      .tier-content {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .tier-item {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .ant-typography {
          margin-bottom: 0;
          font-size: 13px;
        }

        .tier-value {
          font-weight: 500;
          color: #1f2937;
        }
      }
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@media (max-width: 768px) {
  .price-detail-modal {
    .ant-modal-body {
      padding: 16px;
    }

    .price-detail-content {
      .section-title {
        font-size: 15px;
      }

      .info-item {
        .info-value {
          font-size: 13px;
        }
      }
    }
  }

  .new-quotation-editor-modal {
    .ant-modal-body {
      height: 90vh;
    }

    .new-quotation-editor {
      .editor-header {
        padding: 16px 20px;

        .header-left h3 {
          font-size: 18px;
        }

        .header-right .ant-btn {
          height: 36px;
          padding: 0 16px;
        }
      }

      .total-price-banner {
        margin: 12px 20px;
        padding: 16px 24px;

        .price-display {
          .price-value {
            .currency {
              font-size: 16px;
            }

            .amount {
              font-size: 24px;
            }
          }

          .price-arrow {
            font-size: 20px;
          }
        }
      }

      .editor-content {
        padding: 0 20px 20px;

        .content-section {
          .ant-card .ant-card-body {
            padding: 16px;
          }
        }
      }
    }
  }
}

.price-card {
  .airline-tag-prominent {
    font-size: 16px !important;
    font-weight: 700 !important;
    padding: 6px 12px !important;
    border-radius: 6px !important;
    margin-bottom: 8px !important;

    .anticon {
      font-size: 16px !important;
      margin-right: 6px !important;
    }
  }

  @keyframes airline-glow {
    0% {
      box-shadow:
        0 4px 15px rgba(24, 144, 255, 0.4),
        0 0 20px rgba(24, 144, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
    100% {
      box-shadow:
        0 4px 15px rgba(24, 144, 255, 0.6),
        0 0 25px rgba(24, 144, 255, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
  }

  .unit-price-highlight {
    .price-currency {
      font-size: 16px;
      font-weight: 600;
      color: #1890ff;
    }

    .price-amount {
      font-size: 15px;
      font-weight: 700;
      color: #1890ff;
      margin-left: 2px;
    }
  }

  .total-price-highlight {
    color: #e53e3e;
    position: relative;

    .price-currency {
      font-size: 18px;
      font-weight: 700;
      color: #e53e3e;
    }

    .price-amount {
      font-size: 20px;
      font-weight: 800;
      color: #e53e3e;
      margin-left: 2px;
    }

    .price-unit {
      font-size: 14px;
      font-weight: 600;
      color: #e53e3e;
      margin-left: 4px;
    }
  }

  .price-secondary-info {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px dashed rgba(0, 0, 0, 0.06);
  }

  .price-tiers-wrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
    padding: 8px 0;

    .price-tiers-row {
      display: flex;
      gap: 8px;
      justify-content: flex-start;
      flex-wrap: wrap;

      &.single-row {
        margin-bottom: 0;
      }
    }

    .price-tier-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: rgba(240, 248, 255, 0.5);
      padding: 6px 8px;
      border-radius: 4px;
      border: 1px solid rgba(0, 120, 255, 0.05);
      min-width: 70px;
      flex: 0 0 auto;
      box-sizing: border-box;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(240, 248, 255, 0.8);
        border-color: rgba(0, 120, 255, 0.1);
      }

      .ant-badge {
        margin-bottom: 4px;
        width: 100%;
        text-align: center;

        .ant-badge-status-dot {
          width: 8px;
          height: 8px;
        }

        .ant-badge-status-text {
          font-size: 11px;
          font-weight: 500;
          margin-left: 6px;
        }
      }

      .price-tier-value {
        font-size: 11px;
        color: #666;
        line-height: 1.4;
        text-align: center;
        white-space: nowrap;
        width: 100%;

        .price-tier-unit {
          font-size: 10px;
          color: #999;
          margin-left: 2px;
        }
      }
    }
  }
}

.quotation-edit-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 0;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    position: sticky;
    top: 0;
    z-index: 100;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .back-button {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        border-radius: 6px;
        font-weight: 500;
        color: #64748b;
        transition: all 0.3s ease;

        &:hover {
          background: #f1f5f9;
          color: #3b82f6;
          transform: translateX(-2px);
        }

        .anticon {
          font-size: 14px;
        }
      }

      .header-info {
        .page-title {
          margin: 0;
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 20px;
          color: #1e293b;
          font-weight: 700;

          .anticon {
            color: #3b82f6;
            font-size: 22px;
          }
        }

        .price-id {
          color: #64748b;
          font-size: 12px;
          margin-top: 2px;
          display: block;
        }

        .header-meta {
          margin-top: 8px;

          .meta-items {
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
          }

          .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 6px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;

            &:hover {
              background: rgba(255, 255, 255, 0.95);
              border-color: rgba(59, 130, 246, 0.2);
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            }

            .ant-typography {
              margin: 0;
              font-size: 11px;
              color: #64748b;
              font-weight: 500;
              white-space: nowrap;
            }

            .ant-tag {
              margin: 0;
              font-size: 11px;
              font-weight: 600;
              border: none;
              border-radius: 4px;
              padding: 2px 6px;
              line-height: 1.4;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
              transition: all 0.3s ease;

              &:hover {
                transform: scale(1.05);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
              }

              &[color="blue"] {
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                color: white;
              }

              &[color="green"] {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
              }
            }
          }
        }
      }
    }

    .header-actions {
      .ant-btn {
        margin-left: 8px;
        border-radius: 6px;
        height: 32px;
        padding: 0 16px;
        font-weight: 500;
        font-size: 13px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        }

        &.ant-btn-primary {
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          border: none;

          &:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
          }
        }
      }
    }
  }

  .total-price-banner {
    margin: 16px 24px;
    padding: 16px 24px;
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: 2px solid #f59e0b;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(245, 158, 11, 0.15);
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(255, 255, 255, 0.1) 100%
      );
      pointer-events: none;
    }

    .price-display {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      z-index: 1;

      .price-comparison-group {
        display: flex;
        align-items: center;

        .price-pair {
          display: flex;
          gap: 16px;
          align-items: center;

          .original-price,
          .calculated-price {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;

            .ant-typography {
              margin: 0;
              font-size: 12px;
              font-weight: 600;
              color: #92400e;
            }

            .ant-statistic {
              .ant-statistic-content {
                display: flex;
                align-items: baseline;
                gap: 4px;
                font-size: 20px !important;
              }
            }
          }

          .price-arrow {
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
          }
        }
      }

      .profit-display {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        border: 1px dashed rgba(146, 64, 14, 0.3);

        .profit-info {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;

          .ant-typography {
            margin: 0;
            font-size: 12px;
            font-weight: 600;
            color: #92400e;
          }

          .profit-value {
            display: flex;
            align-items: center;
            gap: 4px;

            .ant-statistic {
              .ant-statistic-content {
                display: flex;
                align-items: baseline;
                gap: 2px;
                font-size: 16px !important;
              }
            }
          }
        }
      }
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 25px rgba(245, 158, 11, 0.2);
      transition: all 0.3s ease;
    }
  }

  .page-content {
    padding: 0 24px 24px;
    display: flex;
    min-height: calc(100vh - 160px);

    .ant-row {
      width: 100%;
      min-height: 100%;
    }

    .ant-col {
      display: flex;
      flex-direction: column;
    }

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      border: 1px solid #e2e8f0;
      transition: all 0.3s ease;
      flex: 1;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        border-color: #cbd5e1;
      }

      .ant-card-head {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-bottom: 1px solid #e2e8f0;
        border-radius: 8px 8px 0 0;
        min-height: auto;

        .ant-card-head-title {
          padding: 12px 0;
          font-size: 14px;
          font-weight: 600;
          color: #1e293b;

          .card-title {
            display: flex;
            align-items: center;
            gap: 6px;

            .anticon {
              color: #3b82f6;
              font-size: 16px;
            }
          }
        }
      }

      .ant-card-body {
        display: flex;
        flex-direction: column;
        padding: 12px;
      }
    }

    .quotation-content-card {
      height: 100%;
      min-height: 500px;

      .ant-form {
        height: 100%;
        display: flex;
        flex-direction: column;
        flex: 1;
      }

      .ant-form-item {
        flex: 1;
        margin-bottom: 0;
        display: flex;
        flex-direction: column;
      }

      .quotation-textarea {
        font-family: "JetBrains Mono", "Fira Code", "Courier New", monospace;
        font-size: 13px;
        line-height: 1.5;
        border-radius: 6px;
        border: 1px solid #d1d5db;
        background: #f9fafb;
        color: #374151;
        letter-spacing: 0.2px;
        transition: all 0.3s ease;
        min-height: 400px;

        &:focus {
          background: #ffffff;
          border-color: #3b82f6;
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
          outline: none;
        }

        &:hover {
          border-color: #6b7280;
        }
      }
    }

    .ant-space {
      gap: 12px !important;

      .ant-space-item {
        width: 100%;
      }
    }

    .price-adjustment-card {
      .ant-form-item {
        margin-bottom: 12px;

        .ant-form-item-label {
          padding-bottom: 2px;

          label {
            color: #374151;
            font-weight: 500;
            font-size: 12px;
          }
        }

        .ant-input-number {
          width: 100%;
          border-radius: 4px;
          border-color: #d1d5db;
          transition: all 0.3s;

          &:hover {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
          }

          &:focus-within {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
          }

          .ant-input-number-input {
            height: 28px;
            line-height: 28px;
            padding: 0 8px;
            font-size: 12px;
          }

          .ant-input-number-handler-wrap {
            width: 20px;
          }
        }
      }

      .ant-divider {
        margin: 12px 0 8px;
        border-color: #e2e8f0;

        .ant-divider-inner-text {
          font-weight: 600;
          color: #1e293b;
          font-size: 12px;
        }
      }
    }

    .detail-info-card {
      .ant-card-body {
        padding: 12px;
      }

      .info-item {
        margin-bottom: 4px;

        .ant-typography {
          display: block;
          margin-bottom: 1px;
          font-size: 11px;
          color: #64748b;
          font-weight: 500;
        }

        > div {
          font-size: 12px;
          color: #1e293b;
          font-weight: 500;
          min-height: 18px;
          word-break: break-word;

          .ant-tag {
            font-size: 11px;
            padding: 1px 6px;
            margin-bottom: 1px;
          }
        }
      }

      .price-tier {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 6px;
        padding: 8px;
        border: 1px solid #e2e8f0;
        height: 100%;
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
          border-color: #cbd5e1;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .tier-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 6px;
          flex-wrap: wrap;
          gap: 2px;
        }

        .tier-content {
          .tier-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2px;

            &:last-child {
              margin-bottom: 0;
            }

            .tier-value {
              font-weight: 600;
              color: #1e293b;
              font-size: 11px;
            }
          }
        }
      }

      .charge-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 8px;
        background: #f8fafc;
        border-radius: 4px;
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;

        &:hover {
          background: #f1f5f9;
          border-color: #cbd5e1;
        }
      }
    }

    .price-adjustment-card {
      .ant-form-item {
        margin-bottom: 16px;

        .ant-form-item-label {
          padding-bottom: 4px;

          label {
            color: #374151;
            font-weight: 500;
            font-size: 13px;
          }
        }

        .ant-input-number {
          width: 100%;
          border-radius: 6px;
          border-color: #d1d5db;
          transition: all 0.3s;

          &:hover {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
          }

          &:focus-within {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }

          .ant-input-number-input {
            height: 32px;
            line-height: 32px;
            padding: 0 11px;
          }
        }
      }

      .ant-divider {
        margin: 20px 0 16px;
        border-color: #e2e8f0;

        .ant-divider-inner-text {
          font-weight: 600;
          color: #1e293b;
          font-size: 14px;
        }
      }
    }

    .detail-info-card {
      .info-item {
        margin-bottom: 8px;

        .ant-typography {
          display: block;
          margin-bottom: 2px;
          font-size: 12px;
          color: #64748b;
          font-weight: 500;
        }

        > div {
          font-size: 13px;
          color: #1e293b;
          font-weight: 500;
          min-height: 20px;
          word-break: break-word;

          .ant-tag {
            font-size: 12px;
            padding: 2px 8px;
            margin-bottom: 1px;
          }
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .quotation-edit-page {
    .page-content {
      .ant-col:first-child {
        width: 100%;
        margin-bottom: 24px;
      }

      .ant-col:last-child {
        width: 100%;
      }
    }
  }
}

@media (max-width: 768px) {
  .quotation-edit-page {
    .page-header {
      padding: 12px 16px;
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;

      .header-left {
        width: 100%;

        .page-title {
          font-size: 18px;
        }
      }

      .header-actions {
        width: 100%;
        display: flex;
        justify-content: flex-end;

        .ant-btn {
          height: 32px;
          padding: 0 12px;
          margin-left: 6px;
          font-size: 12px;
        }
      }
    }

    .total-price-banner {
      margin: 12px 16px;
      padding: 12px 16px;

      .price-display {
        flex-direction: column;
        gap: 12px;

        .price-arrow {
          font-size: 20px;
          transform: rotate(90deg);
        }
      }
    }

    .page-content {
      padding: 0 16px 16px;
    }
  }
}

.flight-selection-bar {
  position: relative;

  .flight-bar-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 16px;
    background: linear-gradient(
      135deg,
      rgba(240, 248, 255, 0.8) 0%,
      rgba(230, 240, 255, 0.6) 100%
    );
    border: 1px solid rgba(0, 120, 255, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.4) 0%,
        rgba(240, 248, 255, 0.2) 100%
      );
      pointer-events: none;
    }

    &:hover {
      background: linear-gradient(
        135deg,
        rgba(240, 248, 255, 0.9) 0%,
        rgba(230, 240, 255, 0.8) 100%
      );
      border-color: rgba(0, 120, 255, 0.2);
      box-shadow: 0 2px 8px rgba(0, 120, 255, 0.08);
    }
  }

  .flight-bar-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    font-weight: 600;
    color: #1f2937;
    position: relative;
    z-index: 1;
    flex-shrink: 0;

    .flight-bar-icon {
      color: #1890ff;
      font-size: 14px;
    }
  }

  .flight-dates-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    align-items: center;
    position: relative;
    z-index: 1;

    .flight-date-tag {
      display: inline-block;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(248, 250, 252, 0.8) 100%
      );
      color: #4b5563;
      padding: 4px 10px;
      border-radius: 14px;
      font-size: 11px;
      font-weight: 500;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid rgba(0, 120, 255, 0.1);
      user-select: none;
      position: relative;
      overflow: hidden;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.6) 0%,
          rgba(240, 248, 255, 0.3) 100%
        );
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
      }

      &:hover {
        background: linear-gradient(
          135deg,
          rgba(24, 144, 255, 0.1) 0%,
          rgba(64, 169, 255, 0.05) 100%
        );
        color: #1890ff;
        border-color: rgba(24, 144, 255, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);

        &::before {
          opacity: 1;
        }
      }

      &.selected {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: #667eea;
        font-weight: 600;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        transform: translateY(-1px);

        &::before {
          background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 0.2) 0%,
            rgba(255, 255, 255, 0.1) 100%
          );
          opacity: 1;
        }

        &:hover {
          background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }
      }
    }
  }
}
