import { get, post, del } from "../../utils/request";

export const getInquiryByCondition = (data: any) => {
  return post("/getInquiryByCondition", data);
};

// 获取部门下的用户列表
export const getUsersByDepartment = (data: any) => {
  return post("/getUserByCondition", data);
};

export const addInquiry = (data: any) => {
  return post("/addInquiry", data);
};

export const updateInquiry = (data: any) => {
  return post("/updateInquiry", data);
};

export const delInquiry = (data: any) => {
  return del("/delInquiry", data);
};
export const getAllPort = () => {
  return get("/getAllPort");
};

export const getAllShipmentPlace = () => {
  return get("/getAllShipmentPlace");
};

export const getAllSpecialitems = () => {
  return get("/getAllSpecialitems");
};

export const getAllPackageType = () => {
  return get("/getAllPackageType");
};
