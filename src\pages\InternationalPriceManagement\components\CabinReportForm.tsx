import React, { useState } from "react";
import {
  Form,
  DatePicker,
  InputNumber,
  Switch,
  Button,
  Row,
  Col,
  Modal,
} from "antd";
import {
  PlusOutlined,
  MinusCircleOutlined,
  CalendarOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import AiCabinExtractor from "@/components/AiCabinExtractor";
import "./index.less";

interface CabinReportFormProps {
  form: any;
  onAiExtractSuccess?: (extractedData: any[]) => void;
  showAiExtractor?: boolean;
  showTitle?: boolean;
  isTransfer?: boolean;
  originSchedules?: string | string[];
}

const CabinReportForm: React.FC<CabinReportFormProps> = ({
  form,
  onAiExtractSuccess,
  showAiExtractor = true,
  showTitle = true,
  isTransfer = false,
  originSchedules = "",
}) => {
  const { t } = useTranslation();
  const [dateModalVisible, setDateModalVisible] = useState(false);
  const [selectedDates, setSelectedDates] = useState<dayjs.Dayjs[]>([]);

  // 解析班次频率字符串，返回星期数组
  const parseScheduleString = (scheduleStr: string | string[]): number[] => {
    if (Array.isArray(scheduleStr)) {
      return scheduleStr
        .map(Number)
        .filter((n) => !isNaN(n))
        .sort((a, b) => a - b);
    }

    if (typeof scheduleStr === "string" && scheduleStr.startsWith("D")) {
      return scheduleStr
        .slice(1)
        .split("")
        .map(Number)
        .filter((n) => !isNaN(n))
        .sort((a, b) => a - b);
    }

    return [];
  };

  // 获取指定日期的星期几（1-7，1代表周一）
  const getWeekday = (date: dayjs.Dayjs): number => {
    const weekday = date.day();
    return weekday === 0 ? 7 : weekday; // 将周日的0转换为7
  };

  //根据班次频率推算缺少的日期
  const calculateMissingDates = (
    extractedDates: any[],
    scheduleStr: string | string[],
    isTransfer: boolean
  ): any[] => {
    if (isTransfer) {
      // 中转航班不进行日期推算
      return extractedDates;
    }

    const scheduleWeekdays = parseScheduleString(scheduleStr);
    if (scheduleWeekdays.length === 0) {
      return extractedDates;
    }

    // 获取提取到的日期对应的星期几
    const extractedWeekdays = extractedDates
      .filter((item) => item.date)
      .map((item) => {
        const date = dayjs(item.date);
        return getWeekday(date);
      });

    // 找出缺少的星期几
    const missingWeekdays = scheduleWeekdays.filter(
      (weekday) => !extractedWeekdays.includes(weekday)
    );

    if (missingWeekdays.length === 0) {
      return extractedDates;
    }

    // 找到最晚的提取日期
    const latestDate = extractedDates
      .filter((item) => item.date)
      .map((item) => dayjs(item.date))
      .sort((a, b) => b.valueOf() - a.valueOf())[0];

    if (!latestDate) {
      return extractedDates;
    }

    // 为缺少的星期几推算日期
    const additionalDates: any[] = [];
    missingWeekdays.forEach((weekday) => {
      let currentDate = latestDate.add(1, "day");
      let attempts = 0;
      const maxAttempts = 30; // 最多尝试30天，避免无限循环

      while (attempts < maxAttempts) {
        if (getWeekday(currentDate) === weekday) {
          // 找到匹配的星期几，创建新的舱报记录
          const newCabinReport = {
            date: currentDate,
            transferdate: undefined,
            weightlimit: 99999,
            volumelimit: 99999,
            lowerdensity: 0,
            upperdensity: 99999,
            onlyparts: false,
            singleinquiry: false,
            pricechanges: [],
            specialprice: [],
          };
          additionalDates.push(newCabinReport);
          break;
        }
        currentDate = currentDate.add(1, "day");
        attempts++;
      }
    });

    return [...extractedDates, ...additionalDates];
  };

  // 处理AI提取舱报信息成功的回调
  const handleAiExtractSuccess = (extractedData: any[]) => {
    // 根据班次频率推算缺少的日期
    const updatedCabinReport = calculateMissingDates(
      extractedData,
      originSchedules,
      isTransfer
    );

    // 按照日期从小到大排序
    updatedCabinReport.sort((a, b) => {
      // 如果任一项没有日期，将其排到最后
      if (!a.date && !b.date) return 0;
      if (!a.date) return 1;
      if (!b.date) return -1;

      const dateA = dayjs(a.date);
      const dateB = dayjs(b.date);

      // 如果日期无效，将其排到最后
      if (!dateA.isValid() && !dateB.isValid()) return 0;
      if (!dateA.isValid()) return 1;
      if (!dateB.isValid()) return -1;

      return dateA.isBefore(dateB) ? -1 : dateA.isAfter(dateB) ? 1 : 0;
    });

    form.setFieldsValue({
      cabinreport: updatedCabinReport,
    });

    onAiExtractSuccess?.(updatedCabinReport);
  };
  const handleDateConfirm = (add: any) => {
    selectedDates.forEach((date) => {
      add({ date });
    });
    setDateModalVisible(false);
    setSelectedDates([]);
  };

  return (
    <div className="cabin-report-section">
      {showTitle && (
        <div className="cabin-report-header-section">
          <h2 className="card-title">
            {t("internationalPriceManagement.actionModal.sections.cabinReport")}
          </h2>
          {showAiExtractor && (
            <AiCabinExtractor
              onExtractSuccess={handleAiExtractSuccess}
              buttonSize="small"
            />
          )}
        </div>
      )}
      <div className="sub-section">
        <Form.List name="cabinreport">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <div key={key} className="cabin-report-item">
                  <div className="cabin-report-header">
                    <div className="cabin-report-title">
                      <CalendarOutlined />
                      <span>
                        {t(
                          "internationalPriceManagement.actionModal.fields.cabinReportItem"
                        )}{" "}
                        {name + 1}
                      </span>
                    </div>
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => remove(name)}
                      size="small"
                    >
                      {t(
                        "internationalPriceManagement.actionModal.buttons.delete"
                      )}
                    </Button>
                  </div>
                  {/* 基本信息 */}
                  <Row gutter={[8, 0]} className="cabin-basic-info">
                    <Col flex="auto">
                      <Form.Item
                        {...restField}
                        name={[name, "date"]}
                        label={t(
                          "internationalPriceManagement.actionModal.fields.cabinDate"
                        )}
                        rules={[
                          {
                            required: true,
                            message: t(
                              "internationalPriceManagement.actionModal.validation.selectCabinDate"
                            ),
                          },
                        ]}
                      >
                        <DatePicker
                          className="input-default"
                          style={{ width: "100%" }}
                        />
                      </Form.Item>
                    </Col>
                    {isTransfer && (
                      <Col flex="auto">
                        <Form.Item
                          {...restField}
                          name={[name, "transferdate"]}
                          label={t(
                            "internationalPriceManagement.actionModal.fields.transferDate"
                          )}
                          // rules={[
                          //   {
                          //     required: true,
                          //     message: t(
                          //       "internationalPriceManagement.actionModal.validation.selectTransferDate"
                          //     ),
                          //   },
                          // ]}
                        >
                          <DatePicker
                            className="input-default"
                            style={{ width: "100%" }}
                          />
                        </Form.Item>
                      </Col>
                    )}
                    <Col flex="auto">
                      <Form.Item
                        {...restField}
                        name={[name, "weightlimit"]}
                        label={t(
                          "internationalPriceManagement.actionModal.fields.weightLimit"
                        )}
                        initialValue={99999}
                      >
                        <InputNumber
                          className="input-default"
                          min={0}
                          placeholder={t(
                            "internationalPriceManagement.actionModal.placeholders.inputWeightLimit"
                          )}
                        />
                      </Form.Item>
                    </Col>
                    <Col flex="auto">
                      <Form.Item
                        {...restField}
                        name={[name, "volumelimit"]}
                        label={t(
                          "internationalPriceManagement.actionModal.fields.volumeLimit"
                        )}
                        initialValue={99999}
                      >
                        <InputNumber
                          className="input-default"
                          min={0}
                          placeholder={t(
                            "internationalPriceManagement.actionModal.placeholders.inputVolumeLimit"
                          )}
                        />
                      </Form.Item>
                    </Col>
                    <Col flex="auto">
                      <Form.Item
                        {...restField}
                        name={[name, "lowerdensity"]}
                        label={t(
                          "internationalPriceManagement.actionModal.fields.lowerDensity"
                        )}
                        initialValue={0}
                      >
                        <InputNumber
                          className="input-default"
                          min={0}
                          placeholder={t(
                            "internationalPriceManagement.actionModal.placeholders.inputLowerDensity"
                          )}
                        />
                      </Form.Item>
                    </Col>
                    <Col flex="auto">
                      <Form.Item
                        {...restField}
                        name={[name, "upperdensity"]}
                        label={t(
                          "internationalPriceManagement.actionModal.fields.upperDensity"
                        )}
                        initialValue={99999}
                      >
                        <InputNumber
                          className="input-default"
                          min={0}
                          placeholder={t(
                            "internationalPriceManagement.actionModal.placeholders.inputUpperDensity"
                          )}
                        />
                      </Form.Item>
                    </Col>
                    <Col flex="auto">
                      <Form.Item
                        {...restField}
                        name={[name, "onlyparts"]}
                        label={t(
                          "internationalPriceManagement.actionModal.fields.onlyParts"
                        )}
                        valuePropName="checked"
                        initialValue={false}
                      >
                        <Switch />
                      </Form.Item>
                    </Col>
                    <Col flex="auto">
                      <Form.Item
                        {...restField}
                        name={[name, "singleinquiry"]}
                        label={t(
                          "internationalPriceManagement.actionModal.fields.singleInquiry"
                        )}
                        valuePropName="checked"
                        initialValue={false}
                      >
                        <Switch />
                      </Form.Item>
                    </Col>
                  </Row>

                  {/* 价格变化与特价找货 */}
                  <Row gutter={[10, 0]} className="cabin-price-sections">
                    <Col span={12}>
                      <div className="cabin-sub-section">
                        <h4 className="cabin-sub-title">
                          {t(
                            "internationalPriceManagement.actionModal.sections.priceChanges"
                          )}
                        </h4>
                        <Form.List name={[name, "pricechanges"]}>
                          {(
                            priceFields,
                            { add: addPrice, remove: removePrice }
                          ) => (
                            <>
                              {priceFields.map(
                                ({
                                  key: priceKey,
                                  name: priceName,
                                  ...priceRestField
                                }) => (
                                  <div
                                    key={priceKey}
                                    className="dynamic-row cabin-price-row"
                                  >
                                    <Form.Item
                                      {...priceRestField}
                                      name={[priceName, "leftdensity"]}
                                      className="row-item"
                                      label={t(
                                        "internationalPriceManagement.actionModal.fields.leftDensity"
                                      )}
                                      rules={[
                                        {
                                          required: true,
                                          message: t(
                                            "internationalPriceManagement.actionModal.validation.inputLeftDensity"
                                          ),
                                        },
                                      ]}
                                    >
                                      <InputNumber
                                        className="input-default"
                                        min={0}
                                        placeholder={t(
                                          "internationalPriceManagement.actionModal.placeholders.inputLeftDensity"
                                        )}
                                      />
                                    </Form.Item>
                                    <Form.Item
                                      {...priceRestField}
                                      name={[priceName, "rightdensity"]}
                                      className="row-item"
                                      label={t(
                                        "internationalPriceManagement.actionModal.fields.rightDensity"
                                      )}
                                      rules={[
                                        {
                                          required: true,
                                          message: t(
                                            "internationalPriceManagement.actionModal.validation.inputRightDensity"
                                          ),
                                        },
                                      ]}
                                    >
                                      <InputNumber
                                        className="input-default"
                                        min={0}
                                        placeholder={t(
                                          "internationalPriceManagement.actionModal.placeholders.inputRightDensity"
                                        )}
                                      />
                                    </Form.Item>
                                    <Form.Item
                                      {...priceRestField}
                                      name={[priceName, "pchanges"]}
                                      className="row-item"
                                      label={t(
                                        "internationalPriceManagement.actionModal.fields.priceChange"
                                      )}
                                      rules={[
                                        {
                                          required: true,
                                          message: t(
                                            "internationalPriceManagement.actionModal.validation.inputPriceChange"
                                          ),
                                        },
                                      ]}
                                    >
                                      <InputNumber
                                        className="input-default"
                                        placeholder={t(
                                          "internationalPriceManagement.actionModal.placeholders.inputPriceChange"
                                        )}
                                      />
                                    </Form.Item>
                                    <MinusCircleOutlined
                                      className="icon-remove"
                                      onClick={() => removePrice(priceName)}
                                    />
                                  </div>
                                )
                              )}
                              <Button
                                type="dashed"
                                onClick={() => addPrice()}
                                block
                                icon={<PlusOutlined />}
                                className="btn-dashed"
                                size="small"
                              >
                                {t(
                                  "internationalPriceManagement.actionModal.buttons.addPriceChange"
                                )}
                              </Button>
                            </>
                          )}
                        </Form.List>
                      </div>
                    </Col>
                    <Col span={12}>
                      <div className="cabin-sub-section">
                        <h4 className="cabin-sub-title">
                          {t(
                            "internationalPriceManagement.actionModal.sections.specialPrice"
                          )}
                        </h4>
                        <Form.List name={[name, "specialprice"]}>
                          {(
                            specialFields,
                            { add: addSpecial, remove: removeSpecial }
                          ) => (
                            <>
                              {specialFields.map(
                                ({
                                  key: specialKey,
                                  name: specialName,
                                  ...specialRestField
                                }) => (
                                  <div
                                    key={specialKey}
                                    className="dynamic-row cabin-price-row"
                                  >
                                    <Form.Item
                                      {...specialRestField}
                                      name={[specialName, "densitylowerlimit"]}
                                      className="row-item"
                                      label={t(
                                        "internationalPriceManagement.actionModal.fields.densityLowerLimit"
                                      )}
                                      rules={[
                                        {
                                          required: true,
                                          message: t(
                                            "internationalPriceManagement.actionModal.validation.inputDensityLowerLimit"
                                          ),
                                        },
                                      ]}
                                    >
                                      <InputNumber
                                        className="input-default"
                                        min={0}
                                        placeholder={t(
                                          "internationalPriceManagement.actionModal.placeholders.inputDensityLowerLimit"
                                        )}
                                      />
                                    </Form.Item>
                                    <Form.Item
                                      {...specialRestField}
                                      name={[specialName, "densityupperlimit"]}
                                      className="row-item"
                                      label={t(
                                        "internationalPriceManagement.actionModal.fields.densityUpperLimit"
                                      )}
                                      rules={[
                                        {
                                          required: true,
                                          message: t(
                                            "internationalPriceManagement.actionModal.validation.inputDensityUpperLimit"
                                          ),
                                        },
                                      ]}
                                    >
                                      <InputNumber
                                        className="input-default"
                                        min={0}
                                        placeholder={t(
                                          "internationalPriceManagement.actionModal.placeholders.inputDensityUpperLimit"
                                        )}
                                      />
                                    </Form.Item>
                                    <Form.Item
                                      {...specialRestField}
                                      name={[specialName, "sprice"]}
                                      className="row-item"
                                      label={t(
                                        "internationalPriceManagement.actionModal.fields.specialPrice"
                                      )}
                                      rules={[
                                        {
                                          required: true,
                                          message: t(
                                            "internationalPriceManagement.actionModal.validation.inputSpecialPrice"
                                          ),
                                        },
                                      ]}
                                    >
                                      <InputNumber
                                        className="input-default"
                                        min={0}
                                        precision={3}
                                        placeholder={t(
                                          "internationalPriceManagement.actionModal.placeholders.inputSpecialPrice"
                                        )}
                                      />
                                    </Form.Item>
                                    <MinusCircleOutlined
                                      className="icon-remove"
                                      onClick={() => removeSpecial(specialName)}
                                    />
                                  </div>
                                )
                              )}
                              <Button
                                type="dashed"
                                onClick={() => addSpecial()}
                                block
                                icon={<PlusOutlined />}
                                className="btn-dashed"
                                size="small"
                              >
                                {t(
                                  "internationalPriceManagement.actionModal.buttons.addSpecialPrice"
                                )}
                              </Button>
                            </>
                          )}
                        </Form.List>
                      </div>
                    </Col>
                  </Row>
                </div>
              ))}
              <Button
                type="dashed"
                onClick={() => setDateModalVisible(true)}
                block
                icon={<PlusOutlined />}
                className="btn-dashed add-cabin-report"
              >
                {t(
                  "internationalPriceManagement.actionModal.buttons.addCabinReport"
                )}
              </Button>
              <Modal
                title={t(
                  "internationalPriceManagement.actionModal.placeholders.selectDates"
                )}
                open={dateModalVisible}
                onCancel={() => {
                  setDateModalVisible(false);
                  setSelectedDates([]);
                }}
                onOk={() => handleDateConfirm(add)}
                okText={t("common.confirm")}
                cancelText={t("common.cancel")}
              >
                <DatePicker
                  multiple
                  value={selectedDates}
                  onChange={(dates) => setSelectedDates(dates || [])}
                  style={{ width: "100%" }}
                  placeholder={t(
                    "internationalPriceManagement.actionModal.placeholders.selectDates"
                  )}
                />
              </Modal>
            </>
          )}
        </Form.List>
      </div>
    </div>
  );
};

export default CabinReportForm;
