import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Row, Col, Typography, Tag, Divider } from "antd";
import {
  FileTextOutlined,
  PlusOutlined,
  RocketOutlined,
  EnvironmentOutlined,
  DollarCircleOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";

const { Text } = Typography;

interface QuotationDetailModalProps {
  visible: boolean;
  selectedQuotation: any | null;
  onClose: () => void;
  onSave: () => void;
  onGenerate: () => void;
}

const QuotationDetailModal: React.FC<QuotationDetailModalProps> = ({
  visible,
  selectedQuotation,
  onClose,
  onSave,
  onGenerate,
}) => {
  if (!selectedQuotation) return null;

  // 适配不同数据格式的字段
  const getQuotationId = () => {
    return selectedQuotation.priceid || selectedQuotation.id || "N/A";
  };

  const getAirlineName = () => {
    return (
      selectedQuotation.airlinename ||
      selectedQuotation.airlines ||
      selectedQuotation.airline ||
      "未知航司"
    );
  };

  const getOriginPort = () => {
    return selectedQuotation.originport || selectedQuotation.originPort || "";
  };

  const getDestinationPort = () => {
    return (
      selectedQuotation.unloadingport || selectedQuotation.destinationPort || ""
    );
  };

  const getPrice = () => {
    if (selectedQuotation.totalprice) {
      // 原始数据，转换为USD显示
      return (selectedQuotation.totalprice / 7.2).toFixed(2);
    }
    return selectedQuotation.price || 0;
  };

  const getCurrency = () => {
    return selectedQuotation.currency || "USD";
  };

  const getTransitTime = () => {
    if (selectedQuotation.validity) {
      return `${selectedQuotation.validity} ${selectedQuotation.validity > 1 ? "days" : "day"}`;
    }
    return selectedQuotation.transitTime || "未知";
  };

  const getValidUntil = () => {
    if (selectedQuotation.effectivetime) {
      return new Date(selectedQuotation.effectivetime).toLocaleDateString();
    }
    return selectedQuotation.validUntil || "长期有效";
  };

  const getSchedule = () => {
    if (selectedQuotation.originschedules) {
      return [selectedQuotation.originschedules];
    }
    return selectedQuotation.departureSchedule || [];
  };

  return (
    <Modal
      title={
        <div className="price-detail-title">
          <span className="title-icon">
            <FileTextOutlined />
          </span>
          报价单详情
          {selectedQuotation && (
            <Tag color="blue" className="price-id-tag">
              ID: QT-{new Date().getFullYear()}-
              {String(getQuotationId()).padStart(4, "0")}
            </Tag>
          )}
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={900}
      footer={[
        <Button key="back" onClick={onClose}>
          关闭
        </Button>,
        <Button key="save" icon={<PlusOutlined />} onClick={onSave}>
          保存到我的报价
        </Button>,
        <Button
          key="generate"
          type="primary"
          icon={<FileTextOutlined />}
          onClick={onGenerate}
        >
          生成正式报价单
        </Button>,
      ]}
      className="price-detail-modal"
      destroyOnClose
      centered
    >
      <div className="price-detail-content">
        <Card className="detail-card">
          <div className="section-title">
            <RocketOutlined /> 航线信息
          </div>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">航空公司</Text>
                <div className="info-value">
                  <Tag color="blue">{getAirlineName()}</Tag>
                </div>
              </div>
            </Col>
            {selectedQuotation.serviceLevel && (
              <Col xs={24} sm={12} md={8}>
                <div className="info-item">
                  <Text type="secondary">服务等级</Text>
                  <div className="info-value">
                    <Tag color="cyan">{selectedQuotation.serviceLevel}</Tag>
                  </div>
                </div>
              </Col>
            )}
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">起始港</Text>
                <div className="info-value">
                  <Tag icon={<EnvironmentOutlined />} color="green">
                    {getOriginPort()}
                  </Tag>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">目的港</Text>
                <div className="info-value">
                  <Tag icon={<EnvironmentOutlined />} color="orange">
                    {getDestinationPort()}
                  </Tag>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">运输时间</Text>
                <div className="info-value">{getTransitTime()}</div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">有效期至</Text>
                <div className="info-value">{getValidUntil()}</div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">班期</Text>
                <div className="info-value">
                  <div className="schedule-tags">
                    {getSchedule().map((day: any, index: number) => (
                      <Tag key={index} color="blue">
                        {day}
                      </Tag>
                    ))}
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </Card>

        <Card className="detail-card">
          <div className="section-title">
            <DollarCircleOutlined /> 价格信息
          </div>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <div className="info-item">
                <Text type="secondary">基础价格</Text>
                <div className="info-value highlight">
                  <span className="price-currency">{getCurrency()}</span>
                  <span className="price-amount">{getPrice()}</span>
                </div>
              </div>
            </Col>
          </Row>

          <Divider orientation="left">附加费用</Divider>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <div className="info-item">
                <Text type="secondary">燃油附加费</Text>
                <div className="info-value">
                  <Tag color="red">根据航空公司当月公布费率</Tag>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <div className="info-item">
                <Text type="secondary">安检费</Text>
                <div className="info-value">
                  <Tag color="red">$0.15/kg，最低$15.00</Tag>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <div className="info-item">
                <Text type="secondary">操作费</Text>
                <div className="info-value">
                  <Tag color="red">$50.00/票</Tag>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <div className="info-item">
                <Text type="secondary">文件处理费</Text>
                <div className="info-value">
                  <Tag color="red">$25.00/票</Tag>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <div className="info-item">
                <Text type="secondary">海关申报费</Text>
                <div className="info-value">
                  <Tag color="red">$35.00/票 (最多6项，之后每项$10.00)</Tag>
                </div>
              </div>
            </Col>
          </Row>
        </Card>

        <Card className="detail-card">
          <div className="section-title">
            <InfoCircleOutlined /> 附加说明
          </div>
          <div className="terms-note">
            注: 以上报价不包含目的地产生的费用，如进口关税、目的港操作费等。
          </div>
        </Card>
      </div>
    </Modal>
  );
};

export default QuotationDetailModal;
