import { Switch } from "antd";

// 定义 props 类型
interface LayoutSwitchProps {
  onSwitch: (checked: boolean) => void; // 假设 onSwitch 是一个函数
  checked: boolean; // 控制开关的状态
}

const LayoutSwitch: React.FC<LayoutSwitchProps> = ({ onSwitch, checked }) => {
  return (
    <Switch
      checkedChildren="侧边栏"
      unCheckedChildren="顶部导航"
      checked={checked}
      onChange={onSwitch}
    />
  );
};

export default LayoutSwitch;
