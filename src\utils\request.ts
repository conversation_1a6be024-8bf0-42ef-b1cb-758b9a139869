import axios, { AxiosInstance } from "axios";
import { message } from "antd";

// 定义通用API响应类型
export interface BaseApiResponse {
  resultCode: number;
  message?: string;
  data?: any;
  totalNum?: number; // 添加分页相关字段
  total?: number; // 兼容不同的分页字段名
}

// 主服务的拦截器配置
const setupMainInterceptors = (instance: AxiosInstance) => {
  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem("token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        config.headers.token = token;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response) => {
      if (response?.data?.resultCode === 405) {
        localStorage.removeItem("token");
        message.error("登录已过期，请重新登录");
        setTimeout(() => {
          window.location.href = "/login";
        }, 1000);
      }
      return response;
    },
    (error) => {
      console.error("Request Error:", error);
      return Promise.reject(error);
    }
  );
};

// DeepSeek API的拦截器配置
const setupSecondaryInterceptors = (instance: AxiosInstance) => {
  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      const deepseekApiKey =
        localStorage.getItem("deepseekApiKey") ||
        "***********************************";
      if (deepseekApiKey) {
        config.headers.Authorization = `Bearer ${deepseekApiKey}`;
        config.headers["Content-Type"] = "application/json";
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response) => {
      // DeepSeek API的错误处理
      if (response?.data?.error) {
        const errorType = response.data.error.type;
        if (
          errorType === "invalid_api_key" ||
          errorType === "authentication_error"
        ) {
          message.error("DeepSeek API Key无效或已过期，请检查API Key");
        }
      }
      return response;
    },
    (error) => {
      console.error("DeepSeek API Request Error:", error);
      if (error.response?.status === 401) {
        message.error("DeepSeek API认证失败，请检查API Key");
      }
      return Promise.reject(error);
    }
  );
};

// 主要后端服务实例（原有的）
const mainRequest = axios.create({
  baseURL: process.env.REACT_APP_BASE_URL,
  timeout: 100000,
});

// 第二个后端服务实例
const secondaryRequest = axios.create({
  baseURL: process.env.REACT_APP_SECONDARY_BASE_URL,
  timeout: 100000,
});

// 为两个实例分别设置不同的拦截器
setupMainInterceptors(mainRequest);
setupSecondaryInterceptors(secondaryRequest);

// 主要服务的请求方法
const get = <T = BaseApiResponse>(url: string, params = {}, options = {}) => {
  return mainRequest.get<T>(url, { params, ...options });
};

const post = <T = BaseApiResponse>(url: string, data = {}, options = {}) => {
  return mainRequest.post<T>(url, data, options);
};

const del = <T = BaseApiResponse>(url: string, params = {}, options = {}) => {
  return mainRequest.delete<T>(url, { params, ...options });
};

// 第二个服务的请求方法
const getSecondary = <T = BaseApiResponse>(
  url: string,
  params = {},
  options = {}
) => {
  return secondaryRequest.get<T>(url, { params, ...options });
};

const postSecondary = <T = BaseApiResponse>(
  url: string,
  data = {},
  options = {}
) => {
  return secondaryRequest.post<T>(url, data, options);
};

const delSecondary = <T = BaseApiResponse>(
  url: string,
  params = {},
  options = {}
) => {
  return secondaryRequest.delete<T>(url, { params, ...options });
};

// 导出所有方法
export {
  get,
  post,
  del,
  getSecondary,
  postSecondary,
  delSecondary,
  mainRequest,
  secondaryRequest,
};
