import React, { useState, useEffect } from "react";
import { Modal, Form, Input, Button, message, Row, Col, Spin } from "antd";
import { UserOutlined, MailOutlined, PhoneOutlined } from "@ant-design/icons";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { setUserInfo } from "@/store/slices/userSlice";
import "./index.less";
import { updateUser } from "./services";

interface ProfileModalProps {
  visible: boolean;
  onClose: () => void;
}

// 模拟API调用
// const updateUserProfile = async (userData: any) => {
//   // 这里应该是实际的API调用
//   // 为了演示，我们使用setTimeout模拟网络请求
//   return new Promise((resolve) => {
//     setTimeout(() => {
//       // 模拟成功响应
//       resolve({
//         resultCode: 200,
//         data: userData,
//         message: "个人信息更新成功",
//       });
//     }, 1000);
//   });
// };

const ProfileModal: React.FC<ProfileModalProps> = ({ visible, onClose }) => {
  const [form] = Form.useForm();
  const { user } = useAppSelector((state) => state.user);
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible && user) {
      // 重置表单并填充用户数据
      form.resetFields();
      form.setFieldsValue({
        email: user.email,
        fullname: user.fullname || "",
        telephone: user.telephone || "",
      });
    }
  }, [visible, user, form]);

  // 提交个人信息
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 准备更新的用户数据
      const updatedUserData = {
        ...user,
        ...values,
      };

      // 调用API更新用户信息
      const response: any = await updateUser(updatedUserData);

      if (response.data?.resultCode === 200) {
        // 更新Redux状态
        dispatch(
          setUserInfo({
            user: updatedUserData,
            token: localStorage.getItem("token"),
          })
        );

        message.success("个人信息更新成功");
        onClose();
      } else {
        message.error(response.message || "更新失败，请重试");
      }
    } catch (error) {
      console.error("表单验证或提交出错:", error);
      message.error("提交失败，请检查表单填写是否正确");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="个人信息管理"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
      destroyOnClose
      className="profile-modal"
    >
      <Spin spinning={loading}>
        <Form form={form} layout="vertical" className="profile-form">
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item label="邮箱" name="email">
                <Input
                  prefix={<MailOutlined />}
                  // disabled
                  placeholder="邮箱地址"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="姓名"
                name="fullname"
                // rules={[{ required: true, message: "请输入您的姓名" }]}
              >
                <Input prefix={<UserOutlined />} placeholder="请输入姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="电话"
                name="telephone"
                rules={[
                  // { required: true, message: "请输入您的电话号码" },
                  { pattern: /^1[3-9]\d{9}$/, message: "请输入有效的手机号码" },
                ]}
              >
                <Input
                  prefix={<PhoneOutlined />}
                  placeholder="请输入电话号码"
                />
              </Form.Item>
            </Col>
          </Row>

          <div className="form-actions">
            <div>
              <Button onClick={onClose} style={{ marginRight: 8 }}>
                取消
              </Button>
              <Button type="primary" onClick={handleSubmit}>
                保存
              </Button>
            </div>
          </div>
        </Form>
      </Spin>
    </Modal>
  );
};

export default ProfileModal;
